import React, { useEffect, useState } from 'react';
import './index.less';
import { Collapse, Drawer, Input, Spin, Card, Tooltip, Menu } from 'antd';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { ReportMasterItem, ReportItem } from '@/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { ReportEventConstant } from '@/constants';
import LeftMenuReportBase from '@/components/left-menu/report-base';
import {
  LeftOutlined,
  ReadOutlined,
  ScheduleOutlined,
  PartitionOutlined,
  HddOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  RightOutlined,
  DownOutlined,
  ArrowDownOutlined,
  ProfileOutlined,
  FolderOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { isEmptyValues } from '@uni/utils/src/utils';
import { v4 as uuidv4 } from 'uuid';
import cloneDeep from 'lodash/cloneDeep';
import { useUpdateEffect } from 'ahooks';

const { Panel } = Collapse;
const { Search } = Input;

interface ReportLeftMenuProps {
  appCode: string;
}

const ReportLeftMenu = (props: ReportLeftMenuProps) => {
  const [searchKeyword, setSearchKeyword] = useState('');

  const [masterItems, setMasterItems] = useState<ReportMasterItem[]>([]);

  const [reportOpenKeys, setReportOpenKeys] = useState([]);
  const [reportFolderMasterItems, setReportFolderMasterItems] = useState([]);

  const [reportBaseOpen, setReportBaseOpen] = useState(false);

  const [selectedReportMasterItem, setSelectedReportMasterItem] =
    useState<ReportMasterItem>();

  useEffect(() => {
    reportMastersReq();
  }, []);

  // 通知table
  useEffect(() => {
    if (selectedReportMasterItem) {
      console.log(
        'ReportEventConstant.REPORT_MASTER_ITEM_CLICK',
        selectedReportMasterItem,
      );
      Emitter.emit(
        ReportEventConstant.REPORT_MASTER_ITEM_CLICK,
        selectedReportMasterItem,
      );
    }
  }, [selectedReportMasterItem]);

  useUpdateEffect(() => {
    let reportFolderMasterItems = buildDirectoryWithItems();
    setReportFolderMasterItems(reportFolderMasterItems);
  }, [masterItems]);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_DETAIL_UPDATE_SUCCESS, (data) => {
      // reportSettingMasterId: props?.reportMasterItem?.Id,
      // appCode: props?.reportMasterItem?.AppCode,
      // remark: event.target.value
      if (data?.reportSettingMasterId) {
        let currentMasterItem = masterItems?.find(
          (item) => item?.Id === data?.reportSettingMasterId,
        );
        if (currentMasterItem) {
          currentMasterItem['Remark'] = data?.remark;
          setMasterItems(masterItems.slice());
        }

        if (selectedReportMasterItem?.Id === data?.reportSettingMasterId) {
          selectedReportMasterItem['Remark'] = data?.remark;
          setSelectedReportMasterItem(selectedReportMasterItem);
        }
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_DETAIL_UPDATE_SUCCESS);
    };
  }, [masterItems]);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_ITEM_CREATED, (data) => {
      let currentMasterItem = masterItems?.find(
        (item) => item.Id === data?.ReportSettingMasterId,
      );
      if (currentMasterItem) {
        currentMasterItem['children'] = [
          ...currentMasterItem['children'],
          data,
        ];

        setMasterItems(masterItems.slice());
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_ITEM_CREATED);
    };
  }, [masterItems]);

  // Api/Report/Report/GetRestrictedReportMasters
  const { loading: reportMastersLoading, run: reportMastersReq } = useRequest(
    () => {
      return uniCommonService('Api/Report/Report/GetRestrictedReportMasters', {
        params: {
          AppCode: props?.appCode,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportMasterItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setMasterItems(
            response?.data?.filter((item) => item?.IsHidden !== true),
          );
        } else {
          setMasterItems([]);
        }
      },
    },
  );

  const onSearch = (event: any) => {
    setSearchKeyword(event?.target?.value);
  };

  const sortReportFolderMasterItems = (items: any[]) => {
    return items?.sort((a, b) => {
      let aSort = a?.MenuSort === 0 ? 65535 : a?.MenuSort ?? 65535;
      let bSort = b?.MenuSort === 0 ? 65535 : b?.MenuSort ?? 65535;

      return aSort - bSort;
    });
  };

  const buildDirectoryWithItems = () => {
    let directoryItems = [];
    masterItems?.forEach((masterItem) => {
      masterItem['itemData'] = cloneDeep(masterItem);

      masterItem['IsLeaf'] = true;
      masterItem['label'] = masterItem?.Title;
      masterItem['key'] = masterItem?.Id;
      masterItem['icon'] = <FileTextOutlined />;

      if (!isEmptyValues(masterItem?.MenuDirectories)) {
        let parentMenus = masterItem?.MenuDirectories?.filter(
          (item) => !isEmptyValues(item),
        );
        let parentMenuItem = directoryItems?.find(
          (item) => item?.label === parentMenus?.at(0) && item?.IsLeaf !== true,
        );
        if (isEmptyValues(parentMenuItem)) {
          parentMenuItem = {
            key: uuidv4(),
            label: parentMenus?.at(0),
            IsLeaf: false,
            icon: <FolderOutlined />,
            children: [],
          };
          directoryItems.push(parentMenuItem);
        }
        // 重新给一个 一定能有 用于引用
        parentMenuItem = directoryItems?.find(
          (item) => item?.label === parentMenus?.at(0) && item?.IsLeaf !== true,
        );
        parentMenus?.slice(1)?.forEach((parentMenuLabel) => {
          let subParentMenuItem = parentMenuItem?.children?.find(
            (item) => item?.label === parentMenuLabel && item?.IsLeaf !== true,
          );
          if (isEmptyValues(subParentMenuItem)) {
            subParentMenuItem = {
              key: uuidv4(),
              label: parentMenuLabel,
              IsLeaf: false,
              icon: <FolderOutlined />,
              children: [],
            };
            parentMenuItem?.children.push(subParentMenuItem);
            parentMenuItem['MenuSort'] = Math.min(
              parentMenuItem?.children?.map((item) => item?.MenuSort ?? 0),
            );
          }
          parentMenuItem = parentMenuItem?.children?.find(
            (item) => item?.label === parentMenuLabel && item?.IsLeaf !== true,
          );
        });
        parentMenuItem?.children.push(masterItem);
        parentMenuItem['MenuSort'] = Math.min(
          parentMenuItem?.children?.map((item) => item?.MenuSort ?? 0),
        );
        parentMenuItem['children'] = sortReportFolderMasterItems(
          parentMenuItem?.children ?? [],
        );
      } else {
        directoryItems.push(masterItem);
      }
    });

    return directoryItems;
  };

  const renderReportFlatList = () => {
    return (
      <>
        {masterItems
          ?.filter(
            (item) =>
              item?.Title?.indexOf(searchKeyword) > -1 ||
              pinyinInitialSearch(item?.Title, searchKeyword),
          )
          ?.sort((a, b) => {
            let aSort = a?.MenuSort === 0 ? 65535 : a?.MenuSort ?? 65535;
            let bSort = b?.MenuSort === 0 ? 65535 : b?.MenuSort ?? 65535;

            return aSort - bSort;
          })
          .map((item: ReportMasterItem) => {
            return renderReportItem(item);
          })}
      </>
    );
  };

  const renderReportFolderList = () => {
    return (
      <Menu
        className={'report-menu-container'}
        mode="inline"
        theme={'light'}
        inlineIndent={18}
        openKeys={reportOpenKeys}
        onOpenChange={(keys) => {
          setReportOpenKeys(keys);
        }}
        selectedKeys={[selectedReportMasterItem?.Id]}
        onClick={({ item, key, keyPath, domEvent }) => {
          let reportData = item?.props?.itemData;
          if (
            reportData?.ReportMode?.toLowerCase()?.indexOf('readonly') === -1
          ) {
            setReportBaseOpen(true);
          }
          setSelectedReportMasterItem(reportData);
        }}
        items={sortReportFolderMasterItems(reportFolderMasterItems)}
      />
    );
  };

  const renderReportItem = (item: ReportMasterItem) => {
    return (
      <div
        className={`master-item ${
          item?.Id === selectedReportMasterItem?.Id &&
          item?.ReportMode?.toLowerCase()?.indexOf('readonly') !== -1
            ? 'item-container-select-v2'
            : ''
        }`}
        onClick={() => {
          if (item?.ReportMode?.toLowerCase()?.indexOf('readonly') === -1) {
            setReportBaseOpen(true);
          }
          setSelectedReportMasterItem(item);
        }}
      >
        <FileTextOutlined style={{ marginRight: 10 }} />
        <span className="title">
          {/* <span className="icon-left">
                        <ScheduleOutlined />
                      </span> */}
          {item?.Title}
        </span>
        {item?.ReportMode?.toLowerCase()?.indexOf('readonly') === -1 && (
          <span className="icon-right">
            <RightOutlined />
          </span>
        )}
      </div>
    );
  };

  return (
    <Card title="报表列表" className={'report-left-menu-bg'}>
      <div className={'report-left-menu-container'}>
        <Spin spinning={reportMastersLoading}>
          <Drawer
            placement="left"
            mask={false}
            open={reportBaseOpen}
            destroyOnClose={true}
            className={'report-base-drawer-container'}
            onClose={() => {
              setReportBaseOpen(false);
              Emitter.emit(ReportEventConstant.REPORT_DETAIL_VISIBLE, false);
              // Emitter.emit(ReportEventConstant.)
            }}
            title={
              <>
                <div className="master-item">
                  <span className="title">
                    {/* <span className="icon-left">
                      <ScheduleOutlined />
                    </span> */}
                    {selectedReportMasterItem?.Title}
                  </span>
                  <span className="icon-right">
                    <Tooltip title={'查看报表详情'}>
                      <ProfileOutlined
                        style={{ marginRight: 5 }}
                        onClick={() => {
                          // TODO 报表详情
                          Emitter.emit(
                            ReportEventConstant.REPORT_DETAIL_VISIBLE,
                            true,
                          );
                        }}
                      />
                    </Tooltip>
                    <LeftOutlined
                      onClick={() => {
                        setReportBaseOpen(false);
                        Emitter.emit(
                          ReportEventConstant.REPORT_DETAIL_VISIBLE,
                          false,
                        );
                      }}
                    />
                  </span>
                </div>
              </>
            }
            width={250}
            maskStyle={{
              background: 'transparent',
            }}
            closable={false}
            getContainer={false}
          >
            <LeftMenuReportBase masterItem={selectedReportMasterItem} />
          </Drawer>
          <Search
            className={'search-container'}
            placeholder="输入报表标题以搜索"
            onChange={onSearch}
            allowClear={true}
            enterButton
          />
          <div className={'left-menu-items-container'}>
            {!isEmptyValues(searchKeyword)
              ? renderReportFlatList()
              : renderReportFolderList()}
          </div>
        </Spin>
      </div>
    </Card>
  );
};
export default ReportLeftMenu;
