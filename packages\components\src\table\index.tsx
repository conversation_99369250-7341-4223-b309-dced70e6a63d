import { Button, Skeleton, Space, Table, TablePaginationConfig } from 'antd';
import React, {
  CSSProperties,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { ParamsType, ProProvider } from '@ant-design/pro-components';

import { ColumnType, FilterValue } from 'antd/lib/table/interface';
import './index.less';
import { ProTable, ProTableProps, ActionType } from '@ant-design/pro-table';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  processSearchFilter,
  SearchItemProcessor,
} from './processor/column/search-item';
import SorterItemProcessor, {
  processDefaultSortedInfo,
} from './processor/column/sorter-item';
import { v4 as uuidv4 } from 'uuid';
import {
  CheckOutlined,
  CloseOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import isEmpty from 'lodash/isEmpty';
import Toolbar from '@ant-design/pro-table/es/components/ToolBar';
import { processSummaryData } from './processor/data/summary-data';
import { exportExcel } from '@uni/utils/src/excel-export';
import cloneDeep from 'lodash/cloneDeep';
import {
  ValueTypeProcessor,
  valueTypeMap,
} from './processor/column/value-type';
import { DictionaryProcessor } from './processor/column/dictionary-processor';
import { FilterItemProcessor } from './processor/column/filter-item';
import { exportExcelDictionaryModuleProcessor } from './processor/data/export';
import isNil from 'lodash/isNil';
import isEqual from 'lodash/isEqual';
import ColumnItemMinWidthProcessor from './processor/column/column-min-width';
import { externalPaginationProcessor } from './pagination';
import { ColumnWidthDetectProcessor } from './processor/column/columns-width-detector';
import { useDeepCompareEffect } from 'ahooks';

const defaultOptions = {
  density: false,
  reload: false,
  setting: false,
  fullScreen: false,
};

const defaultPagination: TablePaginationConfig = {
  size: 'default',
  defaultPageSize: 10,
  pageSizeOptions: ['10', '20'],
  hideOnSinglePage: true,
};

interface ColumnItem<RecordType> extends ColumnType<RecordType> {
  data?: string;
  draggable?: boolean;
}

export interface UniTableProps<T, U = ParamsType> extends ProTableProps<T, U> {
  id: string;
  tableHeader?: React.ReactNode;
  tableHeaderTitle?: string;
  columns?: any[];
  dataItemType?: string;
  columnDefinitions?: object;
  dataSource?: any[];
  rowKey: string;
  scroll?: {
    x?: string | number;
    y?: string | number;
  };
  height?: number;
  // 请求方法
  columnsFetch?: () => Promise<any[]>;
  dataFetch?: () => Promise<any[]>;
  // dataProcessor
  dataItemProcessor?: (dataItem: any, index: number) => any;
  // columnProcessor
  columnItemProcessor?: (columnItem: any, index: number) => any;
  // 样式
  className?: string;
  style?: any;
  // drag
  onTableDataSourceOrderChange?: (tableData: any[]) => void;
  // toolbar
  extraToolBar?: ReactNode[];
  onCustomToolbarExportClick?: () => void;
  needExport?: boolean;
  // 是否需要总计行
  needSummary?: boolean;
  summaryDataIndex?: string;
  summaryExcludeKeys?: string[];
  summaryData?: any;
  // 导出
  exportName?: string;
  exportExcludeKeys?: string[];
  exportTableDataSourceProcessor?: (dataSource: any[]) => any[];
  // 是否能点击
  clickable?: boolean;
  // 用于高度计算
  parentId?: string;
  dictionaryData?: any;

  // 目前此字段 为非必填，若需要用于其他项目中请改为必填
  isBackPagination?: boolean;
  // 是否强制columns更新...对于那些title文字，visible等blahblah更新的，绕过tableColumns检查
  forceColumnsUpdate?: boolean;
  // 是否计算宽度
  widthCalculate?: boolean;
  // 是否在字典翻译完后计算宽度
  widthDetectAfterDictionary?: boolean;
}

const UniTable = ({
  id,
  tableHeader,
  tableHeaderTitle,
  columns,
  dataSource,
  dataItemType,
  columnDefinitions,
  columnsFetch,
  columnItemProcessor,
  dataFetch,
  dataItemProcessor,
  onChange,
  className,
  style,
  scroll,
  height,
  needSummary,
  summaryDataIndex,
  summaryExcludeKeys,
  summaryData,
  exportName,
  exportExcludeKeys,
  pagination,
  clickable,
  parentId,
  dictionaryData,
  isBackPagination,
  forceColumnsUpdate,
  widthCalculate = false,
  widthDetectAfterDictionary = false,
  ...restProps
}: UniTableProps<any>) => {
  const providerValues = useContext(ProProvider);

  const [tableColumns, setTableColumns] = useState([]);
  const [tableRenderColumns, setTableRenderColumns] = useState([]);
  const [tableDataSource, setTableDataSource] = useState([]);
  // Loading
  const [tableLoading, setTableLoading] = useState(false);

  // filters & sorters
  const [searchFilter, setSearchFilter] = useState<
    Array<{ searchText: string; searchedColumn: string }>
  >([]);

  // controlled FilterValue && sortedInfo
  const [filteredInfo, setFilteredInfo] = useState<
    Record<string, FilterValue | null>
  >({});
  const [sortedInfo, setSortedInfo] = useState(undefined);

  useEffect(() => {
    Emitter.on(EventConstant.TABLE_COLUMN_SORTER_EDIT, (sorterInfo) => {
      setSortedInfo(sorterInfo);
    });

    return () => {
      Emitter.off(EventConstant.TABLE_COLUMN_SORTER_EDIT);
    };
  }, []);

  // 初始化Event Listener
  // 部分依赖state的请监听state
  useEffect(() => {
    Emitter.on(
      EventConstant.TABLE_COLUMN_SEARCH_CONFIRM,
      onTableColumnSearchConfirm,
    );
    Emitter.on(
      EventConstant.TABLE_COLUMN_SEARCH_RESET,
      onTableColumnSearchReset,
    );

    return () => {
      Emitter.off(EventConstant.TABLE_COLUMN_SEARCH_CONFIRM);
      Emitter.off(EventConstant.TABLE_COLUMN_SEARCH_RESET);
    };
  }, [searchFilter]);

  useDeepCompareEffect(() => {
    let processors = [
      commonColumnProcessor,
      ColumnItemMinWidthProcessor,
      ValueTypeProcessor,
      SorterItemProcessor,
      FilterItemProcessor,
      SearchItemProcessor,
    ];
    processTableColumns(true, processors);
  }, [columns, columnsFetch]);

  useEffect(() => {
    processTableDataSource();
  }, [dataSource, dataFetch]);

  const onTableColumnSearchConfirm = (data) => {
    setSearchFilter(
      searchFilter.map((item) => {
        return item.searchedColumn === data.dataIndex
          ? {
              searchText: data.selectedKeys[0],
              searchedColumn: item.searchedColumn,
            }
          : item;
      }),
    );
  };

  const onTableColumnSearchReset = (data) => {
    setSearchFilter(
      searchFilter.map((item) => {
        return item.searchedColumn === data.dataIndex
          ? {
              searchText: '',
              searchedColumn: item.searchedColumn,
            }
          : item;
      }),
    );
  };

  // default column processor // 添加一个description转tooltip
  const commonColumnProcessor = ({ columnItem }) => {
    columnItem.hideInTable = !columnItem?.visible;
    if (
      !columnItem.render &&
      (!columnItem.valueType || columnItem?.valueType === 'text')
    ) {
      columnItem['normalRenderer'] = true;
      columnItem.render = (node, record, index) => {
        // module & moduleGroup
        let dataItem = record[columnItem.dataIndex];
        if (columnItem?.isExtraProperty === true) {
          dataItem = record?.['ExtraProperties']?.[columnItem.dataIndex];
        }
        if (columnItem?.dictionaryModule) {
          dataItem = dataItemWithColumnModuleProcessor(
            columnItem?.dictionaryModule,
            dataItem,
          );
        }
        return (
          <span>
            {valueNullOrUndefinedReturnDash(
              dataItem,
              // columnItem['x-type'] || columnItem['format'],
              // columnItem['x-scale'],
              columnItem['dataType'],
              columnItem['scale'],
            )}
          </span>
        );
      };
    }

    return {
      ...columnItem,
      tooltip: columnItem?.description,
    };
  };

  const commonDataSourceProcessor = (dataSourceItem) => {
    if (restProps?.rowKey === 'id' && !isEmptyValues(dataSourceItem?.id)) {
      return {
        id: uuidv4(),
        ...dataSourceItem,
      };
    }

    return dataSourceItem;
  };

  // 给出module moduleGroup 去 换 字段 换不到就是用原先的
  const dataItemWithColumnModuleProcessor = (module, dataItem) => {
    if (dictionaryData?.[module]) {
      let dictionaryItem = dictionaryData?.[module]?.find(
        (item) => item.Code === dataItem,
      );
      if (dictionaryItem) {
        return dictionaryItem?.Name || dataItem;
      }
    }

    return dataItem;
  };

  const processTableColumns = async (
    fetchColumn: boolean = false,
    processors: Function[],
  ) => {
    let processColumns = columns
      ? columns.slice()?.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0))
      : [];
    if (fetchColumn && columnsFetch) {
      processColumns = await columnsFetch();
    }
    // 当处理过columns 保持处理同一份columns
    if (
      !forceColumnsUpdate &&
      tableColumns &&
      tableColumns?.length > 0 &&
      tableColumns?.length === columns?.length &&
      // 严格意义上相等 包括顺序也要相等
      isEqual(tableColumns, columns)
    ) {
      processColumns = tableColumns?.slice();
    }
    // console.log('processColumns', processColumns);
    setSearchFilter(processSearchFilter(processColumns));

    let defaultSortedInfo = processDefaultSortedInfo(
      processColumns,
      isBackPagination ?? !isNil((pagination as any)?.total),
    );

    if (isEmptyValues(sortedInfo) && !isEmptyValues(defaultSortedInfo)) {
      if (isEmptyValues(global['tableParameters'])) {
        global['tableParameters'] = {};
      }
      global['tableParameters']['sorter'] = defaultSortedInfo;
    }

    console.log(
      'defaultSortedInfo',
      defaultSortedInfo,
      global['tableParameters'],
    );

    // TODO 处理columns
    // tableColumns = tableColumns.slice().map((item, index) => {
    for (let index = 0; index < processColumns.length; index++) {
      let item = processColumns?.at(index);

      // 存在children的时候是不用过process的
      if (item.children && item.children?.length > 0) {
        item.children = childItemProcessor(item, processors, defaultSortedInfo);
        continue;
      }

      item = itemProcess(item, processors, defaultSortedInfo);
      if (columnItemProcessor) {
        item = Object.assign({}, columnItemProcessor(item, index));
      }
      processColumns[index] = item;
    }

    // dictionary columns(跟外面的冲突了)
    // processColumns = DictionaryProcessor(
    //   processColumns,
    //   dictionaryData,
    //   widthDetectAfterDictionary,
    // )?.slice();

    // sort
    processColumns = processColumns?.sort(
      (a, b) => (a?.order ?? 0) - (b?.order ?? 0),
    );
    console.log('processColumns', processColumns);

    setTableColumns(processColumns?.slice());
  };

  const itemProcess = (
    item,
    processors: any[],
    defaultSortedInfo: any = {},
  ) => {
    let backendPagination =
      isBackPagination ?? !isNil((pagination as any)?.total);

    for (let processor of processors) {
      item = Object.assign(
        {},
        processor({
          // definitionProcessor
          columnItem: item,
          ...item,
          // backendPagination: false,
          backendPagination: backendPagination,
          // search props
          filteredInfo: filteredInfo,
          searchFilter: searchFilter,
          // sorter props
          sortedInfo: isEmptyValues(sortedInfo)
            ? defaultSortedInfo
            : sortedInfo,
          // datasource 用于filters
          dataSource: tableDataSource,
          // 是否计算宽度
          widthCalculate: widthCalculate,
          dictionaryData: dictionaryData,
        }),
      );
    }

    return item;
  };

  const childItemProcessor = (
    item: any,
    processors: any[],
    defaultSortedInfo: any = {},
  ) => {
    return item.children
      ?.map((childItem) => {
        if (childItem?.children && childItem.children?.length > 0) {
          childItem['children'] = childItemProcessor(
            childItem,
            processors,
            defaultSortedInfo,
          );
        } else {
          childItem = itemProcess(childItem, processors, defaultSortedInfo);
        }

        return childItem;
      })
      .slice();
  };

  const processTableDataSource = async () => {
    let tableDataSource = dataSource || [];
    if (dataFetch) {
      tableDataSource = await dataFetch();
    }
    // TODO 处理 dataSource
    tableDataSource = tableDataSource.slice().map((item, index) => {
      if (dataItemProcessor) {
        item = Object.assign({}, dataItemProcessor(item, index));
      }
      return commonDataSourceProcessor(item);
    });

    if (needSummary && summaryDataIndex) {
      tableDataSource = processSummaryData(
        tableDataSource,
        summaryDataIndex,
        summaryData,
        summaryExcludeKeys,
      ).slice();
    }

    setTableDataSource(tableDataSource);
  };

  useEffect(() => {
    let columns = tableColumns?.slice();

    if (columns && columns?.length > 0) {
      if (!isEmpty(sortedInfo) && columns.length > 0) {
        columns = columns
          .map((item) => {
            let processedItem = Object.assign({}, item);
            if (
              processedItem?.children &&
              processedItem?.children?.length > 0
            ) {
              processedItem.children = childItemProcessor(processedItem, [
                SorterItemProcessor,
              ]);
            }
            return SorterItemProcessor({
              columnItem: processedItem,
              sortedInfo: sortedInfo,
              // backendPagination: false,
              backendPagination:
                isBackPagination ?? !isNil((pagination as any)?.total),
              dictionaryData: dictionaryData,
            });
          })
          ?.slice();
      }
      if (!isEmpty(filteredInfo) && columns.length > 0) {
        let filterProcessors = [FilterItemProcessor, SearchItemProcessor];

        columns = columns.map((item) => {
          let processedItem = Object.assign({}, item);

          if (processedItem?.children && processedItem?.children?.length > 0) {
            processedItem.children = childItemProcessor(
              processedItem,
              filterProcessors,
            );
          } else {
            processedItem = itemProcess(processedItem, filterProcessors);
          }

          return processedItem;
        });
      }
      console.log('filteredInfo && sortedInfo', columns);

      setTableColumns(columns?.slice());
    }
  }, [filteredInfo, sortedInfo]);

  // 处理 翻译 && filterType = filter
  // filterType 用于 生成filters ，且 如果遇到有key为sort的则直接略过
  // 这边的处理是最后一步 因为这2个都需要dataSource做支撑
  useEffect(() => {
    setTimeout(() => {
      let columns = tableColumns.slice();
      // 这边先翻译 翻译是必做的
      if (
        dictionaryData &&
        tableColumns?.length > 0 &&
        tableDataSource?.length > 0
      ) {
        let dictionaryProceedColumns = DictionaryProcessor(
          tableColumns,
          dictionaryData,
          widthDetectAfterDictionary,
        )?.slice();
        // 这边判断要不要做widthDetect
        if (widthDetectAfterDictionary) {
          dictionaryProceedColumns = ColumnWidthDetectProcessor(
            dictionaryProceedColumns,
            tableDataSource,
            dictionaryData,
            isBackPagination,
          )?.slice();
        }
        columns = dictionaryProceedColumns?.slice();
      }
      // 然后再filterType = filter
      if (columns && columns?.length > 0 && tableDataSource?.length > 0) {
        let filterSortColumns = columns
          .map((item) => {
            let columnItem = Object.assign(
              {},
              FilterItemProcessor({
                columnItem: item,
                ...item,
                backendPagination:
                  isBackPagination ?? !isNil((pagination as any)?.total),
                // search props
                filteredInfo: filteredInfo,
                dataSource: tableDataSource,
                dictData: dictionaryData,
              }),
            );

            if (columnItem.children && columnItem.children?.length > 0) {
              columnItem.children = columnItem.children
                ?.map((childItem) => {
                  return FilterItemProcessor({
                    columnItem: childItem,
                    ...childItem,
                    backendPagination:
                      isBackPagination ?? !isNil((pagination as any)?.total),
                    // search props
                    filteredInfo: filteredInfo,
                    dataSource: tableDataSource,
                    dictData: dictionaryData,
                  });
                })
                .slice();
            }

            return columnItem;
          })
          ?.slice();

        if (!isEqual(columns, filterSortColumns)) {
          columns = filterSortColumns.slice();
        }
      }
      setTableRenderColumns(columns);
    }, 200);
  }, [
    dictionaryData,
    tableDataSource,
    tableColumns,
    widthDetectAfterDictionary,
    isBackPagination,
    // tableColumns,
  ]);

  const onToolbarExportClick = () => {
    const canExportColumns = tableColumns?.filter(
      (columnItem) =>
        columnItem.className?.indexOf('exportable') !== -1 &&
        columnItem.valueType !== 'option' &&
        columnItem.dataIndex !== 'operation',
    );
    if (!isEmpty(canExportColumns)) {
      exportExcel(
        canExportColumns.slice() as any[],
        exportExcelDictionaryModuleProcessor(
          canExportColumns,
          restProps.exportTableDataSourceProcessor
            ? restProps.exportTableDataSourceProcessor(
                cloneDeep(tableDataSource.slice()),
              )
            : cloneDeep(tableDataSource.slice()),
          dictionaryData,
        ),
        exportName,
        exportExcludeKeys,
      );
    }
  };

  const defaultToolbar = () => (
    <>
      {restProps?.needExport ? (
        <Button
          key="export"
          icon={<FileExcelOutlined />}
          onClick={() => {
            restProps?.onCustomToolbarExportClick
              ? restProps.onCustomToolbarExportClick()
              : onToolbarExportClick();
          }}
        >
          导出Excel
        </Button>
      ) : (
        <></>
      )}
    </>
  );

  const renderToolbar: React.ReactNode[] = [
    defaultToolbar(),
    ...(restProps?.extraToolBar || []),
  ];

  const parentHeight = parentId
    ? document.getElementById(parentId)?.offsetHeight
    : undefined;

  return (
    <Skeleton active loading={tableLoading}>
      {/*table toolbar*/}
      {(restProps?.needExport || restProps.extraToolBar?.length > 0) && (
        <div className={'toolbar-container'}>
          <div className={'tools-left'} />
          <Space
            className={`tools-right`}
            direction={'horizontal'}
            size={16}
            align={'center'}
          >
            {renderToolbar.map((item) => {
              return item;
            })}
          </Space>
        </div>
      )}

      {/*table header*/}
      {(tableHeader || tableHeaderTitle) && (
        <div className={'header-container'}>
          {tableHeader ? (
            tableHeader
          ) : (
            <span className={'label'}>{tableHeaderTitle}</span>
          )}
        </div>
      )}
      {/* <ProProvider.Provider
        value={{
          ...providerValues,
          valueTypeMap,
        }}
      > */}
      <ProTable
        id={id}
        loading={tableLoading}
        className={`uni-table ${className} ${clickable ? 'clickable' : ''}`}
        style={{
          ...(style || {}),
          ...(parentHeight
            ? { height: parentHeight, minHeight: parentHeight }
            : {}),
        }}
        columns={tableRenderColumns}
        dataSource={tableDataSource}
        onChange={(pagination, filters, sorter, extra) => {
          // FIXME： WARNING 当且仅当 存在数据的时候才能 排序 换页 这种操作 没数据 改了也没用
          if (tableDataSource?.length === 0) {
            return;
          }
          console.log('ProTable onChange', sorter);
          setFilteredInfo(filters);
          setSortedInfo(sorter);
          // 把sorter和filter挂在global上 用于下一次带有DtParam的请求 如果下一次不包含DtParam即丢弃
          if (isBackPagination ?? !isNil((pagination as any)?.total)) {
            global['tableParameters'] = {
              filters: filters,
              sorter: sorter,
            };
          }
          onChange && onChange(pagination, filters, sorter, extra);
        }}
        scroll={scroll}
        pagination={
          pagination !== false
            ? {
                ...defaultPagination,
                ...(pagination || {}),
                ...externalPaginationProcessor(),
              }
            : false
        }
        toolBarRender={false}
        {...restProps}
        search={false}
        options={{ ...defaultOptions, ...(restProps?.options || {}) }}
        // TODO components 可能要定制拖拽
        onRow={
          restProps?.onRow
            ? restProps?.onRow
            : (record, index) => {
                return {
                  onClick: (event) => {
                    if (clickable) {
                      Emitter.emit(EventConstant.TABLE_ROW_CLICK, {
                        record,
                        index,
                      });
                    }
                  },
                  // ...(restProps?.onRow || {}),
                };
              }
        }
      />
      {/* </ProProvider.Provider> */}
    </Skeleton>
  );
};

export default UniTable;

export { ActionType };
