import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { Emitter } from '@uni/utils/src/emitter';
import { UniDragEditTable } from '@uni/components/src/index';
import { DetailColumnSettingContentConstants } from '../constants/events';
import { columnSettingColumns } from '../utils/columns';
import { SelectedColumnTableProps } from '../types/interfaces';

export const Table = (props: SelectedColumnTableProps) => {
  const [form] = Form.useForm();
  const mainFormInstance = Form.useFormInstance();
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectedItemsOrder, setSelectedItemsOrder] = useState({});
  const nowOrder = useRef(1);

  useEffect(() => {
    form.resetFields();

    Emitter.on(DetailColumnSettingContentConstants.COLUMN_SELECT_RESET, () => {
      form.resetFields();
    });

    return () => {
      Emitter.off(DetailColumnSettingContentConstants.COLUMN_SELECT_RESET);
    };
  }, []);

  useEffect(() => {
    mainFormInstance.setFieldValue('selectedItems', selectedItems);
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_TOP,
      (data) => {
        if (data?.index) {
          let currentItem = selectedItems[data?.index];
          let currentSelectedItemOrders = selectedItems?.map(
            (item) => item?.order,
          );

          if (currentItem) {
            selectedItems?.splice(data?.index, 1);

            let currentSelectedItems = [currentItem, ...selectedItems]?.map(
              (item, index) => {
                item['order'] = currentSelectedItemOrders[index];
                return item;
              },
            );
            setSelectedItems(currentSelectedItems);
            updateSelectedItemOrder(currentSelectedItems);
          }
        }
      },
    );

    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_BOTTOM,
      (data) => {
        let currentItem = selectedItems[data?.index];
        let currentSelectedItemOrders = selectedItems?.map(
          (item) => item?.order,
        );

        if (currentItem) {
          selectedItems?.splice(data?.index, 1);

          let currentSelectedItems = [...selectedItems, currentItem]?.map(
            (item, index) => {
              item['order'] = currentSelectedItemOrders[index];
              return item;
            },
          );

          setSelectedItems(currentSelectedItems);
          updateSelectedItemOrder(currentSelectedItems);
        }
      },
    );

    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_DELETE,
      (data) => {
        let currentItem = selectedItems[data?.index];
        if (currentItem) {
          Emitter.emit(
            DetailColumnSettingContentConstants.COLUMN_DESELECT,
            currentItem,
          );
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        DetailColumnSettingContentConstants.COLUMN_SELECT,
        DetailColumnSettingContentConstants.COLUMN_SELECT_TOP,
        DetailColumnSettingContentConstants.COLUMN_SELECT_BOTTOM,
        DetailColumnSettingContentConstants.COLUMN_SELECT_DELETE,
      ]);
    };
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT,
      (selectItems) => {
        let items = _.cloneDeep(selectItems);

        items?.forEach((item) => {
          if (selectedItemsOrder[item?.name]) {
            item['order'] = selectedItemsOrder[item?.name];
          } else {
            item['order'] = nowOrder.current;
            nowOrder.current += 1;
          }
          item['textOverflowType'] = item?.textOverflowType ?? 'none';
        });

        setSelectedItems(
          items
            ?.slice()
            ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
            ?.slice(),
        );
        updateSelectedItemOrder(items);
      },
    );

    return () => {
      Emitter.offMultiple([DetailColumnSettingContentConstants.COLUMN_SELECT]);
    };
  }, [selectedItemsOrder]);

  const updateSelectedItemOrder = (items: any[]) => {
    let currentSelectedItemsOrder = {};
    items?.forEach((item) => {
      currentSelectedItemsOrder[item?.name] = item?.order;
    });
    setSelectedItemsOrder(currentSelectedItemsOrder);
  };

  return (
    <div className={'selected-table-container'}>
      <UniDragEditTable
        {...props}
        bordered={false}
        form={form}
        key={'column-setting-selected-table'}
        id={'column-setting-selected-table'}
        tableId={'column-setting-selected-table'}
        scroll={{
          y: 400,
        }}
        controlled
        pagination={false}
        className={`table-container`}
        dataSource={selectedItems}
        rowKey={'id'}
        onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
          let formInstance = mainFormInstance.getFieldValue('selectedItems');

          let currentItems = tableData?.map((d, i) => ({
            ...d,
            order: formInstance?.at(i).order,
            customTitle:
              formInstance?.find((v) => v.id === d.id)?.customTitle ??
              undefined,
          }));

          setSelectedItems(currentItems);
          updateSelectedItemOrder(currentItems);
        }}
        onValuesChange={(recordList) => {
          mainFormInstance.setFieldValue('selectedItems', recordList);
        }}
        columns={columnSettingColumns}
      />
    </div>
  );
};
