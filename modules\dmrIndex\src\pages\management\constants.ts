import { DmrCardOperation } from '@/utils/constants';

export const ManagementSummaries = [
  // RegisterStatusAllCnt
  // RegisterStatusRegisteredCnt
  // RegisterStatusReviewFailedCnt
  // RegisterStatusUnUpdatedCnt
  // UnlockCnt
  {
    label: '总数量',
    key: 'RegisterStatusAllCnt',
    status: '0',
  },
  {
    label: '审核错误数量',
    key: 'RegisterStatusReviewFailedCnt',
    status: '999',
  },
  {
    label: '未登记数量',
    key: 'RegisterStatusUnUpdatedCnt',
    status: '1',
  },
  {
    label: '已登记数量',
    key: 'RegisterStatusRegisteredCnt',
    status: '2',
  },
  {
    label: '未锁定数量',
    key: 'UnlockCnt',
    status: '0',
    isLocked: false,
  },
];

//    ○ 审核选中病案
//     ○ 审核全部病案
//     ○ 审核已登记病案
//     ○ 审核未登记病案
//     ○ 重审出错病案
//     ○ 审核未锁定病案

export const ManagementAppealActions = [
  {
    key: DmrCardOperation.APPEAL_CHECKED,
    label: '审核选中病案',
    appealType: 'CHECKED',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return selectedCardIds?.length === 0;
    },
  },
  {
    key: DmrCardOperation.APPEAL_ALL,
    label: '审核全部病案',
    appealType: 'All',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return summaryData?.['RegisterStatusAllCnt'] === 0;
    },
  },
  {
    key: DmrCardOperation.APPEAL_REGISTERED,
    label: '审核已登记病案',
    appealType: 'Registered',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return summaryData?.['RegisterStatusRegisteredCnt'] === 0;
    },
  },
  {
    key: DmrCardOperation.APPEAL_UNREGISTERED,
    label: '审核未登记病案',
    appealType: 'UnUpdated',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return summaryData?.['RegisterStatusUnUpdatedCnt'] === 0;
    },
  },
  {
    key: DmrCardOperation.REAPPEAL_FAILED,
    label: '重审出错病案',
    appealType: 'ReviewFailed',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return summaryData?.['RegisterStatusReviewFailedCnt'] === 0;
    },
  },
  {
    key: DmrCardOperation.APPEAL_NOLOCKED,
    label: '审核未锁定病案',
    appealType: 'Unlocked',
    disabled: (selectedCardIds: any[], summaryData?: any) => {
      return summaryData?.['UnlockCnt'] === 0;
    },
  },
];
