//HisId
// HospCode
// InDate
// InTimes
// OutDate
// PatName
// PatNo
// RegisterStatusName

// TODO 字典 & dataType

export const dmrPendingCardIdColumns = [
  {
    dataIndex: 'HisId',
    title: 'HisId',
    visible: true,
    align: 'center',
  },
  {
    dataIndex: 'PatNo',
    title: '病案号',
    visible: true,
    width: 100,
    align: 'center',
  },
  {
    dataIndex: 'PatName',
    title: '姓名',
    visible: true,
    width: 100,
    align: 'center',
  },
  {
    dataIndex: 'HospCode',
    title: '院区',
    width: 100,
    align: 'center',
    visible: true,
    dictionaryModule: 'Hospital',
    dictionaryModuleGroup: null,
  },
  {
    dataIndex: 'InTimes',
    title: '入院次数',
    visible: true,
    align: 'center',
    render: (record) => {
      return <span>第{record['InTimes']}次入院</span>;
    },
  },
  {
    dataIndex: 'OutDate',
    title: '出院时间',
    visible: true,
    width: 100,
    align: 'center',
    dataType: 'DateByDash',
  },
  {
    dataIndex: 'RegisterStatusName',
    title: '登记状态',
    visible: true,
    width: 80,
    align: 'center',
  },
];
