import { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import {
  <PERSON>ton,
  Card,
  Col,
  Modal,
  Popconfirm,
  Row,
  Space,
  Switch,
  TableProps,
  Tabs,
  Upload,
  message,
  Form,
} from 'antd';
import { useSafeState, useUpdateEffect } from 'ahooks';
import {
  IReducer,
  IModalState,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  InitModalState,
  InitEditableState,
  TableAction,
  modalReducer,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import {
  colChangePos,
  columnsHandler,
  editableFormItemHandler,
  isRespErr,
} from '@/utils/widgets';
import _ from 'lodash';
import { ExpandableConfig, SorterResult } from 'antd/lib/table/interface';
import { v4 as uuidv4 } from 'uuid';
import { Dispatch, useDispatch, useModel, useSelector, useRequest } from 'umi';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  ReportUploadMetaData,
  SwagReportingSettingMasterItems,
} from './interface';
import {
  ReportTypes,
  ReqActionType,
  SelfDefinedReportEventType,
} from './constants';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { SelfDefinedTableColumns } from './columns';
import { UniTable } from '@uni/components/src';
import CreateReportPage from './createReport';
import ModalForm from './components/stepForm';
import { MenuOutlined } from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import { uniCommonService } from '@uni/services/src';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import { ProFormUploadButton } from '@uni/components/src/pro-form';
import { PlusOutlined } from '@ant-design/icons';
import { commonBusinessDomain } from '@uni/services/src/commonService';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import ReportImportExistsModal from '@/pages/selfDefinedReport/components/importExists';
import './index.less';
import { isEmptyValues } from '@uni/utils/src/utils';

const extraAppCodes =
  (window as any).externalConfig?.['reportSys']?.extraAppCodes ?? [];

const SelfDefinedReport = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [reportSelectorForm] = Form.useForm();

  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<SwagReportingSettingMasterItems>, IReducer>
  >(tableReducer, {
    ...InitTableState,
    selectedKeys: [],
    selectedRecords: [],
    sorter: {},
    clkItem: null,
    // expand
    expandedRecords: [],
    expandedTypes: [],
  });

  const [ModalState, ModalDispatch] = useReducer<
    Reducer<
      IModalState<SwagReportingSettingMasterItems>,
      IReducer<IModalState<SwagReportingSettingMasterItems>>
    >
  >(modalReducer, InitModalState);

  const [editValueObj, setEditValueObj] = useSafeState({});

  const [activeKey, setActiveKey] = useSafeState('1');

  const [batchExportMode, setBatchExportMode] = useSafeState(false);

  // 前端分页
  const [frontPagination, setFrontPagination] = useSafeState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });

  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  // appcode候选项提取
  const [appcodeOpts, setAppcodeOpts] = useSafeState([]);

  // columns处理
  useEffect(() => {
    if (columnsList?.[ReqActionType.GetReportSettingMasters]) {
      TableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            SelfDefinedTableColumns,
            columnsList[ReqActionType.GetReportSettingMasters],
          ),
        },
      });
    }
  }, [columnsList, dictData]);

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return TableState.columns.length > 0
      ? columnsHandler(TableState.columns, {
          dataIndex: 'option',
          title: '操作',
          visible: true,
          width: 60,
          align: 'center',
          valueType: 'option',
          fixed: 'right',
          render: (text, record: SwagReportingSettingMasterItems) => (
            <Space size={10}>
              <IconBtn
                type="download"
                title={'导出配置'}
                onClick={() =>
                  downloadReq([record.ReportSettingMasterId], record.Title)
                }
              />
              <IconBtn
                type="edit"
                onClick={() => getDetailReq(record.ReportSettingMasterId)}
              />
              <IconBtn
                type="delete"
                openPop={true}
                popOnConfirm={async () => {
                  let res = await actionReq(
                    { ReportSettingMasterId: record.ReportSettingMasterId },
                    ReqActionType.DeleteReportSettingMaster,
                  );
                  if (!isRespErr(res)) {
                    message.success('删除成功');
                    tableReq();
                  }
                }}
              />
            </Space>
          ),
        })
      : [];
  }, [TableState.columns]);

  // 普通的tableReq
  const tableReq = async (sorter = TableState.sorter) => {
    // console.log('fetchParams: ', params);
    let res: any[] = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'ReportSys',
        requestParams: [
          {
            url: `Api/${ReqActionType.GetReportSettingMasters}`,
            method: 'GET',
            name: ReqActionType.GetReportSettingMasters,
          },
        ],
      },
    });
    if (!isRespErr(res?.at(0))) {
      TableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.at(0)?.data ?? [],
        },
      });

      // appcode opts
      setAppcodeOpts(
        res
          .at(0)
          ?.data?.map((d) => d.AppCode)
          .concat(extraAppCodes),
      );

      // sorter
      if (!_.isEqual(sorter, TableState.sorter)) {
        TableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }
    }
  };

  useEffect(() => {
    tableReq();
  }, []);

  const handleCreateClk = (item) => {
    console.log(item);
    ModalDispatch({
      type: ModalAction.change,
      payload: {
        ...ModalState,
        visible: true,
        state: item,
      },
    });
  };

  // get detail req
  const getDetailReq = async (id) => {
    if (!id) return;
    let res: any = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: ReqActionType.GetReport,
        requestParams: {
          url: `Api/${ReqActionType.GetReport}`,
          method: 'GET',
          params: {
            ReportSettingMasterId: id,
          },
        },
      },
    });
    if (!isRespErr(res)) {
      let { data } = res;
      console.log(data);
      setEditValueObj(data);
      ModalDispatch({
        type: ModalAction.change,
        payload: {
          ...ModalState,
          visible: true,
          state: ReportTypes.find((d) => d.name === data?.Master.ReportMode),
        },
      });
    }
  };
  // action req
  const actionReq = async (data, reqType, needUpsertMaster: any = {}) => {
    console.log(data, reqType, needUpsertMaster);
    if (!data || !reqType) return false;
    let res: any = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: reqType,
        requestParams: {
          url: `Api/${reqType}`,
          method: 'POST',
          data: {
            ...data,
          },
        },
      },
    });
    return res;
  };

  // 新增/编辑接口处理
  const upsertMasterData = async (data) => {
    console.log(data);
    return await actionReq(data, ReqActionType.UpsertReport);
  };

  // upsert / delete     col / args
  const columnsOrArgsData = async (data, url) => {
    console.log(data);
    return await actionReq(data, url);
  };

  useEffect(() => {
    console.log(columnsSolver);
  }, [columnsSolver]);

  // Api/Sys/ReportSys/Export
  // 下载附件
  const { loading: downloadLoading, run: downloadReq } = useRequest(
    (ids, title) => {
      return uniCommonService('Api/Sys/ReportSys/Export', {
        method: 'POST',
        data: { masterIds: ids },
      });
    },
    {
      manual: true,
      formatResult: (res) => {
        return res;
      },
      onSuccess: (res, params) => {
        if (res.code === 0) {
          downloadFile(params?.at(1), res?.response, UseDispostionEnum.nouse);
        } else {
          message.error(res.msg);
        }
      },
    },
  );

  const { loading: uploadLoading, run: uploadReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/ReportSys/Import', {
        method: 'POST',
        requestType: 'form',
        data,
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res.statusCode === 200) {
          return 'success';
        } else {
          return 'failed';
        }
      },
      onSuccess: (data) => {
        if (data === 'success') {
          message.success('导入成功!');
        }
      },
    },
  );

  const {
    loading: getReportSettingMetaDataLoading,
    run: getReportSettingMetaDataReq,
  } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/ReportSys/GetImportMetadata', {
        method: 'POST',
        requestType: 'form',
        data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<ReportUploadMetaData[]>) => {
        console.log('getReportSettingMetaDataReq', response);
        return response;
      },
    },
  );

  return (
    <Card title="自定义报表配置" id={'self-defined-report-container'}>
      <Tabs
        activeKey={activeKey}
        onTabClick={(key) => setActiveKey(key)}
        tabBarExtraContent={
          activeKey === '1' && (
            <Space>
              <Button
                onClick={(e) => {
                  setBatchExportMode(!batchExportMode);
                  if (batchExportMode === true) {
                    TableDispatch({
                      type: TableAction.selectionChange,
                      payload: {
                        selectedKeys: [],
                        selectedRecords: [],
                      },
                    });
                  }
                }}
              >
                {batchExportMode ? '退出' : ''}批量导出
              </Button>
              <Upload
                fileList={[]}
                accept=".zip"
                customRequest={async ({ file }) => {
                  const formData = new FormData();
                  formData.append('File', file);

                  // 追加 GetMetaData
                  let response: RespVO<ReportUploadMetaData[]> =
                    await getReportSettingMetaDataReq(formData);
                  console.log('response', response);

                  let hasExistsMasters = false;
                  for (let masterItem of response.data) {
                    if (masterItem?.IsMasterExisted === true) {
                      hasExistsMasters = true;
                      break;
                    }
                  }

                  if (hasExistsMasters) {
                    Modal.confirm({
                      className: 'exist-modal-container',
                      title: '',
                      width: 900,
                      destroyOnClose: true,
                      getContainer: () =>
                        document.getElementById(
                          'self-defined-report-container',
                        ),
                      content: (
                        <ReportImportExistsModal
                          form={reportSelectorForm}
                          reportMetaData={response?.data}
                        />
                      ),
                      okText: '确认',
                      cancelText: '取消',
                      onOk: async () => {
                        let values = reportSelectorForm?.getFieldsValue();
                        let masterIds = values?.['selectedMasterIds'];
                        if (!isEmptyValues(masterIds)) {
                          formData.append('ReportSettingMasterIds', masterIds);
                          let res = await uploadReq(formData);

                          if (res === 'success') {
                            // todo
                            tableReq();
                          }
                        }
                      },
                    } as any);
                  } else {
                    formData.append('ReportSettingMasterIds', [
                      response?.data?.at(0)?.Id,
                    ]);
                    let res = await uploadReq(formData);

                    if (res === 'success') {
                      // todo
                      tableReq();
                    }
                  }
                }}
              >
                <Button loading={uploadLoading}>上传</Button>
              </Upload>

              <Button
                onClick={(e) => {
                  tableReq();
                }}
              >
                数据重载
              </Button>
            </Space>
          )
        }
        items={[
          {
            key: '1',
            label: '编辑',
            children: (
              <UniTable
                id="self-defined-report"
                rowKey="ReportSettingMasterId" // 特殊key，用于rowSelection
                // showSorterTooltip={false}
                loading={
                  loadings['ReportSys'] ||
                  loadings[ReqActionType.GetReport] ||
                  downloadLoading ||
                  false
                }
                dictionaryData={dictData}
                forceColumnsUpdate
                // columns={SelfDefinedTableColumns}
                tableAlertOptionRender={() => {
                  return (
                    <Space size={16}>
                      <a
                        onClick={() =>
                          downloadReq(
                            TableState.selectedRecords?.map(
                              (d) => d?.ReportSettingMasterId,
                            ),
                            '批量',
                          )
                        }
                      >
                        批量下载导出
                      </a>
                      <a
                        onClick={() => {
                          TableDispatch({
                            type: TableAction.selectionChange,
                            payload: {
                              selectedKeys: [],
                              selectedRecords: [],
                            },
                          });
                        }}
                      >
                        取消选择
                      </a>
                    </Space>
                  );
                }}
                rowSelection={
                  batchExportMode
                    ? {
                        selectedRowKeys: TableState.selectedKeys,
                        onChange: (selectedRowKeys, selectedRows) => {
                          TableDispatch({
                            type: TableAction.selectionChange,
                            payload: {
                              selectedKeys: selectedRowKeys,
                              selectedRecords: selectedRows,
                            },
                          });
                        },
                      }
                    : undefined
                }
                columns={columnsSolver} //  columnsSolver
                dataSource={TableState.data}
                scroll={{ x: 'max-content' }}
                onChange={frontTableOnChange}
              />
            ),
          },
          {
            key: '2',
            label: '新建',
            children: <CreateReportPage girdClkCb={handleCreateClk} />,
          },
        ]}
      />
      <ModalForm
        modalState={ModalState}
        closeModal={(type = null, cb = undefined) => {
          ModalDispatch({
            type: ModalAction.init,
          });
          setEditValueObj({});
          console.log(type);
          tableReq();

          // if (type) {
          // }
          if (cb) {
            cb();
          }
        }}
        appcodeOpts={appcodeOpts}
        editValueObj={editValueObj}
        dictData={dictData}
        masterAction={upsertMasterData}
        colOrArgsAction={columnsOrArgsData}
        preserve={false}
      />
    </Card>
  );
};

export default SelfDefinedReport;
