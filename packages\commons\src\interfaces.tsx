/**
 * 页面基本的筛选项
 */
export interface BasePageProps {
  dateRange?: string[];
  regionCodes?: any[];
  hospTypes?: any[];
  hospClasses?: any[];
  hospCodes?: any[];
  cliDepts?: any[];
}

export interface RespVO<T> {
  code?: number;
  requestUrl?: string;
  statusCode?: number;
  message?: string;
  data?: T;

  errors?: any;
  response?: any;
  errorBody?: any;

  // 后端给出的辅助服务statusCode
  extraResponseStatus?: number;
  extraResponseErrMsg?: string;
  cancelled?: boolean;
}

export interface DictionaryItems {
  [name: string]: DictionaryItem[];
}

export interface DictionaryItem {
  id?: string;
  Code?: string;
  Name?: string;
  Degree?: string;
  NamePyAll?: string;
  NamePy?: string;
  NameWb?: string;
  NameCustom?: string;
  Remark?: string;
  IsDefault?: boolean;
}

// C: column interface
// D: Data Interface
export interface TableResp<C, D> {
  columns: C[];
  data: D[];
  draw: number;
  error: any;
  recordsFiltered: number;
  recordsTotal: number;
  extraProperties?: any;
}

export interface TableCardResp<C, D> extends TableResp<C, D> {
  RegisterStatusAllCnt?: number;
  RegisterStatusUnUpdatedCnt?: number;
  RegisterStatusRegisteredCnt?: number;
  RegisterStatusReviewFailedCnt?: number;
  UnlockCnt?: number;
}

export interface TableColumns {
  Columns?: ColumnItem[];
  DictColumns?: any;
  ApiRedirectionColumns?: any;
  ApiRedirection?: any;
}

export interface ColumnItem {
  aggregable?: boolean;
  className?: string;
  data?: string;
  columnType?: string;
  dataType?: string;
  dictionaryModule?: string;
  dictionaryModuleGroup?: string;
  groupName?: string[];
  name?: string;
  orderMode?: any;
  orderPriority?: number;
  orderable?: boolean;
  responsivePriority?: number;
  scale?: any;
  shortTitle?: number;
  shortTitleDescription?: string;
  title?: string;
  visible?: boolean;

  directories?: string[];
  directorySort?: number;

  order?: number;
  originTitle?: string;
  title2?: string;

  columnSequence?: number;

  customTitle?: string;

  width?: number;

  pivot?: string;

  isExtraProperty?: boolean;
  extraProperties?: any;
}

export interface ShortcutItem {
  key: string;
  callback?: (keyboardEvent: KeyboardEvent, hotkeysEvent?: any) => void;
  enabled?: boolean;
  description?: string;

  onlyForHint?: boolean;

  type?: string;
  options?: any;
}

export interface HierarchyItem {
  Id?: string;
  HospCode?: string;
  Code?: string;
  Name?: string;
  HierarchyType?: string;
  IsPerson?: boolean;
  ParentId?: string;

  DeptType?: string;
  Edate?: string;
  HierarchyId?: number;
  IsCare?: boolean;
  IsIpt?: boolean;
  IsMedLab?: boolean;
  IsMr?: boolean;
  IsObs?: boolean;
  IsOtp?: boolean;
  IsValid?: boolean;
  MajorPerfDept?: string;
  NameCustom?: string;
  NamePy?: string;
  NamePyAll?: string;
  NameWb?: string;
  Sdate?: string;
  Sort1?: number;
  Sort2?: number;
  Sort3?: number;
  Sort4?: number;
}

export interface PreferencesItem {
  [key: string]: any;
}

export interface UserInfo {
  ExpireTimeRange?: number;
  ForceChangePassword?: boolean;
  Name?: string;
  Preferences?: PreferencesItem;
  UserId?: string;
  UserName?: string;

  HospCodes?: string[];

  Roles?: string[];

  CliDepts?: string[];
}
