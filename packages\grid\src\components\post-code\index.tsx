import { Input } from 'antd';
import { numberInputRestrictKeyDown } from '../../utils';

interface PostCodeProps {
  form?: any;
  value?: any;
  onChange?: (value) => void;
  codeLength?: number;
  [name: string]: any;
}

const PostCode = (props: PostCodeProps) => {
  return (
    <Input
      {...props}
      bordered={false}
      style={{ padding: '4px 4px' }}
      value={props?.value ?? ''}
      min={0}
      type={'text'}
      onKeyDown={(event) => {
        numberInputRestrictKeyDown(event);
      }}
      contentEditable={true}
      autoComplete={'new-password'}
      onChange={(event) => {
        props?.onChange &&
          props?.onChange(event.target.value?.slice(0, props?.codeLength ?? 6));
      }}
    />
  );
};

export default PostCode;
