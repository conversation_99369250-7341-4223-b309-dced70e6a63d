@import '~@uni/commons/src/style/variables.less';

//left container
.dmr-search-container {
  border-top-left-radius: @basic-border-radius;
  border-bottom-left-radius: @basic-border-radius;
  background: @menu-bg-color;

  .ant-tabs-nav {
    padding: 0px 0px;

    .ant-tabs-nav-wrap {
      display: block;
      width: 100%;
    }

    .ant-tabs-tab {
      font-size: 0.95rem;
      padding: 7px 0;
    }
  }

  .ant-tabs {
    height: 100%;

    .ant-tabs-content {
      height: 100%;
    }
  }

  .dmr-search-content {
    padding: 0 10px 10px;
  }

  .ant-drawer-content-wrapper {
    height: 100%;
    width: 400px !important;
  }

  .search-items-container {
    // display: flex;
    // flex-direction: column;
    // align-items: flex-start;
    margin-bottom: 15px;

    .ant-row {
      margin-bottom: 10px;
      width: 100%;
    }

    .search-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;

      .search-item-content-container,
      .selector-container {
        width: 260px;
        max-width: 260px;
      }

      .select-container:last-child {
      }

      label:not(.ant-radio-button-wrapper) {
        color: #7d87b3;
        white-space: nowrap;
        min-width: 70px;
        width: 70px;
      }

      .select-container {
        display: flex;
        flex: 1;
      }

      .suffix {
        margin-left: 10px;
      }

      .ant-radio-group.ant-radio-group-outline {
        .ant-radio-button-wrapper-checked {
          color: @blue-color;
          border-color: @blue-color;
        }
        .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked):hover {
          color: @blue-color;
        }
      }
    }

    .search-btn {
      width: 100px;
      height: 25px;
    }
  }

  .dmr-search-table {
    .ant-card-head {
      font-size: 1rem;
      .ant-card-head-title {
        padding: 5px 0;
      }
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 5px;
    }
    .ant-pagination {
      margin: 8px 0px 0px 0px;
    }

    .ant-table-row {
      cursor: pointer;
    }

    .row-selected {
      // box-shadow: 0px 0px 5px 0px rgba(156, 156, 156, 30%);
      background-color: #f3f5fe;
    }

    .ant-table-body {
      height: 420px;
    }
  }

  .dmr-cards-summary-container {
    margin: 12px 0px;
  }

  .dmr-cards-summary-item-container {
    text-align: center;
    padding: 5px 10px;
    line-height: 20px;
    border-radius: 10px;
    background: @tag-bg-color;
    border: 1px solid @border-color;
    cursor: pointer;

    .label {
      color: rgba(0, 0, 0, 0.5);
      font-size: 12px;
    }

    .value {
      font-size: 18px;
      color: @text-color;
    }
  }

  .card-selected {
    background-color: #7495f7 !important;
    box-shadow: 0 0 5px 0 #7495f7 !important;
    color: #ffffff !important;
    border: 1px solid #7495f7 !important;
    border-radius: 4px;

    span {
      color: #ffffff !important;
    }
  }

  .title-badge-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}
