import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  UniDmrDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { icdeColumns, pathologyIcdeColumns } from '@/pages/dmr/columns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import { generateUniqueNumberId } from '@uni/utils/src/utils';

interface PathologyIcdeDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;
}

interface PathologyIcdeItem {
  PathologyIcdeId?: number;
  id?: string | number;

  PathologyIcdeSort?: number;
  PathologyIcdeName?: string;
  PathologyIcdeCode?: string;
  PalgNo?: string;
}

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },

  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('pathologicalDiagnosisTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('pathologicalDiagnosisTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const clearKeysMap = {
  // 仅用于联动删除使用
  PathologyIcdeCode: ['PathologyIcdeName', 'PathologyIcdeCode'],
};

const PathologyIcdeDragTable = (props: PathologyIcdeDragTableProps) => {
  const itemRef = React.useRef<any>();

  const [form] = Form.useForm();

  const pathologyIcdeDataSource =
    Form.useWatch('pathologicalDiagnosisTable', props?.form) ?? [];

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    pathologyIcdeDataSource?.length,
  );

  // 回跳准备状态
  const [backJumpInfo, setBackJumpInfo] = useState<any>(null);

  useEffect(() => {
    setTableDataSourceSize(pathologyIcdeDataSource?.length);
  }, [pathologyIcdeDataSource]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('pathologicalDiagnosisTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'pathological-diagnosis-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('pathologicalDiagnosisTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      pathologyIcdeColumns,
      'PathologyIcdeDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    // 监听pathology-table专用的跳转准备事件
    Emitter.on('DMR_PATHOLOGY_TABLE_JUMP_PREPARE', (jumpInfo) => {
      setBackJumpInfo(jumpInfo);
    });

    // 监听pathology-table专用的取消事件
    Emitter.on('DMR_PATHOLOGY_TABLE_JUMP_CANCEL', (cancelInfo) => {
      setBackJumpInfo(null);
    });

    // delete事件
    Emitter.on(
      getDeletePressEventKey('pathologicalDiagnosisTable'),
      (itemId) => {
        // key 包含 index 和 其他的东西
        console.log('pathologicalDiagnosisTable', itemId);
        let itemIds = itemId?.split('#');
        let index = parseInt(itemIds?.at(2));
        let key = itemIds?.at(1);

        let clearKeys = [key];
        if (clearKeysMap[key]) {
          clearKeys = clearKeysMap[key];
        }
        clearValuesByKeys(clearKeys, index);
      },
    );

    Emitter.on(EventConstant.DMR_PATHOLOGY_ICDE_ADD, (params?: any) => {
      // 兼容原有的 focusId?: string 参数格式
      let focusId: string | undefined;
      let defaultMCode = false;

      if (typeof params === 'string') {
        // 原有格式：直接传递 focusId
        focusId = params;
      } else if (typeof params === 'object' && params !== null) {
        // 新格式：传递对象 { focusId, defaultMCode }
        focusId = params.focusId;
        defaultMCode = params.defaultMCode || false;
      }

      let rowData: any = {
        id: generateUniqueNumberId(),
        UniqueId: uuidv4(),
        // Custom: 永嘉
        TechType: '4',
        DiagAccord: '1',
      };

      // Custom: 永嘉 只有在特定情况下（通过 handlePathologicalDiagnosisFocus 调用）才设置默认值
      if (defaultMCode) {
        rowData.PathoType = 'MCode'; // 默认 PathoType 为 MCode
      } else {
        // 其他情况下 即自动添加的情况下
        rowData.PathoType = 'Patho'; // 默认 PathoType 为 Patho
        let insurTableData =
          props?.form?.getFieldValue('diagnosis-main-table') ||
          props?.form?.getFieldValue('diagnosis-table');
        // 默认 主要诊断
        rowData.PathologyIcdeCode = insurTableData?.at(0)?.IcdeCode;
        rowData.PathologyIcdeName = insurTableData?.at(0)?.IcdeName;
      }

      let tableData = props?.form?.getFieldValue(
        'pathological-diagnosis-table',
      );

      tableData.splice(tableData.length, 0, rowData);
      props?.form?.setFieldValue(
        'pathological-diagnosis-table',
        cloneDeep(tableData),
      );

      // Custom: 永嘉 根据是否设置了默认 MCode 来决定 focus 方式
      if (defaultMCode) {
        // Focus 到新增数据的 IcdeSelect input
        const newIndex = tableData.length - 1;
        setWaitFocusId(`formItem#PathologyIcdeCode#${newIndex}#IcdeSelect`);
      } else {
        // 原有的 focus 方式
        // setWaitFocusId(
        //   `div[id=pathologicalDiagnosisTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
        // );
        setTimeout(() => {
          const newIndex = tableData.length - 1;
          // 默认病理号
          const palgNoInput = document.getElementById(
            `formItem#PalgNo#${newIndex}#Input#PathologyTable`,
          );
          if (palgNoInput) {
            setTimeout(() => {
              palgNoInput.focus();
            }, 100);
          }
        }, 200);
      }
      setTableDataSourceSize(tableData?.length);
    });

    // Custom: 永嘉 处理设置空行默认值的事件
    Emitter.on('DMR_PATHOLOGY_SET_EMPTY_ROW_DEFAULT', (params) => {
      const { emptyRowIndex } = params;
      const pathologicalData =
        props?.form?.getFieldValue('pathological-diagnosis-table') || [];

      if (emptyRowIndex >= 0 && emptyRowIndex < pathologicalData.length) {
        // 给指定的空行设置默认值
        const tableData = cloneDeep(pathologicalData);
        tableData[emptyRowIndex].PathoType = 'MCode';
        tableData[emptyRowIndex].DiagAccord = '1';
        tableData[emptyRowIndex].TechType = '4';

        props?.form?.setFieldValue('pathological-diagnosis-table', tableData);
      }
    });

    Emitter.on(EventConstant.DMR_PATHOLOGY_ICDE_DELETE, (index) => {
      if (index > -1) {
        let tableData = props?.form?.getFieldValue(
          'pathological-diagnosis-table',
        );
        tableData.splice(index, 1);

        // 更新form
        props?.form?.setFieldValue(
          'pathological-diagnosis-table',
          cloneDeep(tableData),
        );

        // 删除的时候 给出当前那个选中的
        // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
        // 表格中不存在即写第0个的icdeName 建议写死
        let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
        if (dataItems?.length > 0) {
          setWaitFocusId(
            `div[id=diagnosisTable] tbody > tr:nth-child(${
              index >= dataItems.length - 1 ? dataItems.length : index + 1
            }) > td input`,
          );
        }
        setTableDataSourceSize(tableData?.length);
      }
    });

    Emitter.on(
      getArrowUpDownEventKey('pathologicalDiagnosisTable'),
      (payload) => {
        const pathologyIcdeDataSource = props?.form?.getFieldValue(
          'pathological-diagnosis-table',
        );
        let type = payload?.type;
        console.log('payload', payload);
        if (
          payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
          payload?.trigger === 'hotkey'
        ) {
          // 表示是 下拉框 需要定制
          return;
        }

        payload?.event?.stopPropagation();

        let { nextIndex, activePaths } = calculateNextIndex(type);
        if (type === 'UP') {
          if (nextIndex < 0) {
            nextIndex = undefined;
          }
        }

        if (type === 'DOWN') {
          if (nextIndex > pathologyIcdeDataSource?.length - 2) {
            nextIndex = undefined;
          }
        }

        if (nextIndex !== undefined) {
          activePaths[2] = nextIndex.toString();
          document.getElementById(activePaths?.join('#'))?.focus();
        }
      },
    );

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('pathologicalDiagnosisTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off('DMR_PATHOLOGY_TABLE_JUMP_PREPARE');
      Emitter.off('DMR_PATHOLOGY_TABLE_JUMP_CANCEL');
      Emitter.off('DMR_PATHOLOGY_SET_EMPTY_ROW_DEFAULT');
      Emitter.off(EventConstant.DMR_PATHOLOGY_ICDE_ADD);
      Emitter.off(EventConstant.DMR_PATHOLOGY_ICDE_DELETE);

      Emitter.off(getDeletePressEventKey('pathologicalDiagnosisTable'));
      Emitter.off(getArrowUpDownEventKey('pathologicalDiagnosisTable'));
    };
  }, []);

  const clearValuesByKeys = (keys, index) => {
    const pathologyIcdeDataSource = props?.form?.getFieldValue(
      'pathological-diagnosis-table',
    );
    let formItemId = pathologyIcdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('pathological-diagnosis-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue(
      'pathological-diagnosis-table',
      cloneDeep(tableData),
    );
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'pathologicalDiagnosisTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(
        props?.form?.getFieldValue('pathological-diagnosis-table') ?? []
      )
        ?.filter((item) => item.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = generateUniqueNumberId();
          }

          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(tableData, changedValues) => {
        // setPathologyIcdeDataSource(tableData);

        props?.form?.setFieldValue('pathological-diagnosis-table', tableData);
        triggerFormValueChangeEvent('pathological-diagnosis-table');

        // Custom: 永嘉 检查是否需要执行回跳
        if (backJumpInfo) {
          // 检查本次变化是否涉及 PathologyIcdeCode 字段，以及是否设置为有值
          const hasPathologyIcdeCodeChange =
            changedValues &&
            Object.values(changedValues).some(
              (item: any) => item && item.PathologyIcdeCode !== undefined,
            );

          if (hasPathologyIcdeCodeChange) {
            // 检查变化的 PathologyIcdeCode 是否为有值（非空且不是纯空格）
            const hasPathologyIcdeCodeWithValue = Object.values(
              changedValues,
            ).some(
              (item: any) =>
                item &&
                item.PathologyIcdeCode &&
                item.PathologyIcdeCode.trim() !== '',
            );

            // 只有当 PathologyIcdeCode 设置为有值时才执行回跳
            if (hasPathologyIcdeCodeWithValue) {
              // 延迟执行回跳，确保当前输入已处理完成
              setTimeout(() => {
                Emitter.emit('DMR_PATHOLOGY_BACK_JUMP_EXECUTE', {
                  ...backJumpInfo,
                  targetIndex: 1,
                  targetFieldId: 'formItem#MainIcdeCode#1#IcdeSelect',
                });
                setBackJumpInfo(null); // 清除回跳状态
              }, 150);
            }
          }
        }
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        props?.form?.setFieldValue(
          'pathological-diagnosis-table',
          cloneDeep(tableData),
        );
        // props?.form?.setFieldValue(
        //   'pathologicalDiagnosisTable',
        //   cloneDeep(newTableData),
        // );
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('pathological-diagnosis-table');
      }}
      columns={tableColumns}
    />
  );
};

export default React.memo(PathologyIcdeDragTable);
