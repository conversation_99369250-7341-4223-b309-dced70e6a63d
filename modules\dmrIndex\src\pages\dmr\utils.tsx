import { configurableDataIndex } from '@/pages/configuration/properties/column';
import { tableComponentToColumnsPropertiesMapping } from '@/pages/configuration/components/form/table-columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import { message } from 'antd';

/**
 * 检查是否应该基于用户角色进行权限控制
 * @param userRole 用户角色
 * @returns 是否应该进行权限控制
 */
export const shouldApplyUserRolePermission = (
  userRole?: string | null,
): boolean => {
  const externalDmrConfig = (window as any).externalConfig?.['dmr'];
  const enableUserRolePermission =
    externalDmrConfig?.['enableUserRolePermission'];

  // 如果配置为 false，则不进行权限控制
  if (enableUserRolePermission === false) {
    return false;
  }

  // 默认启用权限控制，并且需要有有效的用户角色
  return true;
};

/**
 * 检查用户是否为 Medicare 角色且启用了权限控制
 * @param userRole 用户角色
 * @returns 是否为 Medicare 角色且启用权限控制
 */
export const isMedicareWithPermission = (userRole?: string | null): boolean => {
  return shouldApplyUserRolePermission(userRole) && userRole === 'Medicare';
};

export const buildMessageOnSubmit = (reviewData: any[]) => {
  let hasRestrictErrorLevel = false;
  let hasSuggestiveErrorLevel = false;

  for (let reviewItem of reviewData?.sort(
    (a, b) => parseInt(b?.ErrorLevel ?? '0') - parseInt(a?.ErrorLevel ?? '0'),
  )) {
    if (reviewItem?.ErrorLevel === '5') {
      hasRestrictErrorLevel = true;
      break;
    }

    if (reviewItem?.ErrorLevel === '2') {
      hasSuggestiveErrorLevel = true;
      break;
    }
  }

  if (hasRestrictErrorLevel) {
    message.error('审核不通过，存在强制性错误');
  } else if (hasSuggestiveErrorLevel) {
    message.warn('保存成功，存在提示性错误');
  } else {
    message.success('保存成功，审核不通过');
  }
};

export const defaultValueToFormValueBeforeValidation = (form: any) => {
  let formValues = form.getFieldsValue();

  // 当且仅当 年龄为 0 才能写 -
  if (
    formValues?.['PatAge']?.toString() === '0' &&
    formValues?.['PatIdKind']?.toString() === '1'
  ) {
    if (isEmptyValues(formValues?.['IdCard'])) {
      form.setFieldValue('IdCard', '-');
    }
  }
};

const publicModeHideKeys =
  (window as any).externalConfig?.['dmr']?.['publicModeHideKeys'] ?? [];

export const mergeColumnsInDmrTable = (
  propertyColumns,
  columns,
  componentName: string,
) => {
  let mergedColumns = columns?.map((item) => {
    let columnProperty = (propertyColumns ?? [])?.find(
      (propertyItem) => item?.dataIndex === propertyItem?.dataIndex,
    );

    let extraHiddenColumnProps = {};

    if (global['PublicMode'] === true) {
      if (publicModeHideKeys?.includes(item?.dataIndex)) {
        extraHiddenColumnProps['visible'] = false;
        extraHiddenColumnProps['readonly'] = true;
      }
    }

    if (columnProperty) {
      let remoteProperty = {};
      configurableDataIndex?.forEach((key) => {
        remoteProperty[key] = columnProperty?.[key] ?? item?.[key] ?? null;
      });

      if (
        tableComponentToColumnsPropertiesMapping[componentName]?.[
          item?.dataIndex
        ]
      ) {
        tableComponentToColumnsPropertiesMapping[componentName]?.[
          item?.dataIndex
        ]?.forEach((blockKey) => {
          // 不可修改的 不做覆盖
          delete remoteProperty?.[blockKey];
        });
      }

      return {
        ...item,
        ...remoteProperty,
        ...extraHiddenColumnProps,
      };
    }

    return {
      ...item,
      ...extraHiddenColumnProps,
    };
  });

  if (componentName === 'OperationDragTable') {
    console.log('OperationDragTable', propertyColumns, columns);
  }

  mergedColumns?.forEach((item) => {
    if (item?.key === 'CONTAINS_ADD') {
      item['onCell'] = (record, rowIndex) => {
        if (record?.id === 'ADD') {
          return {
            colSpan: mergedColumns?.filter((item) => item?.visible === true)
              ?.length,
          };
        }

        return {
          record: record,
          rowIndex: rowIndex,
          rowkey: 'id',
          dataIndex: item?.dataIndex,
        };
      };
    } else {
      // IcdeMainTable 特殊处理 他没有CONTAINS_ADD
      if (componentName === 'IcdeMainTable' && item?.key === 'sort') {
        item['onCell'] = (record, rowIndex) => {
          if (record?.id === 'ADD') {
            console.log(
              'IcdeMainTable ADD',
              mergedColumns,
              mergedColumns?.filter((item) => item?.visible === true),
            );
            return {
              colSpan:
                mergedColumns?.filter((item) => item?.visible === true)
                  ?.length - 1,
            };
          }

          return {
            record: record,
            rowIndex: rowIndex,
            rowkey: 'id',
            dataIndex: item?.dataIndex,
          };
        };
      } else {
        // 其他所有
        item['onCell'] = (record, rowIndex) => {
          if (record?.id === 'ADD') {
            return {
              colSpan: 0,
            };
          }

          return {
            record: record,
            rowIndex: rowIndex,
            rowkey: 'id',
            dataIndex: item?.dataIndex,
          };
        };
      }
    }
  });

  const uniqueIdItem = [
    {
      dataIndex: 'UniqueId',
      title: 'UniqueId',
      visible: false,
      readonly: true,
    },
  ];

  return uniqueIdItem.concat(
    mergedColumns?.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)),
  );
};

export const employeeDataSourceProcessor = (dataSource) => {
  return dataSource.map((item) => {
    item['label'] = `${item.Code}：${item.Name}`;
    return item;
  });
};

export const tableFormValuesToArray = (tableData: any[], formValues: any) => {
  let newTableData = [];

  Object.keys(formValues).forEach((key) => {
    if (key !== 'ADD') {
      newTableData.push(formValues[key]);
    }
  });

  return newTableData;
};

// 检查元素是否完全在容器的可视区域内（根据forceScrollToCenterDistance而定）
export const isInElementViewport = (
  el: HTMLElement,
  container: HTMLElement,
  forceScrollToCenterDistance: number,
) => {
  if (el) {
    let elementRect = el.getBoundingClientRect();
    let containerRect = container.getBoundingClientRect();

    const eleTop = elementRect.top;
    const eleBottom = elementRect.bottom;

    // The element is fully visible in the container
    return (
      eleTop - containerRect.top >= 0 &&
      eleBottom - containerRect?.top <=
        containerRect?.height - forceScrollToCenterDistance
    );
  }

  return false;
};

export const ignoreKeyStrokesInVisibilityChange = (event: any) => {
  let eventKey = event?.key;

  return /[a-zA-Z0-9.]/.test(eventKey);
};
