import { Button, Form, message, Modal, Space, Tooltip } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { TableColumnEditButtonProps } from '../table/column-edit';
import { UniDragEditTable, UniTable } from '../index';
import {
  queryParamItemsDataSourceProcessor,
  tableColumnPropertiesToColumns,
  tableEditColumns,
} from './columns';
import mergeWith from 'lodash/mergeWith';
import pick from 'lodash/pick';
import unset from 'lodash/unset';
let dot = require('./dot-object');
// @ts-ignore
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import { FormItemColumnFullProperties } from './columns';
import { tableDataSourceProcessor } from '../table/column-edit/columns';
import merge from 'lodash/merge';
import { omitBy } from 'lodash';
import {
  DrawerForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
} from '../pro-form';
import ArgsFormItems from './arg-edit';
import { SortableContainer, SortableElement } from 'react-sortable-hoc';
import { TableAction } from '@uni/reducers/src';
import { arrayMoveImmutable, useRefFunction } from '@ant-design/pro-utils';
import {
  dictDataChangeArgChange,
  getFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts,
  typePropertiesProcessor,
} from './processor';
import { getHeaderFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts } from './header-processor';
import './index.less';
import { useDeepCompareEffect } from 'ahooks';
import { dotRecurse } from './utils';

const paramCustomizeEnable =
  (window as any).externalConfig?.['common']?.paramCustomizeEnable ?? false;

interface QueryParameterConfigurationEditModalProps {
  dictData?: any;
  form?: any;
  queryInterfaceUrl?: string;
  fetchMethod?: string;
  saveMethod?: string;
  formItems?: any[];
  onFormItemsEdit?: (formItems: any[]) => void;
  onFormItemsEditConfirm?: (formItems: any[]) => void;
  onFormItemsSaveSuccess?: (formItems?: any[], defaultArgs?: any) => void;
}

interface QueryParameterConfigurationContainerProps
  extends QueryParameterConfigurationEditModalProps {
  containerRef: any;
}

export interface QueryParameterConfigurationEditButtonProps
  extends QueryParameterConfigurationEditModalProps {
  style?: React.CSSProperties;
  className?: string;
  title?: string;
  currentSearchOptsLength: number;
  setSearchOpts: any;
  extraRequest?: any;

  type: 'HEADER' | 'LEFT_CONTAINER';

  setHeaderType?: (type: string) => void; // 是不是自定义header
}

export interface QueryParameterConfigurationItemEditDrawerProps
  extends QueryParameterConfigurationEditModalProps {
  editTableContainerRef: any;
}

export interface QueryParameterConfigurationItemArgProps
  extends QueryParameterConfigurationEditModalProps {
  selectedType?: any;
  editValueObj?: any;
}

const QueryParameterConfigurationItemEditDrawer = (
  props: QueryParameterConfigurationItemEditDrawerProps,
) => {
  const [queryParamsDrawerOpen, setQueryParamsDrawerOpen] =
    React.useState(false);

  const [currentSelectedRecordItem, setCurrentSelectedRecordItem] =
    React.useState(undefined);

  const [form] = Form.useForm();

  useEffect(() => {
    Emitter.on(EventConstant.QUERY_PARAMETER_ITEM_EDIT, (payload) => {
      setQueryParamsDrawerOpen(payload?.status);
      setCurrentSelectedRecordItem(payload?.record);
      let currentSelectItem = props?.form?.getFieldValue(payload?.record?.id);

      let extraInputConfigs = {};
      try {
        extraInputConfigs = {
          extra: JSON.parse(payload?.record?.ExtraInputConfig),
        };
      } catch (e) {
        extraInputConfigs = {};
      }
      let dotNotation = {};
      dotRecurse(extraInputConfigs, '', dotNotation);

      form.setFieldsValue({
        ...payload?.record,
        ...currentSelectItem,
        ...dotNotation,
        ColumnType: typePropertiesProcessor(payload?.record?.ColumnType),
      });
    });

    return () => {
      Emitter.off(EventConstant.QUERY_PARAMETER_ITEM_EDIT);
    };
  }, []);

  return (
    <DrawerForm
      title={`修改配置项`}
      form={form}
      // initialValues={initialValues}
      autoFocusFirstInput={true}
      drawerProps={{
        zIndex: 1002,
        destroyOnClose: true,
        onClose: () => {
          setQueryParamsDrawerOpen(false);
        },
      }}
      open={queryParamsDrawerOpen}
      width={530}
      submitTimeout={2000}
      onValuesChange={(changeValues: any, values: any) => {
        console.log('changeValues', changeValues, values);
      }}
      onFinish={async (values) => {
        console.log('values', values);

        let parsedValues = dot.object(values);
        let extraInputConfig = isEmptyValues(parsedValues?.extra)
          ? null
          : parsedValues?.extra;

        let currenSelectItem = {
          ...currentSelectedRecordItem,
          ...parsedValues,
          ExtraInputConfig: extraInputConfig,
        };

        let dataSources =
          props?.editTableContainerRef?.current?.getDataSources();
        if (!isEmptyValues(dataSources)) {
          let editItemIndex = dataSources?.findIndex(
            (item) => item.id === currentSelectedRecordItem?.id,
          );
          if (editItemIndex > -1) {
            dataSources[editItemIndex] = currenSelectItem;
            // 更新到外层表格里面
            props?.form?.setFieldValue(
              currentSelectedRecordItem?.id,
              currenSelectItem,
            );
            props?.editTableContainerRef?.current?.setDataSources([
              ...dataSources,
            ]);
            setQueryParamsDrawerOpen(false);
          }
        }
      }}
    >
      <ArgsFormItems {...props} form={form} />
    </DrawerForm>
  );
};

const QueryParameterConfigurationEditModal = (
  props: QueryParameterConfigurationEditModalProps,
) => {
  const [queryParamConfigEditOpen, setQueryParamConfigEditOpen] =
    React.useState<boolean>(false);

  const [form] = Form.useForm();

  const containerRef = React.useRef(null);

  const [modalEventData, setModalEventData] = React.useState<any>({});

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      EventConstant.QUERY_PARAMETER_EDIT,
      (data) => {
        setModalEventData(data);
        setQueryParamConfigEditOpen(true);
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off(
        EventConstant.QUERY_PARAMETER_EDIT,
      );
    };
  }, []);

  const { loading: queryArgRefsSaveLoading, run: queryArgRefsSaveReq } =
    useRequest(
      async (argRefs, defaultArgs) => {
        const res: RespVO<any> = await uniCommonService(
          props?.queryInterfaceUrl ?? modalEventData?.queryInterfaceUrl,
          {
            method: modalEventData?.saveMethod ?? 'POST',
            headers: {
              'Modify-Arg-Definitions': 1,
            },
            data: {
              ArgsJson: argRefs,
              ExtraConfig: {
                DefaultArgs: defaultArgs,
              },
            },
          },
        );
        return res;
      },
      {
        manual: true,
        onSuccess: (data) => {
          if (!isEmptyValues(data?.ArgDefs)) {
            setQueryParamConfigEditOpen(false);
            props?.onFormItemsSaveSuccess &&
              props?.onFormItemsSaveSuccess(
                data?.ArgDefs,
                data?.DefaultArgs ?? {},
              );
            modalEventData?.onFormItemsSaveSuccess &&
              modalEventData?.onFormItemsSaveSuccess(
                data?.ArgDefs,
                data?.DefaultArgs ?? {},
              );
            message.success('保存成功');
          } else {
            message.error('保存失败');
          }
        },
        // formatResult: (response: RespVO<TableColumns>) => {
        //   return response;
        // },
      },
    );

  return (
    <Modal
      zIndex={1001}
      title={
        <Space>
          <span>查询参数自定义</span>
          <Button
            onClick={async (e) => {
              await queryArgRefsSaveReq('', '');
              Emitter.emit(EventConstant.QUERY_PARAMETER_RESET);
            }}
          >
            重置
          </Button>
        </Space>
      }
      open={queryParamConfigEditOpen}
      width={1200}
      okText={'确定'}
      cancelText={'取消'}
      wrapClassName={'table-columns-configuration-container'}
      onOk={async (event) => {
        console.log('form items', (props?.form ?? form).getFieldsValue());

        // 默认值
        let DefaultArgs = {};

        let originDataSources = containerRef.current.getDataSources();

        let argRefs = [];
        let formValues = cloneDeep((props?.form ?? form).getFieldsValue());
        Object.keys(formValues).forEach((key) => {
          let originRefItemIndex = originDataSources?.findIndex(
            (item) => item?.id === key,
          );
          if (originRefItemIndex > -1) {
            let originRefItem = originDataSources?.at(originRefItemIndex);
            let argRefItem = {
              ...originRefItem,
              ColumnSort: originRefItemIndex,
              ...(formValues?.[key] ?? {}),
            };

            if (argRefItem?.['sort']) {
              delete argRefItem['sort'];
            }

            if (argRefItem?.['operation']) {
              delete argRefItem['operation'];
            }

            if (argRefItem?.['DefaultValue']) {
              DefaultArgs[argRefItem['ColumnName']] =
                argRefItem?.['DefaultValue'];
            }

            argRefs.push(argRefItem);
          }
        });

        props?.onFormItemsEditConfirm && props?.onFormItemsEditConfirm(argRefs);
        // 保存 列修改
        if (
          !isEmptyValues(
            props?.queryInterfaceUrl ?? modalEventData?.queryInterfaceUrl,
          )
        ) {
          queryArgRefsSaveReq(argRefs, DefaultArgs);
        } else {
          // setQueryParamConfigEditOpen(false);
        }
      }}
      onCancel={(event) => {
        setQueryParamConfigEditOpen(false);
      }}
      confirmLoading={queryArgRefsSaveLoading}
      destroyOnClose={true}
      getContainer={document.getElementById('dmr-configuration-wrapper')}
    >
      <QueryParameterConfigurationEditContainer
        {...props}
        containerRef={containerRef}
        form={props?.form ?? form}
        {...modalEventData}
      />

      <QueryParameterConfigurationItemEditDrawer
        {...props}
        editTableContainerRef={containerRef}
        form={props?.form ?? form}
        {...modalEventData}
      />
    </Modal>
  );
};

const QueryParameterConfigurationEditContainer = (
  props: QueryParameterConfigurationContainerProps,
) => {
  const [queryParamsCandidateDataSource, setQueryParamsCandidateDataSource] =
    React.useState<any[]>([]);

  useImperativeHandle(props?.containerRef, () => {
    return {
      getDataSources: () => {
        return queryParamsCandidateDataSource;
      },

      setDataSources: (dataSources) => {
        setQueryParamsCandidateDataSource(dataSources);
      },
    };
  });

  useEffect(() => {
    if (!isEmptyValues(props?.queryInterfaceUrl)) {
      queryParamsGetReq();
    } else {
      setQueryParamsCandidateDataSource([]);
    }

    // 重置
    Emitter.on(EventConstant.QUERY_PARAMETER_RESET, () => {
      if (!isEmptyValues(props?.queryInterfaceUrl)) {
        queryParamsGetReq();
      } else {
        setQueryParamsCandidateDataSource([]);
      }
    });

    return () => {
      Emitter.off(EventConstant.QUERY_PARAMETER_RESET);
    };
  }, [props?.formItems, props?.queryInterfaceUrl]);

  const { loading: queryParamsGetLoading, run: queryParamsGetReq } = useRequest(
    async () => {
      const response: RespVO<any> = await uniCommonService(
        props?.queryInterfaceUrl,
        {
          method: props?.fetchMethod ?? 'POST',
          headers: {
            'Retrieve-Arg-Definitions': 1,
          },
        },
      );
      if (response.code === 0) {
        setQueryParamsCandidateDataSource(
          queryParamItemsDataSourceProcessor(
            response?.data?.ArgDefs ?? [],
            props?.formItems,
            JSON.parse(response.data?.ExtraConfig ?? '{}')?.DefaultArgs,
          ),
        );
      } else {
        setQueryParamsCandidateDataSource([]);
      }
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    Emitter.on(EventConstant.QUERY_PARAMETER_SWITCH_EDIT, (payload) => {
      let tableItem = queryParamsCandidateDataSource?.find(
        (item) => item.id === payload?.record?.id,
      );
      tableItem[payload?.key] = payload?.checked ?? false;

      setQueryParamsCandidateDataSource([...queryParamsCandidateDataSource]);
    });

    return () => {
      Emitter.off(EventConstant.QUERY_PARAMETER_SWITCH_EDIT);
    };
  }, [queryParamsCandidateDataSource]);

  return (
    <UniDragEditTable
      className={'query-params-edit-container'}
      bordered={false}
      form={props?.form}
      key={'query-params-edit-table'}
      id={'query-params-edit-table'}
      tableId={'query-params-edit-table'}
      scroll={{
        y: 400,
        x: 'max-content',
      }}
      loading={queryParamsGetLoading}
      pagination={false}
      forceColumnsUpdate={true}
      columns={[
        ...tableEditColumns(),
        ...tableColumnPropertiesToColumns(FormItemColumnFullProperties),
      ]}
      dataSource={queryParamsCandidateDataSource}
      rowKey={'id'}
      onValuesChange={(recordList: any[]) => {
        props?.onFormItemsEdit && props?.onFormItemsEdit(recordList);
      }}
      onTableDataSourceOrderChange={(tableData) => {
        let formValues = props?.form?.getFieldsValue();
        tableData?.forEach((item, index) => {
          // 设定tableData 与 form Values 合并
          let currentFormItem = formValues[item?.id];
          mergeWith(
            item,
            pick(
              currentFormItem,
              FormItemColumnFullProperties?.map((item) => item?.dataIndex),
            ),
            (objValue, srcValue, key, object) => {
              if (srcValue == null) {
                unset(object, key);
              }
            },
          );
        });
        props?.form.setFieldsValue({
          ...formValues,
        });

        setQueryParamsCandidateDataSource(tableData);

        props?.onFormItemsEdit && props?.onFormItemsEdit(tableData);
      }}
    />
  );
};

const QueryParameterConfigurationEditButton = (
  props: QueryParameterConfigurationEditButtonProps,
) => {
  let userInfoString = sessionStorage.getItem('userInfo');
  let userInfo = JSON.parse(userInfoString);

  const [hasSuccessDone, setHasSuccessDone] = useState(false);

  // 重置 hasSuccessDone 的方式
  useDeepCompareEffect(() => {
    setHasSuccessDone(false);
  }, [props?.queryInterfaceUrl, props?.formItems]);

  // 请求接口并且赋值到外层去
  useEffect(() => {
    // TODO 这个 为 0 的判断感觉有问题 如果默认进来的时候表头全部visible为false会导致一直触发
    if (props?.currentSearchOptsLength === 0 && !hasSuccessDone) {
      if (props?.type === 'LEFT_CONTAINER') {
        if (props?.queryInterfaceUrl && paramCustomizeEnable === true) {
          getFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts(
            props?.queryInterfaceUrl,
            props?.extraRequest,
            props?.formItems,
            props?.setSearchOpts,
            props?.dictData,
            props?.form,
            // TODO 最后传一个？
          );
        } else {
          let formItems = dictDataChangeArgChange(
            props?.formItems,
            props?.dictData,
          );
          props?.setSearchOpts(
            formItems?.filter((item) => item?.visible === true),
          );
        }
      }

      if (props?.type === 'HEADER') {
        if (props?.queryInterfaceUrl && paramCustomizeEnable === true) {
          getHeaderFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts(
            props?.queryInterfaceUrl,
            props?.extraRequest,
            props?.formItems,
            props?.setSearchOpts,
            // 最后再传一个
            setHasSuccessDone,
          );
        } else {
          props?.setSearchOpts([...props?.formItems]);
        }
      }
    }
  }, [props, hasSuccessDone]);

  //

  // 保证dictData一定存在
  useEffect(() => {
    if (props?.currentSearchOptsLength > 0) {
      if (props?.type === 'LEFT_CONTAINER') {
        let formItems = dictDataChangeArgChange(
          props?.formItems,
          props?.dictData,
        );
        props?.setSearchOpts([...formItems]);
      }

      if (props?.type === 'HEADER') {
        if (props?.queryInterfaceUrl && paramCustomizeEnable === true) {
          // TODO props?.setSearchOpts([...props?.formItems]);
        }
      }
    }
  }, [props?.dictData]);

  if (paramCustomizeEnable === false) {
    return <></>;
  }

  if (
    !userInfo?.Roles?.includes('Admin') ||
    isEmptyValues(props?.queryInterfaceUrl)
  ) {
    return <></>;
  }

  return (
    <Tooltip title={props?.title ?? '查询参数配置'}>
      <Button
        style={
          props?.type == 'HEADER'
            ? {
                position: 'absolute',
                top: 10,
                right: 10,
                zIndex: 2,
              }
            : {}
        }
        className={props?.className ?? ''}
        type="text"
        shape="circle"
        icon={<SettingOutlined className="infinity_rotate" />}
        onClick={(e) => {
          (global?.window as any)?.eventEmitter?.emit(
            EventConstant.QUERY_PARAMETER_EDIT,
            { ...props },
          );
        }}
      />
    </Tooltip>
  );
};

export {
  QueryParameterConfigurationEditModal,
  QueryParameterConfigurationEditButton,
};
