import { IGridItem } from '@/pages/dmr/interfaces';

export const blood: IGridItem[] = [
  {
    data: {
      prefix: '血型',
      key: 'XX',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'XX',
        modelDataKey: 'XX',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    w: 3,
    xs: {
      w: 6,
    },
    xxs: {
      w: 5,
    },
  },
  {
    data: {
      prefix: 'Rh',
      key: 'Rh',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'Rh',
        modelDataKey: 'Rh',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    offsetX: 1,
    w: 3,
    xs: {
      w: 6,
    },
    xxs: {
      w: 5,
    },
  },
  {
    data: {
      prefix: '是否输血',
      key: 'IsBloods',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'IsBloods',
        modelDataKey: 'IsBloods',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    w: 4,
    xs: {
      w: 6,
    },
    xxs: {
      w: 5,
    },
  },
  {
    data: {
      prefix: '输血反应',
      key: 'BloodReaction',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'BloodReaction',
        modelDataKey: 'BloodReaction',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    w: 4,
    xs: {
      w: 6,
    },
    xxs: {
      w: 5,
    },
  },
];
