import { ProFormInstance, StepsForm } from '@uni/components/src/pro-form';
import { Modal, Button, notification, Space, Select } from 'antd';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import FirstStep from '../firstStep';
import SecondStep from '../secondStep';
import { isRespErr } from '@/utils/widgets';
import ThirdStep from '../thirdStep';
import { ReportTypes } from '../../constants';
import FourthStep from '../fourthStep';
import { useEventEmitter } from 'ahooks';

const waitTime = (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

const ModalForm = ({
  modalState,
  closeModal,
  editValueObj,
  dictData,
  masterAction,
  colOrArgsAction,
  appcodeOpts,
  ...restProps
}: any) => {
  const formMapRef = useRef<
    React.MutableRefObject<ProFormInstance<any> | undefined>[]
  >([]);

  const [hideItems, setHideItems] = useState([]);
  const [stepCurrent, setStepCurrent] = useState(0);

  const [masterId, setMasterId] = useState(undefined);
  // 进入第二步后，保存一下第一步的数据，官方的formref不好使
  // TODO: 但是formMapRef好使，可以用其代替
  const [firstStepValues, setFirstStepValues] = useState(null);

  // 判断value有没有变过?
  const hasFirstStepChanged = useRef(false);

  // 将state本地保存起来，因为会需要修改
  const [state, setState] = useState(undefined);

  // 第四步点击外面的提交时 需要用到内部数据去调接口
  const event$ = useEventEmitter();

  useEffect(() => {
    if (!_.isEmpty(editValueObj)) {
      setMasterId(editValueObj?.Master?.ReportSettingMasterId);
    }
  }, [editValueObj]);

  useEffect(() => {
    if (modalState?.state) {
      setState(modalState?.state);
    }
  }, [modalState]);

  useEffect(() => {
    if (state?.defaultValues) {
      let hiddenItems = Object.keys(state?.defaultValues).map((d) => d);
      setHideItems(hiddenItems);
      // 切换defaultValue = 切换modal类型
      // 在这里对新增时的dataSize做特殊处理
      if (_.isEmpty(editValueObj)) {
        if (
          hiddenItems?.findIndex((d) => d === 'DataSize') === -1 &&
          formMapRef.current
            ?.at(0)
            .current.getFieldValue(['MasterArgs', 'DataSize']) === 'Details'
        ) {
          setTimeout(() => {
            formMapRef.current
              ?.at(0)
              .current.setFieldValue(
                ['MasterArgs', 'DataSize'],
                'VariableMultiLine',
              );
          }, 20);
        }
      }
      console.log(editValueObj);
    }
  }, [state?.defaultValues]);

  // 记录 && upsert 时需要使用的通用设置参数，因为后面几步需要此数据
  const firstStepDataSet = (values) => {
    return {
      ...values,
      MasterArgs: {
        ...values.MasterArgs,
        ...state?.defaultValues, // 覆盖
        ReportSettingMasterId:
          editValueObj?.Master?.ReportSettingMasterId || masterId || undefined,
      },
      // 额外处理下 ExportArgs
      ExportArgs: values?.ExportArgs
        ? values?.ExportArgs
        : values?.MasterArgs?.EnableExport
        ? {}
        : undefined,
    };
  };

  // 点击步骤 2 3 4 的事件
  const handleStepClk = (current) => {
    if (_.isEmpty(editValueObj)) return;
    if (hasFirstStepChanged.current) {
      openNotification(current);
    } else {
      // 额外添加一个 将第一步的数据赋值给 firstStepValues
      setFirstStepValues(
        firstStepDataSet(formMapRef.current.at(0).current.getFieldsValue()),
      );
      setStepCurrent(current);
    }
  };

  const openNotification = (current) => {
    const key = `open${Date.now()}`;
    const btn = (
      <Space>
        <Button
          size="small"
          onClick={() => {
            notification.close(key);
            setStepCurrent(current);
            hasFirstStepChanged.current = false;
            setFirstStepValues(
              firstStepDataSet(
                formMapRef.current.at(0).current.getFieldsValue(),
              ),
            );
          }}
        >
          跳过
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            formMapRef.current.at(0).current.submit(); // 走onFinish
            hasFirstStepChanged.current = false;
            notification.close(key);
          }}
        >
          提交
        </Button>
      </Space>
    );
    notification.open({
      message: '注意',
      description: '通用设置有未提交的修改，要现在提交吗？',
      btn,
      key,
      onClose: close,
    });
  };

  event$.useSubscription((val: any) => {
    if (val === 'forthStepCommitDone') {
      setStepCurrent(0);
      closeModal('Done', () => {
        setMasterId(undefined);
      });
    }
  });

  return (
    <>
      {/* <Modal
        title={`新建：${state?.name}`}
        width={1200}
        onCancel={() => closeModal()}
        open={visible}
        // footer={submitter}
        destroyOnClose
      >
        <Steps current={stepCurrent} items={}>

        </Steps>
      </Modal> */}
      <StepsForm
        // formRef={proFormRef}
        formMapRef={formMapRef}
        current={stepCurrent}
        onCurrentChange={(cur) => {
          setStepCurrent(cur);
        }}
        onFinish={async (values) => {
          if (stepCurrent === 3) {
            event$.emit('forthStepCommit' as any);
          } else {
            setStepCurrent(0);
            closeModal('Done', () => {
              setMasterId(undefined);
            });
            return true;
          }
        }}
        stepsFormRender={(dom, submitter) => {
          return (
            <Modal
              title={
                <Space size={15}>
                  <span>
                    {_.isEmpty(editValueObj) ? '新建' : '编辑'}：
                    {stepCurrent === 0 &&
                    state?.name !== 'ReportGroupReadOnly' ? (
                      <Select
                        value={state?.name}
                        optionLabelProp="label"
                        onChange={(value) => {
                          setState(ReportTypes?.find((d) => d.name === value));
                          // 切换时，同样会切换hideItems，此时一些initalvalue已经被注入，所以需要手动赋值（主要是新建的时候切换类型时的datasize，其他编辑时等都不会）
                        }}
                        options={ReportTypes.map((d) => ({
                          label: d.title,
                          value: d.name,
                          disabled:
                            d.name === state?.name ||
                            d.name === 'ReportGroupReadOnly',
                        }))}
                      />
                    ) : (
                      state?.title
                    )}
                  </span>
                </Space>
              }
              width={1200}
              onCancel={() => {
                setStepCurrent(0);
                hasFirstStepChanged.current = false;
                closeModal(null, () => {
                  setMasterId(undefined);
                });
              }}
              open={modalState.visible}
              footer={submitter}
              destroyOnClose={true}
              keyboard={false}
            >
              {dom}
            </Modal>
          );
        }}
        {...restProps}
      >
        <StepsForm.StepForm // 得写在外面....
          name="common"
          // title={state?.title ?? 'StatsReadOnly'}
          stepProps={{
            title: '通用设置',
            // description: '通用设置',
            onClick: (e) => {
              setStepCurrent(0);
            },
          }}
          grid={true}
          rowProps={{
            gutter: [16, 16],
          }}
          onValuesChange={(changeValues, allValues) => {
            // hack 简单处理
            if (!hasFirstStepChanged.current) {
              hasFirstStepChanged.current = true;
            }
            // console.log('valueChanged: ', changeValues, allValues)
          }}
          onFinish={async (values) => {
            console.log('firstStep Values', values);
            let data = firstStepDataSet(values);
            // 新增 dependencyArgs & relationArgs 但是第一步不需要编辑他们 upsert的时候需要带入，不然会被清空
            data = {
              ...data,
              DependencyArgs: editValueObj.ReportDependencySettings,
              RelationArgs: editValueObj.ReportRelationSettings,
            };
            console.log(data);
            let res = await masterAction(data);

            if (!isRespErr(res)) {
              setMasterId(res.data);
              setFirstStepValues(data);
              hasFirstStepChanged.current = false;
              return true;
            } else {
              return false;
            }
          }}
        >
          <FirstStep
            editValueObj={editValueObj}
            dictData={dictData}
            hideItems={hideItems}
            defaultValues={state?.defaultValues}
            stepType={state?.name}
            appcodeOpts={appcodeOpts}
          />
        </StepsForm.StepForm>

        <StepsForm.StepForm
          name="columns"
          title="列设置"
          stepProps={{
            // description: '列设置',
            onClick: () => handleStepClk(1),
          }}
          // onFinish={async (values) => {
          //   return true
          // }}
        >
          <SecondStep
            // editValueObj={editValueObj}
            firstStepValues={firstStepValues}
            dictData={dictData}
            hideItems={hideItems}
            defaultValues={state?.columnsDefaultValues}
            stepType={state?.name}
            masterId={masterId}
            colOrArgsAction={colOrArgsAction}
          />
        </StepsForm.StepForm>

        <StepsForm.StepForm
          name="args"
          title="参数设置"
          stepProps={{
            // description: '参数设置',
            onClick: () => handleStepClk(2),
          }}
          // onFinish={async (values) => {
          //   console.log(values)
          //   await waitTime(2000);
          //   return false;
          // }}
        >
          <ThirdStep
            // editValueObj={editValueObj}
            firstStepValues={firstStepValues}
            dictData={dictData}
            hideItems={hideItems}
            defaultValues={state?.columnsDefaultValues}
            stepType={state?.name}
            masterId={masterId}
            colOrArgsAction={colOrArgsAction}
          />
        </StepsForm.StepForm>

        {/* 第四步 tree结构的报表需要 表层是select + 可拖拽tree；内层是每个报表的额外配置内容ByRow/ByColumns */}
        {state?.name === 'ReportGroupReadOnly' && (
          <StepsForm.StepForm
            name="relatedTables"
            title="报表关系&子报表内容设置"
            stepProps={{
              // description: '参数设置',
              onClick: () => handleStepClk(3),
            }}
            onFinish={async (values) => {
              console.log('fourthSteps', values);
              await waitTime(2000);
              return false;
            }}
          >
            <FourthStep
              reportMainData={editValueObj}
              firstStepValues={firstStepValues}
              dictData={dictData}
              hideItems={hideItems}
              defaultValues={state?.columnsDefaultValues}
              stepType={state?.name}
              masterId={masterId}
              colOrArgsAction={colOrArgsAction}
              event$={event$}
            />
          </StepsForm.StepForm>
        )}
      </StepsForm>
    </>
  );
};

export default ModalForm;
