import React, { useEffect, useState } from 'react';
import { CloseOutlined, HolderOutlined } from '@ant-design/icons';
import { UniSelect, UniTable } from '@uni/components/src';
import { icdePortalColumns } from '@/pages/dmr/components/icde-oper-portal/columns';
import { Coordinates, CSS } from '@dnd-kit/utilities';
import { useDraggable } from '@dnd-kit/core';
import { defaultCoordinates } from '@/pages/dmr/components/icde-oper-portal/icde';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import './index.less';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { v4 as uuidv4 } from 'uuid';
import { But<PERSON>, Col, Input, Row, TableProps } from 'antd';
import { chargeDataTableColumns } from '@/pages/dmr/components/charge-portal/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import cloneDeep from 'lodash/cloneDeep';

interface ChargePortalProps {
  containerRef: any;
  hisId: string;
  dictData: any;
}

interface ChargeItem {
  ChargeCode?: string;
  ChargeName?: string;
  Cnt?: number;
  DetItemFeeSumamt?: number;
  FeeOcurTime?: string;
  HisId?: string;
  HisMedChrgitmType?: string;
  Pric?: number;
  RefundFlag?: string;
  Specification?: string;
  StatsMedChrgitmType?: string;
  Unit?: string;
  UnitDisplay?: string;
}

const ChargePortal = (props: ChargePortalProps) => {
  const [coordinates, setCoordinates] =
    useState<Coordinates>(defaultCoordinates);

  const [chargePortalShow, setChargePortalShow] = useState<boolean>(false);

  const [chargeData, setChargeData] = useState<ChargeItem[]>([]);
  const [chargeDataColumns, setChargeDataColumns] = useState<any[]>([]);

  const [chargeSearchParams, setChargeSearchParams] = useState({});

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: 'charge-data-container',
    });
  const style = {
    transform: CSS.Translate.toString(transform),
  };

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      showStatus: (data: any) => {
        chargeDataColumnsReq();
        setChargePortalShow(data?.status);
      },
      updateCoordinates: (data: any) => {
        let currentCoordinates: any = {};
        currentCoordinates['x'] =
          (coordinates['x'] ?? defaultCoordinates['x']) + data['x'];
        currentCoordinates['y'] =
          (coordinates['y'] ?? defaultCoordinates['y']) + data['y'];
        setCoordinates(currentCoordinates);
      },
    };
  });

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    chargeDataReq(pagi.current, pagi.pageSize);
  };

  const { loading: chargeDataLoading, run: chargeDataReq } = useRequest(
    (current, pageSize) => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        // 100029661
        HisId: props?.hisId,
        ...chargeSearchParams,
      };
      return uniCommonService(`Api/His/ChargeDetail/QuerySimpleDetails`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<TableResp<any, ChargeItem>>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setChargeData(
            response?.data?.data?.map((item) => {
              return {
                rowId: uuidv4(),
                ...item,
              };
            }),
          );

          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsTotal,
          });
        } else {
          setChargeData([]);
        }
      },
    },
  );

  const { loading: chargeDataColumnsLoading, run: chargeDataColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(`Api/His/ChargeDetail/QuerySimpleDetails`, {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<TableColumns>) => {
          if (response.code === 0) {
            setChargeDataColumns(
              tableColumnBaseProcessor(
                chargeDataTableColumns,
                response?.data?.Columns,
              ),
            );
          } else {
            setChargeDataColumns([]);
          }
        },
      },
    );

  return (
    <div
      className={'charge-portal-container charge-draggable-container'}
      ref={setNodeRef}
      style={
        {
          display: `${chargePortalShow ? 'flex' : 'none'}`,
          ...style,
          top: `${coordinates?.y}px`,
          left: `${coordinates?.x}px`,
          '--translate-x': `${transform?.x ?? 0}px`,
          '--translate-y': `${transform?.y ?? 0}px`,
        } as React.CSSProperties
      }
    >
      <div className={'charge-data-container'}>
        <div className={`charge-header-container`} {...listeners}>
          <div className={`title-container ${isDragging ? 'grabbing' : ''}`}>
            <HolderOutlined className={'handle'} size={30} />
            <span className={'title'}>收费明细</span>
          </div>
          <CloseOutlined
            onClick={() => {
              setChargePortalShow(false);
            }}
          />
        </div>

        <div className={'charge-search-container'}>
          <div className={'search-container'}>
            <Row gutter={[16, 16]}>
              <Col span={10}>
                <div className={'item-container'}>
                  <span className={'label'}>收费项目名称：</span>
                  <Input
                    placeholder={'请输入收费项目名称'}
                    value={chargeSearchParams?.['ChargeName']}
                    onChange={(event) => {
                      setChargeSearchParams({
                        ...chargeSearchParams,
                        ChargeName: event?.target.value,
                      });
                    }}
                  />
                </div>
              </Col>

              <Col span={10}>
                <div className={'item-container'}>
                  <span className={'label'}>退费状态：</span>
                  <UniSelect
                    className={'selector-container'}
                    dataSource={props?.dictData?.['RefundFlag']}
                    placeholder="请选择退费状态"
                    showSearch
                    value={chargeSearchParams?.['RefundFlags']}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    allowClear={true}
                    mode={'multiple'}
                    maxTagCount={3}
                    onChange={(value) => {
                      chargeSearchParams['RefundFlags'] = value;
                      setChargeSearchParams(cloneDeep(chargeSearchParams));
                    }}
                  />
                </div>
              </Col>
            </Row>
            <Row gutter={[16, 16]} style={{ marginTop: 10 }}>
              <Col span={10}>
                <div className={'item-container'}>
                  <span className={'label'}>费用类别：</span>
                  <UniSelect
                    className={'selector-container'}
                    dataSource={props?.dictData?.['StatsChargeType']}
                    placeholder="请选择费用类别"
                    showSearch
                    value={chargeSearchParams?.['StatsMedChrgitmTypes']}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    allowClear={true}
                    mode={'multiple'}
                    maxTagCount={3}
                    onChange={(value) => {
                      chargeSearchParams['StatsMedChrgitmTypes'] = value;
                      setChargeSearchParams(cloneDeep(chargeSearchParams));
                    }}
                  />
                </div>
              </Col>

              <Col span={10}>
                <div className={'item-container'}>
                  <span className={'label'}>院内收费类别：</span>
                  <UniSelect
                    className={'selector-container'}
                    dataSource={props?.dictData?.['HisMedChrgitmType']}
                    placeholder="请选择院内收费类别"
                    showSearch
                    value={chargeSearchParams?.['HisMedChrgitmTypes']}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    allowClear={true}
                    mode={'multiple'}
                    maxTagCount={3}
                    onChange={(value) => {
                      chargeSearchParams['HisMedChrgitmTypes'] = value;
                      setChargeSearchParams(cloneDeep(chargeSearchParams));
                    }}
                  />
                </div>
              </Col>
            </Row>
          </div>

          <Button
            type={'primary'}
            onClick={() => {
              setBackPagination({
                ...backPagination,
                current: 1,
              });
              chargeDataReq(1, backPagination?.pageSize);
            }}
          >
            查询
          </Button>
        </div>

        <UniTable
          id={'charge-data-table'}
          rowKey={'rowId'}
          scroll={{ y: 380 }}
          loading={chargeDataColumnsLoading || chargeDataLoading}
          columns={chargeDataColumns}
          dataSource={chargeData}
          pagination={backPagination}
          onChange={backTableOnChange}
          isBackPagination={true}
        />
      </div>
    </div>
  );
};

export default ChargePortal;
