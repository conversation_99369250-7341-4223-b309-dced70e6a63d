import { IGridItem } from '@/pages/dmr/interfaces';

export const poisons: IGridItem[] = [
  {
    data: {
      prefix: '损伤、中毒外部原因 编码',
      key: 'IcdeDamgsItem',
      desc: '',
      suffix: '',
      component: 'IcdeSelect',
      props: {
        componentId: 'IcdeDamgsIcdeCode',
        selectFormKey: 'IcdeDamgsIcdeCode',
        itemKey: 'IcdeDamgsItem',
        formKeys: {
          IcdeDamgsIcdeName: 'Name',
          IcdeDamgsIcdeCode: 'Code',
        },
        parentId: 'dmr-content-container',
        icdeSelectType: 'IsDamg',
      },
    },
    w: 6,
    sm: {
      w: 8,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '名称',
      key: 'IcdeDamgsIcdeName',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
        disabled: true,
      },
    },
    w: 12,
    sm: {
      w: 6,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
];
