image: node:18 # 全局默认 node 版本
variables:
  REPO_TAG: '${CI_COMMIT_REF_SLUG}_${CI_COMMIT_SHORT_SHA}'
  REPO_NAME: 'uni-dmr-web'
  DOCKER_REGISTRY: '************:5700/library'

  MINIO_ENDPOINT: 'http://************:8000'
  MINIO_ACCESS_KEY: 're4UElHnYercihlVFywj'
  MINIO_SECRET_KEY: 'URvxym044UYRe2BwmX2IaUL1k9WewGCIBWm0X2Xd'
  MINIO_BUCKET: 'gitlab'

default:
  before_script:
    - source ~/.bashrc
    - nvm use 18
    - node -v
    - git config user.name "GitLab CI Bot"
    - git config user.email "noreply+gitlab-ci-bot@{instance host}"
    - CURRENT_DATE=$(git show -s --format="%ct" | xargs -i --  date -d '@{}' '+%Y%m%d-%H%M%S')

stages:
  - pre_build
  - build_dist
  - upload_artifact
  - build_image
  - tagging
  - after_built

pre_build:
  stage: pre_build
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^his\/.*$/
    - /^beta\/.*$/
    - master
    - dev-zkz
    - /AI/
  script:
    - pnpm config set store-dir ~/.pnpm-store
    # 安装依赖
    - pnpm install
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
      - node_modules # 添加 node_modules 到缓存

make_tag:
  stage: tagging
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^his\/.*$/
    - master
    - dev-zkz
  script:
    - git tag -l | xargs git tag -d
    - PROJECT_NAME=$(sed -e "s/project-//g;s/his-//g;s/common-//g" <<< $REPO_TAG)
    - git tag "${PROJECT_NAME}_${CURRENT_DATE}"
    - git push --tags http://gitlab-ci-token:${GITLAB_PUSH_TOKEN}@${CI_SERVER_HOST}:8088/${CI_PROJECT_PATH}.git

build_dist:
  stage: build_dist
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^his\/.*$/
    - /^beta\/.*$/
    - master
    - dev-zkz
    - /AI/
  script:
    - pnpm config set store-dir ~/.pnpm-store # 使用缓存
    - pnpm install --no-frozen-lockfile # 使用 --frozen-lockfile，若 node_modules 存在且完整，pnpm install 会很快
    # 打 prod 包
    - pnpm run build:prod
  artifacts:
    name: 'dist_${REPO_TAG}'
    paths:
      - dist

upload_artifact:
  stage: upload_artifact
  before_script:
    - mc alias set myminio $MINIO_ENDPOINT $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    - CURRENT_DATE=$(git show -s --format="%ct" | xargs -i --  date -d '@{}' '+%Y%m%d-%H%M%S')
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^beta\/.*$/
    - master
  script:
    - tar -zvcf "unidmrweb-${REPO_TAG}-${CURRENT_DATE}.tar.gz" -C ./dist .
    - mc cp "unidmrweb-${REPO_TAG}-${CURRENT_DATE}.tar.gz" "myminio/${MINIO_BUCKET}/unidmrweb/${CI_COMMIT_REF_SLUG}/"

build_image:
  stage: build_image
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^his\/.*$/
    - master
  script:
    - PROJECT_NAME=$(sed -e "s/project-//g;s/his-//g;s/common-//g" <<< $CI_COMMIT_REF_SLUG)
    - IMAGE_NAME=$DOCKER_REGISTRY/${REPO_NAME}/${PROJECT_NAME}
    - docker build -t "${IMAGE_NAME}" .
    - docker tag ${IMAGE_NAME} ${IMAGE_NAME}:${CURRENT_DATE}
    - docker push ${IMAGE_NAME} --all-tags
    # 清理一下推送完之后的image
    - docker rmi ${IMAGE_NAME}:latest
    - docker rmi $(docker images -q ${IMAGE_NAME})

after_built:
  stage: after_built
  only:
    - /^common\/.*$/
    - /^project\/.*$/
    - /^his\/.*$/
    - /^beta\/.*$/
    - master
    - dev-zkz
    - /AI/
  script:
    # 清理cache
    - pnpm store prune
