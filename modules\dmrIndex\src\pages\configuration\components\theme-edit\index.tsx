import React, { useEffect, useState } from 'react';
import './index.less';
import {
  Button,
  Empty,
  Form,
  message,
  Modal,
  Popover,
  Spin,
  TableProps,
} from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import { DmrConfigurationConstants } from '@/pages/configuration/constants';
import { Sketch } from '@uiw/react-color';
import cloneDeep from 'lodash/cloneDeep';
import { UniSelect } from '@uni/components/src';
import { saveGlobalDmrThemeConfig } from '@/pages/dmr/network/save';
import { useRequest } from 'umi';
import { getDmrSavedMenu, getDmrSavedTheme } from '@/pages/dmr/network/get';
import { RespVO } from '@uni/commons/src/interfaces';
import { topMenuKeys } from '@/pages/dmr';
import { isEmptyValues } from '@uni/utils/src/utils';

interface DmrThemeEditProps {}

const dmrThemeEditLabelKeyMap = {
  '--form-background-color': '背景颜色',
  '--input-font-color': '输入框字体颜色',
  '--input-font-weight': '输入框字体粗细',
  '--form-label-font-weight': '标识文案字体粗细',
  '--form-label-font-color': '标识文案字体颜色',
  '--form-border-color': '边框颜色',
  '--separator-color': '分隔线颜色',
  '--operation-font-color': '手术类型-手术颜色',
  '--operation-intervention-font-color': '手术类型-介入治疗颜色',
  '--operation-treatment-font-color': '手术类型-治疗操作颜色',
  '--operation-diagnosis-font-color': '手术类型-诊断性治疗颜色',

  '--focus-item-background-color': '聚焦项底色',
  '--focus-item-border-color': '聚焦项边框色',
  '--table-focus-item-left-border-color': '表格聚焦行左侧提示色',
  '--table-focus-item-background-color': '表格聚焦行底色',
};

const optionsMap = {
  '--input-font-weight': [
    {
      label: '正常',
      value: 'normal',
    },
    {
      label: '粗体',
      value: 'bold',
    },
    {
      label: '细体',
      value: 'ligher',
    },
  ],
  '--form-label-font-weight': [
    {
      label: '正常',
      value: 'normal',
    },
    {
      label: '粗体',
      value: 'bold',
    },
    {
      label: '细体',
      value: 'ligher',
    },
  ],
};

const DmrThemeEdit = (props: DmrThemeEditProps) => {
  const [themeEditOpen, setThemeEditOpen] = React.useState<boolean>(false);

  const [originThemeMap, setOriginThemeMap] = useState({});
  const [currentThemeMap, setCurrentThemeMap] = useState({});

  const [form] = Form.useForm();

  useEffect(() => {
    dmrGetSavedThemeReq();
  }, []);

  useEffect(() => {
    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_THEME_EDIT,
      (status) => {
        setThemeEditOpen(status);
      },
    );

    return () => {
      Emitter.off(DmrConfigurationConstants.DMR_CONFIGURATION_THEME_EDIT);
    };
  }, []);

  const { loading: dmrGetSavedThemeLoading, run: dmrGetSavedThemeReq } =
    useRequest(
      () => {
        return getDmrSavedTheme();
      },
      {
        manual: true,
        formatResult: async (response: RespVO<any>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            dmrThemeSetter(response?.data?.DmrTheme);
          } else {
            dmrThemeSetter(undefined);
          }
        },
      },
    );

  const dmrThemeSetter = (savedTheme: any) => {
    let currentThemeMap = {};
    Object.keys(dmrThemeEditLabelKeyMap)?.map((key: string) => {
      currentThemeMap[key] = (
        savedTheme?.[key] ??
        window
          ?.getComputedStyle(document?.getElementById('dmr-form-container'))
          ?.getPropertyValue(key)
      )
        ?.replaceAll('"', '')
        ?.trim();
    });

    setCurrentThemeMap(currentThemeMap);
    setOriginThemeMap(cloneDeep(currentThemeMap));
  };

  return (
    <Modal
      title="主题颜色自定义"
      open={themeEditOpen}
      width={400}
      onCancel={(event) => {
        // 还原
        Object.keys(originThemeMap)?.forEach((key) => {
          document
            ?.getElementById('dmr-form-container')
            .style?.setProperty(key, originThemeMap[key]);
        });

        setCurrentThemeMap(cloneDeep(originThemeMap));
        setThemeEditOpen(false);
      }}
      onOk={() => {
        // 保存 并 更新
        saveGlobalDmrThemeConfig(currentThemeMap);
        message.success('更新成功');

        setThemeEditOpen(false);
      }}
      destroyOnClose={true}
      wrapClassName={'theme-edit-container'}
      okText={'确定'}
      cancelText={'取消'}
      getContainer={document.getElementById('dmr-configuration-wrapper')}
    >
      <DmrThemeEditContent
        originTheme={originThemeMap}
        theme={currentThemeMap}
        setTheme={setCurrentThemeMap}
      />
    </Modal>
  );
};

interface DmrThemeEditContentProps {
  originTheme: any;
  theme: any;
  setTheme: any;
}

interface DmrColorEditorProps {
  colorKey?: string;
  color?: string;

  onColorChange: (key: string, color: string) => void;
  onColorReset: (key: string) => void;
}

const DmrColorEditor = (props: DmrColorEditorProps) => {
  return (
    <div className={'color-selector-container'}>
      <Sketch
        color={props?.color}
        disableAlpha={true}
        onChange={(color) => {
          props?.onColorChange(props?.colorKey, color.hex);
        }}
      />
      {/*<Button*/}
      {/*  size="small"*/}
      {/*  type="primary"*/}
      {/*  onClick={(event) => {*/}
      {/*    props?.onColorReset(props?.colorKey);*/}
      {/*  }}*/}
      {/*  className={'reset'}*/}
      {/*>*/}
      {/*  重置*/}
      {/*</Button>*/}
    </div>
  );
};

interface DmrThemeSelectorProps {
  themeKey?: string;
  value?: string;

  onSelectorChange: (key: string, value: string) => void;
  onSelectorReset: (key: string) => void;
}

const DmrThemeSelector = (props: DmrThemeSelectorProps) => {
  return (
    <UniSelect
      value={props?.value?.replaceAll('"', '')}
      dataSource={optionsMap[props?.themeKey]}
      onSelect={(value) => {
        props?.onSelectorChange(props?.themeKey, value);
      }}
    />
  );
};

const DmrThemeEditContent = (props: DmrThemeEditContentProps) => {
  const onSelectItemChange = (colorKey: string, color: string) => {
    document
      ?.getElementById('dmr-form-container')
      .style?.setProperty(colorKey, color);
    props?.setTheme({
      ...props?.theme,
      [colorKey]: color,
    });
  };

  const onSelectItemReset = (colorKey: string) => {
    let originColor = props?.originTheme?.[colorKey];
    onSelectItemChange(colorKey, originColor);
  };

  return (
    <div
      id={'theme-edit-content-container'}
      className={'theme-edit-content-container'}
    >
      {Object.keys(dmrThemeEditLabelKeyMap)?.map((key: string) => {
        let themeItem = props?.theme?.[key];

        return (
          <div className={'theme-color-item-container'}>
            <span className={'color-edit-label'}>
              {dmrThemeEditLabelKeyMap[key]}：
            </span>
            {themeItem?.startsWith('#') ? (
              <Popover
                content={
                  <DmrColorEditor
                    colorKey={key}
                    color={themeItem}
                    onColorChange={onSelectItemChange}
                    onColorReset={onSelectItemReset}
                  />
                }
                trigger="click"
                getPopupContainer={(triggerNode) =>
                  document.getElementById('theme-edit-content-container')
                }
              >
                {/* 颜色 */}
                {document?.getElementById('dmr-form-container') && (
                  <div
                    className={'flex-row-center'}
                    style={{ width: '25%', cursor: 'pointer' }}
                  >
                    <div
                      style={{ backgroundColor: `${themeItem}` }}
                      className={'color-indicator'}
                    />
                    <span className={'color-selector'}>{themeItem}</span>
                  </div>
                )}
              </Popover>
            ) : (
              <div style={{ width: '25%' }}>
                <DmrThemeSelector
                  themeKey={key}
                  value={themeItem}
                  onSelectorChange={onSelectItemChange}
                  onSelectorReset={onSelectItemReset}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default DmrThemeEdit;
