import { useEffect, useRef, useState } from 'react';
import { insurMetaDataService } from '@uni/services/src';
import GroupTreeSelect from '../../components/GroupTreeSelect';
import {
  message,
  Card,
  Switch,
  Tag,
  TableProps,
  Tooltip,
  Space,
  Input,
  Checkbox,
  Select,
} from 'antd';
import { debounce } from 'lodash';
import { useRequest } from 'umi';
import TreeCtrlTable from '@uni/components/src/tree-ctrl-table';
import { IGetQualityCheckRuleTypeTree } from './interface';
import { uniCommonService } from '@uni/services/src';
import { DictionaryItem, RespVO } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import IconBtn from '@uni/components/src/iconBtn';
import { useModel } from '@@/plugin-model/useModel';
import { dataTableFilterSorterHandler } from '@/utils/utils';
// 引入自定义组件
import TestRuleModal from './components/TestRuleModal';
import RuleSettingsModal from './components/RuleSettingsModal';
import { SorterResult } from 'antd/lib/table/interface';
import { CheckErrorType } from '@/constants';
import { useUpdateEffect } from 'ahooks';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';

enum QualityCheckCategory {
  Custom = '0',
  CodeReview = '9',
}

const { CheckableTag } = Tag;

interface TreeNodeType {
  key: string;
  title: string;
  name: string;
  value: number;
  children?: TreeNodeType[];
}

interface TreeDataType {
  [propName: string]: number | TreeNodeType[];
  treeDataResult: TreeNodeType[];
}

interface TreeKeySelectedType {
  key: string;
  title?: string;
  node?: any;
}

interface SettingsModalData {
  record: any;
  visible: boolean;
}

interface SearchParams {
  keyword?: string;
  ruleTypes?: string[];
  subTypes?: string[];
  isCodeReviewRule?: boolean | null;
}

const translateCodeReview = ({ Custom, CodeReview }) => {
  if (Custom && CodeReview) {
    return null;
  } else if (Custom && !CodeReview) {
    return false;
  } else if (!Custom && CodeReview) {
    return true;
  }
};

const Rule = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [selectErrorLevelDataSource, setSelectErrorLevelDataSource] = useState<
    any[]
  >([]);

  const [keyword, setKeyword] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<string[]>([]);
  const [isCodeReviewRule, setIsCodeReviewRule] = useState({
    Custom: true,
    CodeReview: true,
  });
  const [settingsModalData, setSettingsModalData] = useState<SettingsModalData>(
    {
      record: null,
      visible: false,
    },
  );
  const [treeData, setTreeData] = useState<TreeDataType | undefined>(undefined);
  const [treeKeySelected, setTreeKeySelected] = useState<
    TreeKeySelectedType | undefined
  >({
    key: 'key-all',
    title: '全部规则',
  });

  const [qualityCheckRulesData, setQualityCheckRulesData] = useState<any[]>([]);

  // 测试规则Modal
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [testModalTitle, setTestModalTitle] = useState<string | undefined>(
    undefined,
  );

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });
  const errorLevelTooltipProcessor = (item: any, editRuleCode: string) => {
    if (item?.RuleCode === editRuleCode) {
      let preErrorLevelItem = item?.Picks?.find(
        (item) => item?.RuleTemplate === 'Dmr-Review-Pre',
      );
      let postErrorLevelItem = item?.Picks?.find(
        (item) => item?.RuleTemplate === 'Dmr-Review-Post',
      );

      // 当且仅当都存在的时候
      if (preErrorLevelItem && postErrorLevelItem) {
        if (preErrorLevelItem?.ErrorLevel > postErrorLevelItem?.ErrorLevel) {
          preErrorLevelItem['tooltipLabel'] = '初审等级高于复审等级，请确认';
        } else {
          preErrorLevelItem['tooltipLabel'] = '';
        }
      }
    }
  };

  const {
    data: qualityCheckRulesInitData,
    loading: getQualityCheckRulesLoading,
    run: getQualityCheckRulesReq,
  } = useRequest(
    (data, current, pageSize, other = {}) => {
      return uniCommonService(
        `Api/Sys/QualitySys/GetQualityCheckRuleWithPicksByPages`,
        {
          method: 'POST',
          data: {
            ...data,
            DtParam: {
              Draw: 1,
              Start: (current - 1) * pageSize,
              Length: pageSize,
              ...other,
            },
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setQualityCheckRulesData(
            res.data?.data?.map((d) => ({ ...d, uuid: uuidv4() })),
          );
          setBackPagination({
            ...backPagination,
            total: res?.data?.recordsTotal || 0,
          });
        } else {
          setQualityCheckRulesData([]);
        }
      },
    },
  );

  // 将selectedScenario转换为RuleTemplateErrorLevels格式
  const transformSelectedScenario = (values: string[]) => {
    // 创建一个映射来聚合相同RuleTemplate的值
    const templateMap: { [key: string]: string[] } = {};

    // 处理每个选中的值
    values.forEach((value) => {
      if (value && value.includes('|')) {
        const [template, errorLevel] = value.split('|');

        // 将"提示性"映射为"2"，"强制性"映射为"5"
        let levelValue = errorLevel === '提示性' ? '2' : '5';

        if (!templateMap[template]) {
          templateMap[template] = [];
        }
        templateMap[template].push(levelValue);
      }
    });

    // 转换为目标格式
    return Object.keys(templateMap).map((template) => ({
      RuleTemplate: template,
      ErrorLevels: templateMap[template],
    }));
  };

  const getRequestParams = (page = 1, pageSize = backPagination.pageSize) => {
    // 转换selectedScenario为所需格式
    const ruleTemplateErrorLevels = transformSelectedScenario(selectedScenario);

    return {
      params: {
        keyword,
        ruleTypes:
          !treeKeySelected?.key || treeKeySelected?.key === 'key-all'
            ? undefined
            : [
                treeKeySelected?.key?.includes('|')
                  ? treeKeySelected.key.split('|')[0]
                  : (treeKeySelected?.key as string),
              ],
        subTypes: treeKeySelected?.key?.includes('|')
          ? [treeKeySelected.key.split('|')[1]]
          : undefined,
        isCodeReviewRule: translateCodeReview(isCodeReviewRule),
        tags: selectedTags,
        RuleTemplateErrorLevels:
          ruleTemplateErrorLevels.length > 0
            ? ruleTemplateErrorLevels
            : undefined,
      },
      page,
      pageSize,
    };
  };

  // Debounced keyword search handler
  const debouncedSearch = useRef(
    debounce((value: string) => {
      setKeyword(value);
    }, 300),
  ).current;

  // Handle code review type changes
  const handleCodeReviewTypeChange = (
    type: 'homePage' | 'codeReview',
    checked: boolean,
  ) => {
    // 获取当前要更新的字段名
    const fieldName = type === 'homePage' ? 'Custom' : 'CodeReview';
    // 如果用户尝试取消选中，且另一个已经是 false，则阻止操作
    if (
      !checked &&
      !isCodeReviewRule[type === 'homePage' ? 'CodeReview' : 'Custom']
    ) {
      message.warning('至少需要选择一个选项');
      return;
    }
    // 更新状态
    setIsCodeReviewRule((prevState) => ({
      ...prevState,
      [fieldName]: checked,
    }));
  };

  // keyword && IsCodeReviewRule && ruleType subType && tags && selectedScenario 变化时的统一处理
  useUpdateEffect(() => {
    setBackPagination((prev) => ({ ...prev, current: 1 }));
    const { params, page, pageSize } = getRequestParams(1);
    getQualityCheckRulesReq(params, page, pageSize);
  }, [
    keyword,
    isCodeReviewRule,
    treeKeySelected,
    selectedTags,
    selectedScenario,
  ]);

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    const { params, page, pageSize } = getRequestParams(
      pagi.current,
      pagi.pageSize,
    );
    getQualityCheckRulesReq(
      params,
      page,
      pageSize,
      dataTableFilterSorterHandler([], filters, sorter as SorterResult<any>),
    );
  };

  const {
    data: qualityCheckRulesColumnsData,
    mutate: mutateColumns,
    loading: getQualityCheckRulesColumnsLoading,
    run: getQualityCheckRulesColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(
        `Api/Sys/QualitySys/GetQualityCheckRuleWithPicksByPages`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(ruleColumns, res?.data?.Columns);
        }
      },
    },
  );

  // ruletype - subtype
  const {
    data: qualityCheckRuleTypeTree,
    loading: getQualityCheckRuleTypeTreeLoading,
    run: getQualityCheckRuleTypeTreeReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Sys/QualitySys/GetQualityCheckRuleTypeTree`, {
        method: 'POST',
      }),
    {
      manual: true,
      formatResult: (res: RespVO<IGetQualityCheckRuleTypeTree>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // Transform the data into tree structure
          if (res.data) {
            // Transform and sort the data
            const treeNodes = res.data.RuleTypes.map((ruleType) => ({
              key: ruleType.RuleType,
              title: ruleType.RuleType,
              name: ruleType.RuleType,
              value: ruleType.RuleTypeCnt,
              children: ruleType.SubTypes.map((subType) => ({
                key: `${ruleType.RuleType}|${subType.SubType}`,
                title: subType.SubType,
                name: subType.SubType,
                value: subType.SubTypeCnt,
              })).sort((a, b) => b.value - a.value), // Sort SubTypes by count
            })).sort((a, b) => b.value - a.value); // Sort RuleTypes by count

            setTreeData({
              totalCount: res.data.RuleCnt,
              treeDataResult: [
                {
                  key: 'key-all',
                  title: '全部规则',
                  name: '全部规则',
                  value: res.data.RuleCnt,
                  children: treeNodes,
                },
              ],
            });
          }
          return res.data;
        }
      },
    },
  );

  // tags
  const {
    data: qualityCheckRuleTagsData,
    loading: getQualityCheckRuleTagsLoading,
    run: getQualityCheckRuleTagsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Sys/QualitySys/GetQualityCheckRuleTags`, {
        method: 'POST',
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.map((d) => ({ tag: d }));
        }
      },
    },
  );

  // 适用场景 - 可编辑模板
  const {
    data: editableTemplatesData,
    loading: getEditableTemplatesLoading,
    run: getEditableTemplatesReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Sys/QualitySys/GetEditableTemplates`, {
        method: 'POST',
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 将数据转换为TreeSelect需要的格式
          const templateGroups = _.groupBy(res.data, 'RuleTemplate');
          const treeData = Object.keys(templateGroups).map((template) => {
            const group = templateGroups[template];
            return {
              title: group[0].Description,
              value: group[0].RuleTemplate,
              key: group[0].RuleTemplate,
              children: [
                {
                  title: `提示性`,
                  value: `${group[0].RuleTemplate}|提示性`,
                  key: `${group[0].RuleTemplate}|提示性`,
                  parentDescription: group[0].Description,
                },
                {
                  title: `强制性`,
                  value: `${group[0].RuleTemplate}|强制性`,
                  key: `${group[0].RuleTemplate}|强制性`,
                  parentDescription: group[0].Description,
                },
              ],
            };
          });
          return treeData;
        }
        return [];
      },
    },
  );

  // UpdateQualityCheckPick
  const {
    data: updateQualityCheckPickData,
    loading: getUpdateQualityCheckPickLoading,
    run: getUpdateQualityCheckPickReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckPick`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      onSuccess: (res, params) => {
        if (qualityCheckRulesData?.length) {
          let param = params?.length ? params[0] : null;
          if (
            param &&
            param?.RuleCode &&
            param?.RuleTemplate &&
            param?.AppCode
          ) {
            qualityCheckRulesData.forEach((item) => {
              item.Picks.forEach((pick) => {
                if (
                  pick?.RuleCode === param.RuleCode &&
                  pick?.RuleTemplate === param.RuleTemplate &&
                  pick?.AppCode === param.AppCode
                ) {
                  pick.IsValid = param?.IsValid;
                  pick.ErrorLevel = param?.ErrorLevel;
                }
              });

              errorLevelTooltipProcessor(item, param.RuleCode);
            });

            console.log('qualityCheckRulesData', qualityCheckRulesData);
          }
        }
      },
      onError: (res: RespVO<any>) => {
        message.error('修改失败，请联系管理员');
      },
    },
  );

  const {
    data: updateQualityCheckRuleData,
    loading: getUpdateQualityCheckRuleLoading,
    run: getUpdateQualityCheckRuleReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckRule`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>, params) => {},
      onError: (res: RespVO<any>) => {
        message.error('修改失败，请联系管理员');
      },
    },
  );

  // UpdateQualityCheckCategory
  const { run: updateQualityCheckCategoryReq } = useRequest(
    (data: { ruleCode: string; checkCategory: string }) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckCategory`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>, params) => {
        setSettingsModalData((prev) => ({
          ...prev,
          record: {
            ...prev.record,
            CheckCategory: params?.at(0)?.checkCategory,
          },
          hasChanges: true,
        }));
      },
      onError: (res: RespVO<any>) => {
        message.error('修改失败，请联系管理员');
      },
    },
  );

  const handleSettingsModalClose = (hasChanged) => {
    if (hasChanged) {
      const { params, page, pageSize } = getRequestParams(
        backPagination.current,
      );
      getQualityCheckRulesReq(params, page, pageSize);
    }
    setSettingsModalData({
      record: null,
      visible: false,
    });
  };

  // 测试规则的API请求逻辑已移至TestRuleModal组件中

  const dataSourceProcessor = (dataSource: any[]) => {
    let selectErrorLevelDataSource = [];
    dataSource.slice().forEach((item) => {
      let selectDataItem = {
        ...item,
      };

      selectDataItem['title'] = `${selectDataItem['Name']}`;

      selectErrorLevelDataSource.push(selectDataItem);
    });

    return selectErrorLevelDataSource;
  };

  const getSelectDataSource = async (module) => {
    let response: RespVO<DictionaryItem[]> = await insurMetaDataService(module);

    if (response?.code === 0) {
      if (response?.statusCode === 200) {
        setSelectErrorLevelDataSource(dataSourceProcessor([...response?.data]));
        return;
      }
    }

    setSelectErrorLevelDataSource([]);
  };

  useEffect(() => {
    getQualityCheckRuleTypeTreeReq();
    getQualityCheckRuleTagsReq();
    getEditableTemplatesReq(); // 获取适用场景数据
    setSelectErrorLevelDataSource(dataSourceProcessor(CheckErrorType));
    getQualityCheckRulesColumnsReq();

    // 默认是全部不选中，设置为空数组
    setSelectedScenario([]);

    // 初始化请求数据
    const { params, page, pageSize } = getRequestParams(1, 10);
    getQualityCheckRulesReq(params, page, pageSize);
  }, []);

  const ruleColumns = [
    {
      dataIndex: 'Options',
      title: '操作',
      align: 'center',
      width: 60,
      fixed: 'left',
      visible: true,
      render: (text, record, index) => {
        return (
          <Space>
            <IconBtn
              type="edit"
              onClick={() => {
                let editingRecord = _.cloneDeep(record);
                errorLevelTooltipProcessor(
                  editingRecord,
                  editingRecord.RuleCode,
                );
                setSettingsModalData({
                  record: editingRecord,
                  visible: true,
                });
              }}
            />
            {(globalState?.userInfo?.Roles ?? [])?.includes('Admin') && (
              <IconBtn
                type="test"
                onClick={() => {
                  setTestModalTitle(record?.DisplayErrMsg);
                  setTestModalVisible(true);
                }}
              />
            )}
          </Space>
        );
      },
    },
    {
      dataIndex: 'RuleType',
      align: 'center',
      // filterType: 'filter',
      render: (_, record, index) => {
        return <Tag color={'blue'}>{_}</Tag>;
      },
    },
    {
      dataIndex: 'Tags',
      render: (text, record, index) => {
        return text?.map((tag) => {
          return <Tag key={tag}>{tag}</Tag>;
        });
      },
    },
    {
      visible: true,
      align: 'center',
      fixed: 'right',
      width: 100,
      dataIndex: 'IsValid',
      render: (_, record, index) => {
        return (
          <Switch
            defaultChecked={_ || false}
            onChange={(value) => {
              getUpdateQualityCheckRuleReq({
                RuleCode: record.RuleCode,
                IsValid: value,
              });
            }}
          />
        );
      },
    },
    {
      dataIndex: 'Picks',
      visible: false,
    },
    {
      dataIndex: 'CliDeptPicks',
      visible: false,
      // render: (text, record, index) => {
      //   return text?.map((tag) => {
      //     return <Tag key={tag?.}>{tag}</Tag>;
      //   });
      // },
    },
  ];

  // 已将 picksColumns 函数移至 RuleSettingsModal 组件中

  const handleTreeSelect = (keys: React.Key[], info: any) => {
    const { node } = info;
    setTreeKeySelected({
      key: node.key,
      title: node.title,
    });
  };

  return (
    <>
      <Card
        title={'质控审核规则配置'}
        className={'rule_list_tree_ctrl_table_container'}
        bordered={false}
      >
        <TreeCtrlTable
          className={'rule_list_tree_ctrl_table'}
          treeData={{
            title: '规则分类',
            subTitle: '规则总数',
            subKey: 'totalCount',
            data: treeData,
          }}
          treeLoading={getQualityCheckRuleTypeTreeLoading}
          treeAction={{
            onSelect: handleTreeSelect,
          }}
          tableData={{
            tableTitle: (
              <Space>
                <Tooltip title={treeKeySelected?.title}>
                  <div
                    style={{
                      fontSize: '16px',
                      maxWidth: '300px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {treeKeySelected?.title}
                  </div>
                </Tooltip>
                <Input.Search
                  placeholder="搜索关键字"
                  style={{ width: 200 }}
                  allowClear
                  onChange={(e) => debouncedSearch(e.target.value)}
                />
              </Space>
            ),
            tableTitleExtra: (
              <Space>
                <span>
                  标签：
                  <Select
                    options={qualityCheckRuleTagsData ?? []}
                    fieldNames={{ label: 'tag', value: 'tag' }}
                    style={{ width: '180px' }}
                    allowClear
                    mode="multiple"
                    maxTagCount={1}
                    placeholder="请选择标签"
                    value={selectedTags}
                    onChange={(value) => {
                      setSelectedTags(value);
                    }}
                  />
                </span>
                <span>
                  适用场景：
                  <GroupTreeSelect
                    treeData={editableTemplatesData || []}
                    delayedChange
                    value={selectedScenario}
                    onChange={setSelectedScenario}
                    placeholder="请选择适用场景"
                    loading={getEditableTemplatesLoading}
                    showAllSelectButton={false}
                    quickSelectButtons={[
                      {
                        text: '全部提示',
                        filter: (value) => value.includes('|提示性'),
                      },
                      {
                        text: '全部强制',
                        filter: (value) => value.includes('|强制性'),
                      },
                    ]}
                    style={{ width: '250px' }}
                  />
                </span>
                <Checkbox
                  checked={isCodeReviewRule.Custom}
                  onChange={(e) =>
                    handleCodeReviewTypeChange('homePage', e.target.checked)
                  }
                >
                  首页规则
                </Checkbox>
                <Checkbox
                  checked={isCodeReviewRule.CodeReview}
                  onChange={(e) =>
                    handleCodeReviewTypeChange('codeReview', e.target.checked)
                  }
                >
                  编码规则
                </Checkbox>
              </Space>
            ),
            columnEdit: {
              columnInterfaceUrl: `Api/Sys/QualitySys/GetQualityCheckRuleWithPicksByPages`,
              onTableRowSaveSuccess: (newColumns) => {
                mutateColumns(
                  tableColumnBaseProcessor(ruleColumns, newColumns),
                );
              },
            },
            columns: qualityCheckRulesColumnsData,
            dataSource: qualityCheckRulesData,
            rowKey: 'uuid',
            id: 'chs-quality-check-rules-table',
            loading: getQualityCheckRulesLoading,
            pagination: backPagination,
            onChange: backTableOnChange,
            dictionaryData: globalState?.dictData,
            scroll: { x: 'max-content' },
          }}
        />
      </Card>

      {/* 规则设置Modal组件 */}
      <RuleSettingsModal
        visible={settingsModalData.visible}
        record={settingsModalData.record}
        onClose={handleSettingsModalClose}
        selectErrorLevelDataSource={selectErrorLevelDataSource}
        onUpdateRule={getUpdateQualityCheckRuleReq}
        onUpdateCategory={updateQualityCheckCategoryReq}
        onUpdatePick={getUpdateQualityCheckPickReq}
        cliDepts={globalState?.dictData?.CliDepts}
      />

      {/* 测试规则Modal组件 */}
      <TestRuleModal
        visible={testModalVisible}
        title={testModalTitle}
        onCancel={() => {
          setTestModalVisible(false);
          setTestModalTitle(undefined);
        }}
        qualityCheckRulesData={qualityCheckRulesData}
        globalState={globalState}
      />
    </>
  );
};
export default Rule;
