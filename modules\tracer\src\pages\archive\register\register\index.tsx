import React, { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import {
  Alert,
  Button,
  Card,
  Col,
  Divider,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Spin,
  Tooltip,
  message,
} from 'antd';
import { useSafeState, useKeyPress } from 'ahooks';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { SwagTraceRecordItem } from '../../interface';
import { columnsHandler, handleMrActionApi, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, SigninType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import './index.less';
import {
  ProForm,
  ProFormDependency,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { exportExcel } from '@uni/utils/src/excel-export';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  FileExcelOutlined,
  PrinterOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { archiveColumns, modalSelectedColumns } from '../../columns';
import { ActionRecordItem } from '@/pages/mrRoom/interface';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { QueryParameterConfigurationEditButton } from '@uni/components/src/query-configuration';
import qs from 'qs';

const ArchiveSignin = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  const proFormRef = useRef<ProFormInstance>();

  // 已归档列表
  const [RegisteredTable, RegisteredTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagTraceRecordItem[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });

  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  const barCodeRef = useRef<InputRef>(null);

  // 节流隐式标识
  const hiddenLoading = useRef(false);
  // 设计思路是先判断是不是相同的 不是就没事 是就进debounce判断
  const newestBarCode = useRef(undefined);

  // 查询结果处理
  const searchResultHandler = (params, res, needDataPush) => {
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        // Api/Mr/TraceRecord/GetList
        resData = res?.data?.Items?.slice();
      } else {
        // Api/Mr/TraceRecord/GetListByBarCode
        resData = res?.data?.slice();
      }
      if (!needDataPush) {
        if (resData?.length === 1) {
          // 单个，直接处理
          reqActionReq(
            { ...params, BarCode: resData?.at(0)?.BarCode },
            null,
            ReqActionType.warehouseSignIn,
          );
        } else if (resData?.length > 1) {
          // 多条，modal提示处理
          ModalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: resData,
              specialData: res?.data?.Items ? params : params?.AutoBarCode,
              actionType: undefined,
            },
          });
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;

          Modal.confirm({
            title: `查无数据`,
            content: '请确认病案标识填写正确',
            onCancel: () => {
              proFormRef.current.resetFields(['AutoBarCode']);
              focusBarCode();
            },
            onOk: () => {
              proFormRef.current.resetFields(['AutoBarCode']);
              focusBarCode();
            },
            cancelButtonProps: { style: { display: 'none' } },
          });
        }
      } else {
        // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
        // 根据免打标签 自动打印
        if (!proFormRef.current.getFieldValue('NeedAutoPrint')) {
          fetchPrint(resData?.at(0));
        }
        // 查询成功后，把数据插入已记录列表
        RegisteredTableDispatch({
          type: TableAction.dataUnshift,
          payload: {
            data: Array.isArray(resData) ? resData : [resData],
            key: 'BarCode',
          },
        });
        // 重置节流标识
        hiddenLoading.current = false;
        // focus input
        focusBarCode();
      }
    } else {
      // 重置节流标识
      hiddenLoading.current = false;
    }
  };

  // 批量查询，查询→操作→查询(只有归档需要再次查询，以获取上架号)
  const searchOneReq = async (data: any, needDataPush = false) => {
    if (!data) return;

    // 直接使用传入的参数对象，添加分页参数
    const requestData = {
      ...data,
      SkipCount: 0,
      MaxResultCount: 999999,
    };

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(data, res, needDataPush);
  };

  // 特殊debounce 扫码枪才需要
  const handleBarCodeDebounce = (barCode) => {
    if (RegisteredTable?.data?.findIndex((d) => d?.BarCode === barCode) > -1) {
      // 判断时间 存在与表内 长于 特定时间，比如 2s
      if (
        dayjs().diff(
          RegisteredTable?.data?.find((d) => d?.BarCode === barCode)
            ?.InsertTime,
        ) < 2000
      ) {
        // 拦截
        hiddenLoading.current = false;
        proFormRef?.current.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;

    // 扫之前，先做判断 判断条码号与上一次扫的 & 表格内的 是否相符 如果能匹配出来则特殊报错
    if (handleBarCodeDebounce(params?.AutoBarCode)) {
      // 拦截
      return;
    }

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.AutoBarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(params, res, needDataPush);
  };

  // 现在变成了操作记录数据的table用
  // 扫码枪输入的是一个barCode，然后点签收 这一个操作, 当操作成功时，在调Get接口单独获取这个病案信息，再在右边显示出来
  // 正常输入则是用于查询，查询的数据
  // **现在的逻辑顺序：
  /**
   * 第一先拿BarCode去查询Get，获取一个list（searchOneReq）
   * 如果只有一条那么就正常继续走归档登记（reqActionReq）
   * 等归档登记走完后，为了获取上架号，必须再重新用BarCode去查询Get，获取一个list，并且里面只有一条数据（searchOneReq）
   * 最后将那条数据插入table内
   */
  const reqActionReq = async (
    data: any,
    item = null,
    reqType: ReqActionType,
  ) => {
    if (!data || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Tracing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Tracing/${reqType}`,
          method: 'POST',
          data,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 不管结果 重置节流标识
    hiddenLoading.current = false;

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.warehouseSignIn) {
        // 把modal关闭
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setSelectedRecordKey([]);
        setModalAlert(false);

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        result?.isCorrect
          ? message.success('归档成功')
          : message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          RegisteredTableDispatch({
            type: TableAction.dataUnshiftUniq,
            payload: {
              data: {
                ...(Array.isArray(result?.data)
                  ? result?.data?.at(0)
                  : result?.data),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
              overWriteBy: {
                key: 'isCorrect',
                value: true,
              },
            },
          });
        }

        if (result?.errType === '404') {
          // 404
          Modal.error({
            title: result?.errMsg?.join('。/n'),
            onOk: () => {
              proFormRef.current.resetFields(['AutoBarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['AutoBarCode']);
              focusBarCode();
            },
          });
        } else {
          // 其他的 barCode自动清除
          proFormRef.current.resetFields(['AutoBarCode']);
          // 根据免打标签 自动打印
          if (!proFormRef.current.getFieldValue('NeedAutoPrint')) {
            fetchPrint(
              Array.isArray(result.data) ? result.data?.at(0) : result.data,
            );
          }
          // focus input
          focusBarCode();
        }
      } else if (reqType === ReqActionType.warehouseRevertSignin) {
        message.success('撤销成功');

        // revert
        RegisteredTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: item?.BarCode,
          },
        });

        setRevertRecord(item);
      }
    }
  };

  // 处理撤销后的操作(因为useMemo的缘故，得effect处理...蛮蠢的，但是又懒得改useMemo的写法)
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (RegisteredTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      RegisteredTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  const RegisteredTableColumnsSolver = useMemo(() => {
    return RegisteredTable.columns?.length > 0
      ? columnsHandler(RegisteredTable.columns, {
          dataIndex: 'option',
          title: '操作',
          visible: true,
          width: 60,
          align: 'center',
          valueType: 'option',
          fixed: 'right',
          render: (
            text,
            record: SwagTraceRecordItem & { isCorrect: boolean },
          ) => (
            <Space>
              {record?.isCorrect && (
                <>
                  <Tooltip title="打印">
                    <PrinterOutlined
                      className="icon_blue-color"
                      onClick={(e) => {
                        e.stopPropagation();
                        fetchPrint(record);
                      }}
                    />
                  </Tooltip>
                  <Popconfirm
                    key="revert"
                    title="确定要撤销？"
                    onConfirm={(e) => {
                      e.stopPropagation();
                      reqActionReq(
                        { BarCodes: [record.BarCode] },
                        record,
                        ReqActionType.warehouseRevertSignin,
                      );
                    }}
                    onCancel={(e) => e.stopPropagation()}
                  >
                    <Tooltip title="撤销">
                      <UndoOutlined
                        className="icon_blue-color"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Tooltip>
                  </Popconfirm>
                </>
              )}
            </Space>
          ),
        })
      : [];
  }, [RegisteredTable?.columns]);

  // columns处理
  if (
    columnsList?.['WarehouseSignedIn'] &&
    columnsList?.['TraceRecord'] &&
    RegisteredTable.columns.length < 1
  ) {
    RegisteredTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          archiveColumns,
          columnsList['WarehouseSignedIn'],
        ),
      },
    });
    setModalColumns(
      tableColumnBaseProcessor(
        modalSelectedColumns,
        columnsList['TraceRecord'],
      ),
    );
  }

  // 调接口，逻辑：
  /**
   * 第一先拿BarCode去查询Get，获取一个list（searchOneReq）
   * 如果只有一条那么就正常继续走归档登记（reqActionReq）
   * 等归档登记走完后，为了获取上架号，必须再重新用BarCode去查询Get，获取一个list，并且里面只有一条数据（searchOneReq）
   * 最后将那条数据插入table内
   */
  const autoFetchReqActionReq = ({ AutoBarCode }, type = 'Single') => {
    if (!AutoBarCode) return;
    if (hiddenLoading.current) return;
    hiddenLoading.current = true;
    proFormRef.current
      .validateFields()
      .then((values) => {
        if (values.SignType.value === 'BarCode') {
          searchByBarCodeReq(values, false);
        } else {
          // 对于非BarCode类型，构建参数对象
          const searchParam = {
            [values.SignType.value]: values.AutoBarCode,
          };
          searchOneReq(searchParam, false);
        }
      })
      .catch((err) => {
        hiddenLoading.current = false;
        message.error('请确认已填写所有归档信息！');
      });
  };

  // print barcode
  const fetchPrint = (record) => {
    if (!record) return; // 函数处理包含 "Date" 或 "date" 的字段

    const processDateFields = (obj) => {
      return Object.keys(obj).reduce((acc, key) => {
        // 将首字母转换为小写
        const newKey = key.charAt(0).toLowerCase() + key.slice(1);
        // 如果字段名包含 'Date' 或 'date'
        if (newKey.includes('Date') || newKey.includes('date')) {
          // 对日期字段进行格式化，确保其为有效日期
          acc[newKey] = dayjs(obj[key]).isValid()
            ? dayjs(obj[key]).format('YYYY-MM-DD')
            : obj[key];
        } else {
          // 其他字段保持不变
          acc[newKey] = obj[key] ?? 'null';
        }
        return acc;
      }, {});
    }; // 处理对象中的日期字段

    const formattedRecord = processDateFields(record);

    let res = window.open(
      `unidmr://tagprint?${qs.stringify(formattedRecord)}`,
      '_self',
      'noopener',
    );
    // res.close();
  };

  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      proFormRef.current.validateFields().then((values) => {
        autoFetchReqActionReq(values);
      });
    },
    {
      exactMatch: true,
      target: document.getElementById('archiveRegisterForm'),
    },
  );

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xxl={7} xl={8}>
          <Card
            title={<Space>归档信息</Space>}
            style={{ marginBottom: '15px' }}
          >
            <Spin spinning={loadings['TraceRecord/GetList'] || false}>
              <ProForm
                layout="horizontal"
                className="archive_info_form" // flex-wrap
                id="archiveRegisterForm"
                grid
                labelCol={{ flex: '120px' }}
                wrapperCol={{ flex: 'auto' }}
                formRef={proFormRef}
                submitter={{
                  render: (props, doms) => {
                    return [
                      <Button
                        type="primary"
                        style={{
                          width: 'calc(100% - 90px)',
                          float: 'right',
                          marginTop: '8px',
                        }}
                        key="submit"
                        onClick={() => {
                          props.form.validateFields().then((values) => {
                            autoFetchReqActionReq(values);
                          });
                        }}
                      >
                        归档(Enter)
                      </Button>,
                    ];
                  },
                }}
              >
                <ProFormSelect
                  rules={[{ required: true }]}
                  name="WareHouseNo"
                  label="归档库房"
                  showSearch
                  options={dictData.Warehouse?.map((d) => ({
                    label: d.Name,
                    value: d.Code,
                  }))}
                  fieldProps={{
                    filterOption: (inputValue, option) => {
                      return (
                        (option &&
                          option.label
                            ?.toString()
                            ?.toLowerCase()
                            ?.indexOf(inputValue) !== -1) ||
                        option.value
                          ?.toString()
                          ?.toLowerCase()
                          ?.indexOf(inputValue) !== -1 ||
                        pinyinInitialSearch(
                          option.label?.toString()?.toLowerCase(),
                          inputValue.toLowerCase(),
                        )
                      );
                    },
                  }}
                />
                <ProFormDependency name={['WareHouseNo']}>
                  {({ WareHouseNo }) => {
                    let opts = dictData.InventoryLocation?.filter(
                      (d) => d?.ExtraProperties?.WareHouseCode === WareHouseNo,
                    );
                    // console.log(opts, WareHouseNo, dictData.InventoryLocation?.at(0)?.ExtraProperties);
                    return (
                      <ProFormSelect
                        rules={[{ required: true }]}
                        name="InventoryLocation"
                        label="归档库位"
                        showSearch
                        options={
                          opts?.length > 0
                            ? opts
                            : dictData.InventoryLocation?.filter(
                                (d) => !d?.ExtraProperties?.WareHouseCode,
                              )
                        }
                        fieldProps={{
                          fieldNames: {
                            label: 'Name',
                            value: 'Code',
                          },
                          filterOption: (inputValue, option) => {
                            return (
                              (option &&
                                option.label
                                  ?.toString()
                                  ?.toLowerCase()
                                  ?.indexOf(inputValue) !== -1) ||
                              option.value
                                ?.toString()
                                ?.toLowerCase()
                                ?.indexOf(inputValue) !== -1 ||
                              pinyinInitialSearch(
                                option.label?.toString()?.toLowerCase(),
                                inputValue.toLowerCase(),
                              )
                            );
                          },
                        }}
                      />
                    );
                  }}
                </ProFormDependency>

                <ProFormGroup>
                  <ProFormSelect
                    name="SignType"
                    colProps={{ flex: '120px' }}
                    allowClear={false}
                    initialValue={{
                      label: SigninType[0].title,
                      value: SigninType[0].value,
                    }}
                    fieldProps={{
                      labelInValue: true,
                      fieldNames: {
                        label: 'title',
                        value: 'value',
                      },
                    }}
                    rules={[{ required: true }]}
                    options={SigninType as any[]}
                  />
                  <ProFormDependency name={['SignType']}>
                    {({ SignType }) => {
                      return (
                        <ProFormText
                          colProps={{ flex: 'auto' }}
                          name="AutoBarCode"
                          placeholder={
                            SignType?.value === 'BarCode'
                              ? '条码号(扫码)'
                              : `请输入${SignType?.label}`
                          }
                          fieldProps={{
                            ref: barCodeRef,
                          }}
                          rules={[{ required: true }]}
                        />
                      );
                    }}
                  </ProFormDependency>
                </ProFormGroup>
                <ProFormSwitch
                  name="NeedAutoPrint"
                  label={
                    <span
                      style={{ display: 'inline-block', marginLeft: '11px' }}
                    >
                      免打标签
                    </span>
                  }
                  unCheckedChildren="否"
                  checkedChildren="是"
                />
              </ProForm>
            </Spin>
          </Card>
          <PatTimeline
            item={RegisteredTable?.clkItem}
            loading={loadings['TraceRecord/GetActions']}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            title="病案归档列表"
            extra={
              <Space>
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let exportColumns = RegisteredTableColumnsSolver?.filter(
                      (columnItem) =>
                        columnItem.className?.indexOf('exportable') > -1 &&
                        columnItem.valueType !== 'option' &&
                        columnItem.dataIndex !== 'operation',
                    );
                    exportExcel(
                      exportColumns,
                      exportExcelDictionaryModuleProcessor(
                        exportColumns,
                        _.cloneDeep(
                          RegisteredTable.data?.filter((d) => d.isCorrect),
                        ),
                      ),
                      `病案归档列表__${dayjs().format('YYYY-MM-DD')}`,
                      [],
                    );
                  }}
                  disabled={
                    RegisteredTable.data?.filter((d) => d.isCorrect)?.length < 1
                  }
                >
                  <Tooltip title="导出Excel">
                    <Button
                      type="text"
                      shape="circle"
                      key="export"
                      disabled={
                        RegisteredTable.data?.filter((d) => d.isCorrect)
                          ?.length < 1
                      }
                      icon={<FileExcelOutlined />}
                    ></Button>
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl:
                      'Api/Mr/TraceRecord/WarehouseSignedInList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'WarehouseSignedIn',
                          value: columns,
                        },
                      });
                      RegisteredTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor(
                            archiveColumns,
                            columns,
                          ),
                        },
                      });
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="trace_archive_record"
              rowKey="uuid"
              showSorterTooltip={false}
              loading={
                loadings['TraceRecord/GetList'] ||
                loadings[`Tracing/${ReqActionType.warehouseSignIn}`] ||
                loadings[`Tracing/${ReqActionType.warehouseRevertSignin}`] ||
                false
              }
              columns={RegisteredTableColumnsSolver} // columnsHandler
              dataSource={RegisteredTable.data}
              scroll={{ x: 'max-content' }}
              dictionaryData={dictData}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.uuid === RegisteredTable.clkItem?.uuid) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (RegisteredTable.clkItem?.BarCode !== record?.BarCode) {
                      RegisteredTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
        </Col>
      </Row>
      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            reqActionReq(
              { ...ModalState.specialData, BarCode: selectedRecordKey?.at(0) },
              null,
              ReqActionType.warehouseSignIn,
            );
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.warehouseSignIn}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();
          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['TraceRecord/GetList'] ||
            loadings[`Tracing/${ReqActionType.warehouseSignIn}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagTraceRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
              },
            };
          }}
        />
      </Modal>
    </>
  );
};

export default ArchiveSignin;
