import { getParentRoutesByCurrentRoute } from '@/pages/configuration/utils';
import { menuData } from '@/layouts/menuData';
import { getAllMenusContainsHidden } from '@uni/components/src/menu-sider/utils';
import { MenuData } from '@uni/components/src/menu-sider/MenuSider';
import filterDeep from 'deepdash/filterDeep';

export const menuParamProcessor = (
  menus: string[],
  flattenedMenuData?: any[],
) => {
  let menuParam = {};

  menus.forEach((item) => {
    menuParam[item] = true;

    // 追加两个全屏的
    if (item === '/dmr/index') {
      menuParam['/dmr/index/fullscreen'] = true;
    }

    if (item === '/chs/main/index') {
      menuParam['/chs/main/index/fullscreen'] = true;
    }

    // 子选项 -> 推导父选项
    let currentRoute = flattenedMenuData?.find(
      (menuItem) => menuItem?.route === item,
    );
    if (currentRoute) {
      currentRoute?.parentRoutes?.forEach((parentRoute) => {
        menuParam[parentRoute] = true;
      });
    }
  });

  return menuParam;
};

export const menuProhibitedParamProcessor = (activeMenuParam: any) => {
  let prohibitedMenuParam = {};
  getAllMenusContainsHidden(menuData)?.forEach((item) => {
    if (activeMenuParam[item.route] === undefined) {
      prohibitedMenuParam[item.route] = false;
    }
  });

  return prohibitedMenuParam;
};

// TODO 现在只有两层 后面可能有更多 要改掉
export const subSystemParamProcessor = (subSystems: string[]) => {
  let subSystemParams = {};

  subSystems.forEach((item) => {
    subSystemParams[item] = true;

    // 追加两个全屏的
    if (item === '/dmr') {
      subSystemParams['/dmr/index/fullscreen'] = true;
    }

    if (item === '/chs') {
      subSystemParams['/chs/main/index/fullscreen'] = true;
    }
  });

  // 父选项 -> 推导子选项
  menuData.forEach((menuItem) => {
    if (subSystems.includes(menuItem?.route)) {
      activateChildRoutes(subSystemParams, menuItem);
    }
  });

  return subSystemParams;
};

export const activateChildRoutes = (subSystemParams, menuItem) => {
  menuItem?.children?.forEach((child) => {
    subSystemParams[child?.route] = true;
    if (child?.children) {
      activateChildRoutes(subSystemParams, child);
    }
  });
};

export const roleMenuDataProcessor = (globalMenus: string[]) => {
  return filterDeep(
    menuData,
    (o) => {
      return globalMenus?.includes(o?.route);
    },
    { childrenPath: ['children'] },
  );
};
