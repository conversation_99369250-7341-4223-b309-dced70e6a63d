import { Reducer, useEffect, useReducer, useState, useRef } from 'react';
import { useModel, useRequest } from 'umi';
import { Col, MenuProps, Row, Tabs } from 'antd';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import Stats from '@/components/Stats/index';
import { Emitter } from '@uni/utils/src/emitter';
import {
  CodeValueQuantificationEventConstants,
  CodeValueStats,
  ELEMENT_TYPES,
  ELEMENT_CONFIG,
  API_ENDPOINTS,
  DEFAULT_TAB_KEY,
  DEFAULT_TAB_LABEL,
  OPERATION_TAB,
  ElementType,
  MONTH_TREND_TAB,
} from './constants';
import { RespVO } from '@uni/commons/src/interfaces';
import DetailTableModal from './components/DetailTableModal/index';
import TableStatistic from './components/TableStatistic/index';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { getMonthBoundaries, transformData } from './utils';
import SurgicalWorloadAnalysis from './components/SurgicalWorkloadAnalysis/index';
import CodeStatsEchart from './components/statsEchart';
import { IReducer, IModalState } from '@uni/reducers/src/interface';
import { InitModalState, modalReducer, ModalAction } from '@uni/reducers/src';
import { checkSpecialKey } from './components/TableStatistic/utils';
import SurgicalDetailTableModal from './components/SurgicalDetailTableModal/index';
import StatisticDrawer from './components/StatisticDrawer/index';
import MonthTable from './components/MonthTable/index';

const isOpenDateTrend =
  (window as any).externalConfig?.['qualityControl']?.codeValueQuantification
    ?.isOpenDateTrend ?? false;

const CodeValueQuantification = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState(DEFAULT_TAB_KEY);
  // stat click change
  const [selectedStat, setSelectedStat] = useState<any>({});

  // 明细modal 非手术
  const [ModalState, ModalDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, { ...InitModalState });

  // 手术 modal
  const [SurgicalModalState, SurgicalModalDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, { ...InitModalState });

  // statistic dill down drawer
  const [drillDownDrawerState, setDrillDownDrawerState] = useState({
    open: false,
    record: undefined,
    selectedKey: '',
  });

  // 获取所有 grouperColList
  // 暂时里面缺少 绩效科室  MajorPerfDept
  const {
    data: groupItems,
    loading: groupsLoading,
    run: groupItemsReq,
  } = useRequest(
    () => {
      let data = {
        TableName: 'OmniCard',
      };

      return uniCommonService(API_ENDPOINTS.GET_GROUPER_LIST, {
        params: data,
      });
    },
    {
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return response?.data;
        } else {
          return [];
        }
      },
    },
  );

  // 这边获取的只是类似 columns 的数据
  const {
    data: MetricData,
    loading: getMetricDataLoading,
    run: getMetricDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(API_ENDPOINTS.GET_METRICS, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 先处理下数据
          return res?.data?.map((d) => {
            return {
              ...d,
              label: d?.MenuDirectories?.at(0)?.split('/')?.at(2),
            };
          });
          // return res.data;
        }
        return [];
      },
    },
  );

  // 这边获取的才是 统计 的数据
  const {
    data: StatsData,
    loading: getStatsDataLoading,
    run: getStatsDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(API_ENDPOINTS.GET_STATS, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res?.data?.data?.at(0);
        }
        return [];
      },
    },
  );

  // 添加一个ref来存储上一次的searchParams，用于比较是否真的发生了变化
  const prevSearchParamsRef = useRef(searchParams);

  // 添加一个标记，用于标识正在执行的Modal操作
  const [isModalActive, setIsModalActive] = useState(false);

  // 处理tableParams
  useEffect(() => {
    // 如果searchParams为空，直接返回
    if (!searchParams) {
      return;
    }
    // 如果不是按钮点击，需要检查参数是否真的变化了
    if (searchParams?.triggerSource !== 'btnClick') {
      return;
    }

    let params = {
      Sdate: searchParams?.dateRange?.at(0),
      Edate: searchParams?.dateRange?.at(1),
      HospCode: searchParams?.hospCodes,
      CliDepts: searchParams?.CliDepts,
      MajorPerfDepts: searchParams?.MajorPerfDepts,
    };
    setTableParams(params);
    getMetricDataReq(params);
    // 获取一个total的数据
    getStatsDataReq({
      BasicArgs: params,
      GrouperCols: [],
    });
  }, [searchParams]);

  // 右边chart data
  // 趋势
  const {
    data: statsChangedData,
    loading: getStatsChangedLoading,
    run: getStatsChangedReq,
    fetches: fetchesStatsChanged,
  } = useRequest(
    (id, data) =>
      uniCommonService(API_ENDPOINTS.GET_STATS, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data) {
            // TODO
            return res.data?.data;
          }
        }
        return [];
      },
    },
  );

  // 趋势 Columns
  const { data: statsChangedColumnsData } = useRequest(
    () =>
      uniCommonService(`Api/Drgs/HospCmi/CmiTrend`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    if (
      tableParams &&
      Object.keys(tableParams)?.length &&
      groupItems &&
      groupItems?.length > 0 &&
      selectedStat?.ColumnName
    ) {
      Object.values(ELEMENT_CONFIG).forEach(({ key }) => {
        getStatsChangedReq(key, {
          BasicArgs: tableParams,
          GrouperCols: groupItems?.filter(
            (item) => item?.expr === key || item?.name === key,
          ),
          OutputColumns: [
            { Id: selectedStat?.preData?.Id, customTitle: '编码前' },
            { Id: selectedStat?.Id, customTitle: '编码后' },
          ],
        });
      });
    }
  }, [tableParams, selectedStat, groupItems]);

  // 处理bundleData
  const [bundleData, setBundleData] = useState([]);
  useEffect(() => {
    if (MetricData && MetricData?.length > 0) {
      let result = MetricData?.map((d) => {
        return {
          ...d,
          value: StatsData?.[d?.ColumnName] || undefined,
        };
      });
      // 然后再这边就处理好数据 处理成 CodeValueStats 那种形式
      let groupedData = _.groupBy(
        // _.sortBy(result, ['MenuSort']),
        _.orderBy(result, [
          (item) => (item.MenuSort === 0 ? 1 : 0),
          'MenuSort',
        ]),
        (o) =>
          `${o?.MenuDirectories?.at(0)
            ?.split('/')
            ?.at(1)}/${o?.MenuDirectories?.at(0)?.split('/')?.at(2)}`,
      );
      let transformedData = transformData(groupedData);
      setBundleData(
        transformedData?.map((data) => {
          return {
            ...data,
            value: StatsData?.[data?.ColumnName] || 0,
            preValue: StatsData?.[data?.preData?.ColumnName] || 0,
            diffMetricName:
              StatsData?.[data?.diffData?.ColumnName] || undefined,
            // 还需要一个额外算出来的数据 根据 MetricDirection Positive 则是后-前 越大越好 Negative 则是后-前 越小越好
            computeValue:
              StatsData?.[data?.ColumnName] -
                StatsData?.[data?.preData?.ColumnName] || 0,
          };
        }),
      );
    }
  }, [MetricData, StatsData]);

  // emitter part
  useEffect(() => {
    // 数字点击
    Emitter.on(
      CodeValueQuantificationEventConstants.STAT_NUMBER_CLK,
      (record) => {
        // 根据record内部是 Coder / CliDept / Chief / MedTeam 判断？
        let key = checkSpecialKey(record),
          dictName;
        if (key && ELEMENT_CONFIG[key as keyof typeof ELEMENT_TYPES]) {
          const config = ELEMENT_CONFIG[key as keyof typeof ELEMENT_TYPES];
          dictName =
            dictData?.[config.dictModuleGroup]?.[config.dictModule]?.find(
              (d) => d?.Code === record?.[key],
            )?.Name || record?.[key];
        }
        if (activeKey === 'monthTrendStatistic' && record?.Month) {
          const date = getMonthBoundaries(record?.Month);
          record.specialData = date;
        }
        if (key === ELEMENT_TYPES.MAJOR_PERF_DEPT) {
          const config = ELEMENT_CONFIG[key as keyof typeof ELEMENT_TYPES];
          dictName =
            dictData?.[config.dictModule]?.find(
              (d) => d?.Code === record?.[key],
            )?.Name || record?.[key];
          record.specialData = {
            MajorPerfDepts: record?.MajorPerfDept
              ? [record?.MajorPerfDept]
              : [],
            MajorPerfDept: record?.MajorPerfDept,
          };
        }
        ModalDispatch({
          type: ModalAction.change,
          payload: {
            visible: true,
            record,
            dictName,
          },
        });
      },
    );
    // 外科手术点击
    Emitter.on(
      CodeValueQuantificationEventConstants.SURGICAL_MODAL_OPEN,
      (record) => {
        console.log('SURGICAL_MODAL_OPEN', record);
        SurgicalModalDispatch({
          type: ModalAction.change,
          payload: {
            visible: true,
            record,
          },
        });
      },
    );
    // 卡片下钻
    // Listen for drilldown click
    Emitter.on(
      CodeValueQuantificationEventConstants.STAT_DRILLDOWN_CLICK,
      ({ item, key }) => {
        console.log('STAT_DRILLDOWN_CLICK', item, key);
        // 打开下钻抽屉，传入点击的统计项和选择的元素类型
        setDrillDownDrawerState({
          open: true,
          record: item,
          selectedKey: key,
        });
      },
    );

    return () => {
      Emitter.off(CodeValueQuantificationEventConstants.STAT_NUMBER_CLK);
      Emitter.off(CodeValueQuantificationEventConstants.SURGICAL_MODAL_OPEN);
      Emitter.off(CodeValueQuantificationEventConstants.STAT_DRILLDOWN_CLICK);
    };
  }, [dictData, activeKey]);

  const renderStatCharts = () => {
    return Object.values(ELEMENT_CONFIG).map(({ key }) => (
      <Col span={12} key={key}>
        <CodeStatsEchart
          elementType={key as ElementType}
          dictData={dictData}
          selectedStat={selectedStat}
          chartData={fetchesStatsChanged?.[key]?.data}
          loading={fetchesStatsChanged?.[key]?.loading}
          height={230}
          tableParams={tableParams}
          groupItems={groupItems}
          getStatsChangedReq={getStatsChangedReq}
        />
      </Col>
    ));
  };

  const createAnalysisTab = (key: ElementType) => ({
    key: ELEMENT_CONFIG[key].tabKey,
    label: ELEMENT_CONFIG[key].tabLabel,
    children: (
      <>
        <TableStatistic
          metricData={MetricData}
          dictData={dictData}
          tableParams={tableParams}
          groupItems={groupItems}
          {...{
            [key === ELEMENT_TYPES.CODER
              ? 'needCoder'
              : key === ELEMENT_TYPES.CLIDEPT
              ? 'needCliDept'
              : key === ELEMENT_TYPES.CHIEF
              ? 'needChief'
              : key === ELEMENT_TYPES.MAJOR_PERF_DEPT
              ? 'needMajorPerfDept'
              : 'needMedTeam']: true,
          }}
        />
        <SurgicalWorloadAnalysis
          tableParams={tableParams}
          dictData={dictData}
          groupItems={groupItems}
          isSubComponent={key}
        />
      </>
    ),
  });

  const DrillingDownItem: MenuProps['items'] = Object.values(
    ELEMENT_TYPES,
  )?.map((key: ElementType) => {
    return {
      key,
      label: (
        <span>{ELEMENT_CONFIG[key as keyof typeof ELEMENT_TYPES]?.title}</span>
      ),
    };
  });

  const tabItems = [
    {
      key: DEFAULT_TAB_KEY,
      label: DEFAULT_TAB_LABEL,
      children: (
        <Row gutter={[16, 16]}>
          <Stats
            api={API_ENDPOINTS.GET_METRICS}
            columns={CodeValueStats}
            type="col-xl-6"
            tabKey={activeKey}
            bundledData={bundleData || []}
            tableParams={tableParams}
            loading={getMetricDataLoading || getStatsDataLoading}
            defaultSelectItem={'MainIcdeDiffCnt'}
            statSelectedChange={(record) => {
              setSelectedStat(record);
            }}
            dropdownItems={DrillingDownItem}
          />
          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <Row gutter={[16, 16]}>{renderStatCharts()}</Row>
          </Col>
        </Row>
      ),
    },
    ...Object.values(ELEMENT_TYPES).map((key) =>
      createAnalysisTab(key as ElementType),
    ),
    isOpenDateTrend
      ? {
          ...MONTH_TREND_TAB,
          children: (
            <MonthTable
              metricData={MetricData}
              tableParams={tableParams}
              dictData={dictData}
            />
          ),
        }
      : null,
    {
      ...OPERATION_TAB,
      children: (
        <SurgicalWorloadAnalysis
          tableParams={tableParams}
          dictData={dictData}
          groupItems={groupItems}
          filterDrillDownMetrics={true}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems?.filter((i) => !!i)}
        activeKey={activeKey}
        onChange={(key) => {
          setActiveKey(key);
        }}
        // tabBarExtraContent={{
        //   right: activeKey !== 'Drg' && (
        //     <div style={{ display: 'flex', alignItems: 'center' }}>
        //       <label>当前病组：</label>
        //       <UniSelect
        //         width={300}
        //         showSearch
        //         dataSource={selectOpts}
        //         value={selectedTableItem?.VersionedChsDrgCode}
        //         onChange={(value) => {
        //           setSelectedTableItem(
        //             selectOpts?.find((d) => d?.VersionedChsDrgCode === value),
        //           );
        //         }}
        //         allowClear={false}
        //         optionNameKey={'label'}
        //         optionValueKey={'VersionedChsDrgCode'}
        //         enablePinyinSearch={true}
        //         fieldNames={{
        //           // label: 'ChsDrgName',
        //           value: 'VersionedChsDrgCode',
        //         }}
        //       />
        //     </div>
        //   ),
        // }}
        // destroyInactiveTabPane
      />
      {/* 非手术点击出来的modal */}
      <DetailTableModal
        tableParams={tableParams}
        visible={ModalState.visible}
        record={ModalState.record}
        dictData={dictData}
        modalTitle={`${ModalState?.dictName ?? ''}${
          ModalState.record?.modalTitle
        }`}
        onClose={() => {
          ModalDispatch({
            type: ModalAction.init,
          });
        }}
      />
      {/* 手术点击出来的modal */}
      <SurgicalDetailTableModal
        tableParams={tableParams}
        visible={SurgicalModalState.visible}
        record={SurgicalModalState.record}
        dictData={dictData}
        modalTitle={`${SurgicalModalState?.record?.dictName ?? ''} ${
          SurgicalModalState.record?.OperName
        }`}
        onClose={() => {
          SurgicalModalDispatch({
            type: ModalAction.init,
          });
        }}
      />

      {/* 下钻统计抽屉 */}
      <StatisticDrawer
        open={drillDownDrawerState.open}
        onClose={() =>
          setDrillDownDrawerState({
            open: false,
            record: undefined,
            selectedKey: '',
          })
        }
        metricData={MetricData}
        dictData={dictData}
        record={drillDownDrawerState.record}
        selectedKey={drillDownDrawerState.selectedKey}
        groupItems={groupItems}
        tableParams={tableParams}
      />
    </div>
  );
};

export default CodeValueQuantification;
