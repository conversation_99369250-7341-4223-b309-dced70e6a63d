import { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import {
  Button,
  Card,
  Col,
  Popconfirm,
  Row,
  TableProps,
  Tooltip,
  message,
  Space,
  Divider,
} from 'antd';
import { useSafeState } from 'ahooks';
import { IReducer, ITableState } from '@uni/reducers/src/Interface';
import { SwagTraceRecordItem } from '../interface';
import { columnsHandler, isRespErr, sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import { TableAction, tableReducer, InitTableState } from '@uni/reducers/src';
import { SorterResult, TableRowSelection } from 'antd/lib/table/interface';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import { ArchivedColumns } from '../columns';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { PrinterOutlined, UndoOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ITableReq } from '@/Interface';
import { pickOnlyNeedKeys } from '@uni/utils/src/search-context';
import qs from 'qs';

const Archived = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagTraceRecordItem>,
    );
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    console.log('fetchParams: ', params);
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'WarehouseSignedIn',
        requestParams: [
          {
            url: 'Api/Mr/TraceRecord/WarehouseSignedInList',
            method: 'POST',
            data: {
              ..._.pick(params, [
                'hospCode',
                'cliDept',
                'wareHouseNo',
                'inventoryLocation',
                'Sdate',
                'Edate',
              ]),
              SearchableText: params?.searchKeyword,
              CliDept: params?.dutyDept || undefined,

              // customize header parameters
              ...pickOnlyNeedKeys(params, true),

              current: cur,
              pageSize: size,
              sorting: sortingHandler(sorter),
            },
            dataType: 'mr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      let total = res?.datas[0]?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas[0]?.data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  const reqActionReq = async (
    params: any,
    reqType: ReqActionType,
    selfSearchParams = null,
  ) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Tracing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Tracing/${reqType}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      message.success('操作成功');
      timeLineReset();
      tableReq(
        selfSearchParams ? selfSearchParams : searchParams,
        backPagination.current,
        backPagination.pageSize,
      );
    }
  };

  // print barcode
  const fetchPrint = (record) => {
    if (!record) return; // 函数处理包含 "Date" 或 "date" 的字段

    const processDateFields = (obj) => {
      return Object.keys(obj).reduce((acc, key) => {
        // 将首字母转换为小写
        const newKey = key.charAt(0).toLowerCase() + key.slice(1);
        // 如果字段名包含 'Date' 或 'date'
        if (newKey.includes('Date') || newKey.includes('date')) {
          // 对日期字段进行格式化，确保其为有效日期
          acc[newKey] = dayjs(obj[key]).isValid()
            ? dayjs(obj[key]).format('YYYY-MM-DD')
            : obj[key];
        } else {
          // 其他字段保持不变
          acc[newKey] = obj[key] ?? 'null';
        }
        return acc;
      }, {});
    }; // 处理对象中的日期字段

    const formattedRecord = processDateFields(record);

    let res = window.open(
      `unidmr://tagprint?${qs.stringify(formattedRecord)}`,
      '_self',
      'noopener',
    );
    // res.close();
  };

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns.length > 0
      ? columnsHandler(SearchTable.columns, {
          dataIndex: 'option',
          title: '操作',
          // align: 'center',
          visible: true,
          width: 60,
          valueType: 'option',
          fixed: 'right',
          render: (text, record) => [
            <Tooltip title="打印">
              <PrinterOutlined
                className="icon_blue-color"
                onClick={(e) => {
                  e.stopPropagation();
                  fetchPrint(record);
                }}
              />
            </Tooltip>,
            <Popconfirm
              key="revert"
              title="确定要撤销？"
              onConfirm={(e) => {
                e.stopPropagation();
                reqActionReq(
                  { BarCodes: [record.BarCode] },
                  ReqActionType.warehouseRevertSignin,
                  searchParams,
                );
              }}
              // getPopupContainer={(trigger) => {
              //   return trigger?.parentElement;
              // }}
              onCancel={(e) => e.stopPropagation()}
            >
              <Tooltip title="撤销归档">
                <UndoOutlined
                  className="icon_blue-color"
                  onClick={(e) => e.stopPropagation()}
                />
              </Tooltip>
            </Popconfirm>,
            // <a key="out">病案签出</a>,
            // <a key="in">归还签入</a>,
          ],
        })
      : [];
  }, [SearchTable.columns, searchParams, backPagination]);

  useEffect(() => {
    tableReq(searchParams);
    timeLineReset();
  }, [searchParams]);

  const timeLineReset = () => {
    SearchTableDispatch({
      type: TableAction.clkChange,
      payload: {
        clkItem: null,
      },
    });
    setParams(null);
  };
  // columns处理
  if (columnsList?.['WarehouseSignedIn'] && SearchTable.columns.length < 1) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          ArchivedColumns,
          columnsList['WarehouseSignedIn'],
        ),
      },
    });
  }

  // rowSelection
  const onSelectChange: TableRowSelection<any>['onChange'] = (
    selectedRowKeys,
    selectedRows,
    info,
  ) => {
    let { selectedKeys, selectedRecords } = SearchTable;
    // 判断有没有是要去掉的records
    let needFilterIds = _.difference(selectedRecords, selectedRowKeys);
    // 判断有没有是要添加的keys（records）
    let needAddIds = _.difference(selectedRowKeys, selectedKeys);

    SearchTableDispatch({
      type: TableAction.selectionChange,
      payload: {
        selectedKeys: selectedRowKeys, // keys是不用做处理的，因为有preserveSelectedRowKeys
        // records要处理，hack：因为其实当操作的时候只有可能要么增量要么减量，不会同时触发
        selectedRecords:
          needFilterIds.length > 0
            ? selectedRecords.filter((d) => !needFilterIds.includes(d.BarCode))
            : selectedRecords.concat(
                selectedRows.filter((d) => needAddIds.includes(d.BarCode)),
              ),
      },
    });
  };

  const rowSelection: TableRowSelection<any> = {
    columnWidth: 50,
    preserveSelectedRowKeys: true,
    selectedRowKeys: SearchTable.selectedKeys,
    // selections: [
    //   {
    //     key: 'backendFilteredAll',
    //     text: '全选当前所有',
    //     onSelect: async () => {},
    //   },
    //   {
    //     key: 'backendFilteredClear',
    //     text: '清空当前所有',
    //     onSelect: async () => {},
    //   },
    // ],
    onChange: onSelectChange,
  };

  console.log('DictData', dictData);

  return (
    <>
      <Card
        title="病案归档查询列表"
        extra={
          <Space>
            <Popconfirm
              title="确定批量撤销？"
              key="batchRevert"
              onConfirm={() => {
                reqActionReq(
                  {
                    BarCodes: SearchTable.selectedKeys,
                  },
                  ReqActionType.warehouseRevertSignin,
                );
              }}
            >
              <Button disabled={SearchTable.selectedKeys.length === 0}>
                批量撤销归档
              </Button>
            </Popconfirm>
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Mr/TraceRecord/ExportWarehouseSignedInList',
                method: 'POST',
                data: {
                  ..._.omit(searchParams, [
                    'HospCode',
                    'hospCode',
                    'HospCodes',
                    'hospCodes',
                    'CliDepts',
                    'workFlowStatus',
                  ]),
                  SearchableText: searchParams?.searchKeyword,
                  // Sdate: searchParams?.dateRange
                  //   ? searchParams?.dateRange[0]
                  //   : undefined,
                  // Edate: searchParams?.dateRange
                  //   ? searchParams?.dateRange[1]
                  //   : undefined,
                  // archivedSdate: searchParams?.singleDate || undefined,
                  // archivedEdate: searchParams?.singleDate || undefined,
                  ...searchParams,
                  archivedDate: searchParams?.singleDate || undefined,
                  CliDept: searchParams?.dutyDept || undefined,
                  MaxResultCount: 999999,
                },
                fileName: '病案归档查询列表',
              }}
              btnDisabled={SearchTable.data?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Mr/TraceRecord/WarehouseSignedInList',
                onTableRowSaveSuccess: (columns) => {
                  // 这个columns 存到dva
                  dispatch({
                    type: 'global/saveColumns',
                    payload: {
                      name: 'WarehouseSignedIn',
                      value: columns,
                    },
                  });
                  SearchTableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(
                        ArchivedColumns,
                        columns,
                      ),
                    },
                  });
                },
              }}
            />
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <UniTable
              id="trace_acrchived"
              rowKey="BarCode" // 特殊key，用于rowSelection
              showSorterTooltip={false}
              loading={loadings['WarehouseSignedIn'] ?? false}
              columns={columnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              pagination={backPagination}
              onChange={backTableOnChange}
              scroll={{ x: 'max-content' }}
              rowSelection={rowSelection}
              dictionaryData={dictData}
              forceColumnsUpdate
              // toolBarRender={() => [ ]}
              rowClassName={(record) => {
                if (record?.BarCode === SearchTable.clkItem?.BarCode)
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Col>
          <Col span={6}>
            <PatTimeline
              item={SearchTable?.clkItem}
              timelineItems={timelineItems}
              loading={loadings['TraceRecord/GetActions']}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default Archived;
