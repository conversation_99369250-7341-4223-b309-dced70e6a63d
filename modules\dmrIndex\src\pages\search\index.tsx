import { useEffect, useRef, useState } from 'react';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { dmrManagementCardColumns } from '@/pages/management/columns';
import { DmrManagementCardTableItem } from '@/pages/management/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { DmrCardOperation, DmrEventConstant } from '@/utils/constants';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import SearchForm from '@/components/searchForm';
import { SearchOpts } from '@/pages/management/formItems';
import { useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import {
  codeSdateEdateProcessor,
  dataTableFilter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  isEmptyObj,
  toArrayMode,
} from '@/utils/utils';
import { SorterResult } from 'antd/lib/table/interface';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import ExportIconBtn from '@uni/components/src/backend-export';
import { ManagementSummaries } from '@/pages/management/constants';
import {
  BasePageProps,
  RespVO,
  TableCardResp,
  TableColumns,
} from '@uni/commons/src/interfaces';
import { Button, Card, TableProps, Row, Col, Space, Divider } from 'antd';
import { DmrManagementSummaryItem } from '../management';
import './index.less';
import { DmrCardSummary } from '@/interfaces';

const DmrSearch = (props: BasePageProps) => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');
  const ref = useRef<any>();

  const [tableColumns, setTableColumns] = useState([]);
  const [tableDataSource, setTableDataSource] = useState([]);
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    hideOnSinglePage: false,
  });

  const [dmrCardsSummary, setDmrCardsSummary] = useState<any>({});
  const [dmrCardsSummarySelectedKey, setDmrCardsSummarySelectedKey] = useState(
    'RegisterStatusAllCnt',
  );

  const [searchedValue, setSearchedValue] = useState({
    ..._.omit(
      searchParams,
      'hospCode',
      'hospCodes',
      'Sdate',
      'Edate',
      'dateRange',
    ),
  });

  // handle dmrCardsSummarySelectedKey （上面4个summary切换key）
  const dmrCardsSummarySelectedKeyHandler = (data) => {
    if (dmrCardsSummarySelectedKey) {
      let summaryItem = ManagementSummaries?.find(
        (item) => item?.key === dmrCardsSummarySelectedKey,
      );
      if (summaryItem) {
        data['CustomRegisterStatus'] = summaryItem?.status;
        data['IsLocked'] = summaryItem?.isLocked ?? undefined;
      }
    }
    return data;
  };

  // Emitter 事件处理
  const onDmrManagementCardOperation = async (
    record?: any,
    operation?: string,
    index?: number,
  ) => {
    let currentTableDataSource = [...tableDataSource];
    switch (operation) {
      case DmrCardOperation.VIEW:
        break;
      default:
        break;
    }
  };

  // table 后端分页onchange
  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    dataSourceReq(
      pagi.current,
      pagi.pageSize,
      dataTableFilterSorterHandler(
        originalColumns,
        filters,
        sorter as SorterResult<any>,
      ),
    );

    cardSummaryReq();
  };

  // request 1. fetch columns
  const { data: originalColumns, run: columnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrDataQuery/GetCardsV2', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          return response?.data?.Columns;
        } else {
          return [];
        }
      },
      onSuccess(data, params) {
        setTableColumns(
          tableColumnBaseProcessor(dmrManagementCardColumns(true), data),
        );
      },
    },
  );
  const { loading: cardSummaryLoading, run: cardSummaryReq } = useRequest(
    () => {
      let data = {
        skipFilterSorterMiddleware: true,
        ...searchedValue,
      };

      // 表示用的是 登记时间 所以用的是CodeSdate CodeEdate
      codeSdateEdateProcessor(data, searchedValue?.UseRegistDate === true);

      data = dmrCardsSummarySelectedKeyHandler(data);

      return uniCommonService('Api/Dmr/DmrDataQuery/GetCardSummary', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<DmrCardSummary>) => {
        if (response.code === 0) {
          setDmrCardsSummary({
            RegisterStatusAllCnt: response?.data?.RegisterStatusAllCnt,
            RegisterStatusUnUpdatedCnt:
              response?.data?.RegisterStatusUnUpdatedCnt,
            RegisterStatusRegisteredCnt:
              response?.data?.RegisterStatusRegisteredCnt,
            RegisterStatusReviewFailedCnt:
              response?.data?.RegisterStatusReviewFailedCnt,
            UnlockCnt: response?.data?.UnlockCnt,
          });
        } else {
          setDmrCardsSummary({});
        }
      },
    },
  );

  // request 2. fetch datasource
  const { loading: dataSourceLoading, run: dataSourceReq } = useRequest(
    (current, pageSize, otherData = {}) => {
      let data = {
        skipFilterSorterMiddleware: true,
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
          ...otherData,
        },
        ...searchedValue,
      };

      // 表示用的是 登记时间 所以用的是CodeSdate CodeEdate
      codeSdateEdateProcessor(data, searchedValue?.UseRegistDate === true);

      data = dmrCardsSummarySelectedKeyHandler(data);

      return uniCommonService('Api/Dmr/DmrDataQuery/GetCardsV2', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (
        response: RespVO<TableCardResp<any, DmrManagementCardTableItem>>,
      ) => {
        if (response.code === 0) {
          setTableDataSource(response?.data?.data);
          setBackPagination({
            ...backPagination,
            total:
              response?.data?.recordsFiltered ||
              response?.data?.recordsTotal ||
              0,
          });
        } else {
          setTableDataSource([]);
        }
      },
    },
  );

  useEffect(() => {
    columnsReq();
  }, []);

  // 点击summary card 重新调接口
  useUpdateEffect(() => {
    let pagination = {
      ...backPagination,
      current: 1,
      total: 0,
    };
    setBackPagination(pagination);
    dataSourceReq(pagination?.current, pagination?.pageSize);
    cardSummaryReq();
  }, [dmrCardsSummarySelectedKey]);

  // columns 操作事件监听(先预留，目前用不到)
  useEffect(() => {
    // 事件监听
    Emitter.on(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, (data) => {
      onDmrManagementCardOperation(data?.record, data?.type, data?.index);
    });

    return () => {
      Emitter.off(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION);
    };
  }, [tableDataSource]);

  const tableHeight =
    (document.getElementById('dmr-search-container')?.offsetHeight ?? 65) -
    65 -
    24 -
    50 -
    20 -
    41 -
    (tableDataSource?.length === 0 ? 0 : 50);

  return (
    <div className={'dmr-search-container'} id="dmr-search-container">
      <Row wrap={false} gutter={8} style={{ overflow: 'hidden' }}>
        <Col flex="330px">
          <Card title="查询条件" className="dmr-search-form-card">
            <SearchForm
              classNames={['noMarginBottom']}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              searchOpts={SearchOpts(
                searchedValue,
                {
                  hospOpts: dictData.Hospital,
                  deptOpts: dictData.CliDepts,
                  wardOpts: dictData?.['Dmr']?.Wards,
                  outTypeOpts: dictData?.['Dmr']?.LYFS,
                  ylfkfsOpts: dictData?.['Dmr']?.YLFKFS,
                  coderOpts: dictData?.['Dmr']?.Coder,
                },
                false,
              )}
              onFinish={async (values) => {
                if (
                  values.UseRegistDate === 'UseRegistDate' &&
                  (!values.Sdate || !values.Edate)
                ) {
                  // do nth
                  // TODO 提示
                } else {
                  setSearchedValue({
                    ...values,
                    UseRegistDate:
                      values.UseRegistDate === 'UseRegistDate' ? true : false,
                  });
                  setQiankunGlobalState({
                    dictData: dictData,
                    searchParams: {
                      ...searchParams,
                      ..._.omit(values, ['Sdate', 'Edate', 'HospCode']),
                      dateRange: [values.Sdate, values.Edate],
                      hospCodes: values?.HospCode,
                      UseRegistDate:
                        values.UseRegistDate === 'UseRegistDate' ? true : false,
                    },
                  });

                  setTimeout(() => {
                    dataSourceReq(1, backPagination.pageSize);
                    cardSummaryReq();
                  }, 0);
                }
              }}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{ width: '100%', marginTop: '8px' }}
                      key="reset"
                      onClick={() => {
                        props.form?.submit?.();
                      }}
                    >
                      查询
                    </Button>,
                  ];
                },
              }}
            />
          </Card>
        </Col>
        <Col flex="auto">
          <Card
            title={'病案列表'}
            extra={
              <Space>
                <Divider type="vertical" />
                <ExportIconBtn
                  isBackend={true}
                  backendObj={{
                    url: 'Api/Dmr/DmrDataQuery/ExportGetCardsV2',
                    method: 'POST',
                    data: dmrCardsSummarySelectedKeyHandler({
                      skipFilterSorterMiddleware: true,
                      ...searchedValue,
                    }),
                    fileName: '病案列表',
                  }}
                  btnDisabled={tableDataSource?.length < 1}
                />
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Dmr/DmrDataQuery/GetCardsV2',
                    onTableRowSaveSuccess: (columns) => {
                      setTableColumns(
                        tableColumnBaseProcessor(
                          dmrManagementCardColumns(true),
                          columns,
                        ),
                      );
                    },
                  }}
                />
              </Space>
            }
          >
            <div className={'dmr-search-summary-container'}>
              <Row gutter={[16, 16]}>
                {ManagementSummaries?.map((item) => {
                  return (
                    <DmrManagementSummaryItem
                      className={
                        item?.key === dmrCardsSummarySelectedKey
                          ? 'card-selected'
                          : ''
                      }
                      label={item?.label}
                      itemKey={item?.key}
                      count={dmrCardsSummary?.[item?.key]}
                      onClick={() => {
                        setDmrCardsSummarySelectedKey(item?.key);
                      }}
                    />
                  );
                })}
              </Row>
            </div>
            <UniTable
              actionRef={ref}
              id={'dmr-search-card-table'}
              rowKey={'DmrCardId'}
              dictionaryData={dictData}
              loading={dataSourceLoading}
              columns={tableColumns}
              dataSource={tableDataSource}
              pagination={backPagination}
              onChange={backTableOnChange}
              toolBarRender={null}
              isBackPagination
              scroll={{ x: 'max-content', y: tableHeight }}
              widthCalculate={true}
              style={{
                '--tableMinHeight': `${tableHeight || 300}px`,
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DmrSearch;
