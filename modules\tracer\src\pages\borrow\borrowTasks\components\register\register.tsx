import React, { useRef, useEffect, useState } from 'react';
import { useDispatch, useRequest, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import {
  Row,
  Col,
  InputRef,
  Modal,
  message,
  Space,
  Drawer,
  Button,
  Card,
  Popconfirm,
  Checkbox,
} from 'antd';
import { useKeyPress, useSafeState } from 'ahooks';
import { ReqActionType } from '@/Constants';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import { BorrowFormItems } from '../formItems';
import {
  BorrowColumns,
  BorrowColumnsWithOperation,
  EVENT_BORROW_DELETE,
} from '../constants';
import BorrowPagePrint from '@/components/PrintBorrowPage';
import './index.less';
import { useTableData } from './hooks/useTableData';
import { useModalState } from './hooks/useModalState';
import { useApiRequests } from './hooks/useApiRequests';
import BorrowForm from './components/BorrowForm';
import BorrowTable from './components/BorrowTable';
import ConfirmModal from './components/ConfirmModal';
import { SwagBorrowRecordItem } from '../../../interface';
import { ActionRecordItem } from '@/pages/mrRoom/interface';
import { TableAction } from '@uni/reducers/src/index';
import { ProFormInstance } from '@uni/components/src/pro-form/index';
import { formValuesPostProcessor } from '@uni/components/src/query-configuration/processor';
import dayjs from 'dayjs';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { PrinterOutlined } from '@ant-design/icons';

interface BorrowRegisterProps {
  createReq?: (values: any, cb: () => void) => void;
  loading?: any;
  onClose?: () => void;
  visible?: boolean;
  title?: string;
  width?: string | number;
  placement?: 'left' | 'right' | 'top' | 'bottom';
  bodyStyle?: React.CSSProperties;
  destroyOnClose?: boolean;
}

/**
 * 借阅登记组件 - 优化后的组件，包含Drawer
 */
// 本地存储键名
const PRINT_CONFIRM_STORAGE_KEY = 'borrow_print_confirm_visible';
const CANCEL_CONFIRM_STORAGE_KEY = 'borrow_cancel_confirm_visible';

const BorrowRegister: React.FC<BorrowRegisterProps> = (props) => {
  const {
    createReq,
    loading,
    onClose = () => {},
    visible = false,
    title = '新增借阅申请',
    width = 'calc(100% - 100px)',
    placement = 'right',
    bodyStyle = { padding: 0 },
    destroyOnClose = true,
  } = props;
  const {
    globalState: { dictData, userInfo },
  } = useModel('@@qiankunStateFromMaster');
  const loadings = useSelector((state) => state.global.loadings);

  // 表单相关引用
  const proFormRef = useRef<ProFormInstance>();
  const barCodeRef = useRef<InputRef>(null);
  // 节流隐式标识
  const hiddenLoadingRef = useRef(false);
  // 设计思路是先判断是不是相同的 不是就没事 是就进debounce判断
  const newestBarCode = useRef(undefined);
  // timeline请求钩子
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);
  const [lendSearchOpts, setLendSearchOpts] = useState([]);
  // Popconfirm可见性状态
  const [printConfirmVisible, setPrintConfirmVisible] = useState<boolean>(
    localStorage.getItem(PRINT_CONFIRM_STORAGE_KEY) !== 'false',
  );
  const [cancelConfirmVisible, setCancelConfirmVisible] = useState<boolean>(
    localStorage.getItem(CANCEL_CONFIRM_STORAGE_KEY) !== 'false',
  );
  // 记住选择状态
  const [rememberPrintChoice, setRememberPrintChoice] =
    useState<boolean>(false);
  const [rememberCancelChoice, setRememberCancelChoice] =
    useState<boolean>(false);

  const defaultSearchOpts = BorrowFormItems(barCodeRef);

  useEffect(() => {
    console.log('lendSearchOpts', lendSearchOpts);
  }, [lendSearchOpts]);

  // 初始化表格数据管理
  // 可以借阅的List
  const {
    SearchTable,
    SearchTableDispatch,
    SearchedTableColumnsSolver,
    initTableColumns,
    saveTableColumns,
  } = useTableData<SwagBorrowRecordItem & ActionRecordItem>(
    BorrowColumnsWithOperation, // 使用带操作列的定义
  );

  // 不可借阅的List
  const {
    SearchTable: UnRendTable,
    SearchTableDispatch: UnRendTableDispatch,
    SearchedTableColumnsSolver: UnRendTableColumns,
    initTableColumns: initUnRendTableColumns,
    saveTableColumns: saveUnRendTableColumns,
  } = useTableData<SwagBorrowRecordItem & ActionRecordItem>([]);

  // 模态框状态管理
  const {
    ModalState,
    selectedRecordKey,
    setSelectedRecordKey,
    selectedRecordRows,
    setSelectedRecordRows,
    modalAlert,
    setModalAlert,
    resetModalState,
    showModal,
    selectRecord,
  } = useModalState<SwagBorrowRecordItem>();

  // 初始化模态框列
  const [modalColumns, setModalColumns] = useSafeState([]);

  // 监听Enter键触发借阅操作
  useKeyPress(
    'enter',
    () => {
      console.log('press enter only', proFormRef?.current?.getFieldsValue());
      if (proFormRef.current.getFieldValue('BarCode')) {
        proFormRef.current.validateFields().then(() => {
          fetchReqActionReq(lendSearchOpts);
        });
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('borrowRegisterForm'),
    },
  );

  // 统一处理多条 / 没有数据
  const handleSearchResult = (result) => {
    // 处理多记录情况
    if (result?.multipleRecords) {
      showModal(result.records, result.specialData);
    } else if (result?.noData) {
      // 没查到数据的情况
      Modal.confirm({
        title: `查无数据`,
        content: '请确认病案标识填写正确',
        onOk: () => {
          proFormRef.current.resetFields(['BarCode']);
          focusBarCode();
        },
        onCancel: () => {
          proFormRef.current.resetFields(['BarCode']);
          focusBarCode();
        },
        cancelButtonProps: { style: { display: 'none' } },
      });
    }
  };

  // 重写searchByBarCodeReq以加入条码防抖处理
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    console.log('searchByBarCodeReq', params, needDataPush);
    if (!params) return;
    // 扫之前，先做判断
    if (handleBarCodeDebounce(params?.BarCode)) {
      // 拦截
      return;
    }
    const result = await apiSearchByBarCodeReq(params, needDataPush);

    // 处理多记录情况
    handleSearchResult(result);
  };

  // 重写searchByOtherTypeReq以加入防抖处理
  const searchByOtherTypeReq = async (params: any, needDataPush = false) => {
    console.log('searchByBarCodeReq', params, needDataPush);
    if (!params) return;
    // 扫之前，先做判断
    if (handleBarCodeDebounce(params?.BarCode)) {
      // 拦截
      return;
    }
    const result = await apiSearchOneReq(params, needDataPush);

    // 处理多记录情况
    handleSearchResult(result);
  };

  /**
   * 处理表单提交/借阅操作
   */
  const fetchReqActionReq = (lendSearchOpts) => {
    console.log('hiddenLoading', hiddenLoadingRef);

    if (hiddenLoadingRef.current) return;
    hiddenLoadingRef.current = true;
    console.log('searchByBarCodeReq', searchByBarCodeReq);
    proFormRef.current
      .validateFields()
      .then((values) => {
        values = formValuesPostProcessor(lendSearchOpts, values);

        if (values?.SignType?.value === 'BarCode') {
          searchByBarCodeReq(values, false);
        } else {
          // 对于非BarCode类型，创建请求参数对象
          const searchParam = {
            ..._.omit(values, 'BarCode'),
            [values.SignType.value]: values.BarCode,
          };
          searchByOtherTypeReq(searchParam, false);
        }
      })
      .catch((err) => {
        hiddenLoadingRef.current = false;
        message.error('请确认已填写所有借阅人信息！');
      });
  };

  /**
   * 条码防抖处理，避免重复扫描同一条码
   */
  const handleBarCodeDebounce = (barCode) => {
    if (SearchTable.data?.findIndex((d) => d?.BarCode === barCode) > -1) {
      // 判断时间 存在与表内 长于 特定时间，比如 2s
      if (
        dayjs().diff(
          SearchTable.data?.find((d) => d?.BarCode === barCode)?.InsertTime,
        ) < 2000
      ) {
        // 拦截
        hiddenLoadingRef.current = false;
        proFormRef?.current?.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  /**
   * 让条码输入框获取焦点
   */
  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  // API请求处理
  const {
    searchOneReq: apiSearchOneReq,
    searchByBarCodeReq: apiSearchByBarCodeReq,
    reqActionReq,
    exportBackendReq,
  } = useApiRequests({
    SearchTableDispatch,
    UnLendTableDispatch: UnRendTableDispatch,
    resetModalState,
    hiddenLoading: hiddenLoadingRef,
    focusBarCode,
    proFormRef,
    setRevertRecord,
  });

  const { run: tableColumnReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Mr/BorrowApplication/GetApplicationWithRecords',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        const columns = initTableColumns(response?.data?.Columns);
        initUnRendTableColumns(response?.data?.Columns);

        if (columns.length > 0) {
          console.log('setModalColumns', columns);
          setModalColumns(columns);
        }
      },
    },
  );

  // 初始化表格列
  useEffect(() => {
    if (initTableColumns) {
      tableColumnReq();
    }

    // 注册删除行的事件监听
    Emitter.on(EVENT_BORROW_DELETE, (record) => {
      // 使用 TableAction.dataFilt 过滤掉选中的数据
      SearchTableDispatch({
        type: TableAction.dataFilt,
        payload: {
          key: 'BarCode', // 假设使用BarCode作为唯一标识
          value: record.BarCode,
        },
      });
    });

    // 组件卸载时清理事件监听
    return () => {
      Emitter.off(EVENT_BORROW_DELETE);
    };
  }, [initTableColumns]);

  // 处理撤销后的timeLine重置
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (SearchTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  // 处理模态框确认逻辑
  const handleModalConfirm = () => {
    if (
      ModalState.record.findIndex(
        (d) => d.BarCode === selectedRecordKey?.at(0),
      ) > -1
    ) {
      // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
      // 把数据插入已记录列表
      SearchTableDispatch({
        type: TableAction.dataUnshiftUniqSimple,
        payload: {
          data: {
            ...ModalState.record?.find(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ),
            InsertTime: dayjs(),
          },
          key: 'BarCode',
        },
      });
      hiddenLoadingRef.current = false;
      resetModalState();
      // barCode自动清除 不阻碍扫码枪继续扫码
      proFormRef.current.resetFields(['BarCode']);
      // focus input
      focusBarCode();
    } else {
      // 没选
      setModalAlert(true);
    }
  };

  // 处理表格行点击
  const handleRowClick = (record) => {
    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: record,
        },
      });
      setParams({ barCode: record.BarCode });
    }
  };

  // 清空表格数据
  const clearTableData = () => {
    // 清空可借阅表格数据
    SearchTableDispatch({
      type: TableAction.dataChange,
      payload: {
        data: [],
      },
    });

    // 清空不可借阅表格数据
    UnRendTableDispatch({
      type: TableAction.dataChange,
      payload: {
        data: [],
      },
    });
    // 重置选中的行和时间轴数据
    SearchTableDispatch({
      type: TableAction.clkChange,
      payload: {
        clkItem: null,
      },
    });
    setParams(null);
  };

  // 处理确认、打印和取消按钮
  const handleCancel = () => {
    // 检查是否需要显示确认提示
    if (cancelConfirmVisible && SearchTable.data?.length > 0) {
      // 有数据时才显示确认提示
      return; // 不立即执行关闭，由Popconfirm控制
    }
    // 没有数据或不需要确认时直接关闭
    clearTableData();
    onClose();
  };

  // 确认取消并更新localStorage设置
  const confirmCancel = () => {
    if (rememberCancelChoice) {
      localStorage.setItem(CANCEL_CONFIRM_STORAGE_KEY, 'false');
    }
    clearTableData();
    onClose();
  };

  // 处理打印前确认
  const handlePrintClick = () => {
    // 检查是否需要显示确认提示
    if (!printConfirmVisible) {
      // 不需要确认时，直接执行打印逻辑
      executePrintAndConfirm();
    }
    // 需要确认时，由Popconfirm控制
  };

  // 确认打印并更新localStorage设置
  const confirmPrint = () => {
    if (rememberPrintChoice) {
      localStorage.setItem(PRINT_CONFIRM_STORAGE_KEY, 'false');
    }
    executePrintAndConfirm();
  };

  // 执行打印和确认操作
  const executePrintAndConfirm = () => {
    // 直接传递打印触发标志给handleConfirm，而不依赖状态
    // 先执行确认逻辑，将true作为isPrintTriggered参数传递
    handleConfirm(true);
    // 打印在createReq的onSuccess回调中执行
  };

  const handleConfirm = (fromPrintButton = false) => {
    // 这里执行表单提交逻辑
    const formValues = proFormRef.current?.getFieldsValue();

    // 调用createReq
    createReq(
      {
        BarCodes: SearchTable.data?.map((d) => d.BarCode),
        ..._.omit(formValues, 'BarCode'),
        isPrintTriggered: fromPrintButton, // 直接使用参数值，不依赖状态
      },
      clearTableData,
    );
  };

  return (
    <Drawer
      title={title}
      width={width}
      placement={placement}
      onClose={() => {
        onClose();
        clearTableData();
      }}
      maskClosable={false}
      open={visible}
      bodyStyle={bodyStyle}
      destroyOnClose={destroyOnClose}
      footer={
        <Space style={{ float: 'right' }}>
          <Popconfirm
            title={
              <div>
                <div>取消确认：取消将不会提交申请，数据都将被清空</div>
                <Checkbox
                  checked={rememberCancelChoice}
                  onChange={(e) => setRememberCancelChoice(e.target.checked)}
                  style={{ marginTop: 8 }}
                >
                  之后不再提醒我
                </Checkbox>
              </div>
            }
            onConfirm={confirmCancel}
            okText="确定"
            cancelText="取消"
            open={
              cancelConfirmVisible && SearchTable.data?.length > 0
                ? undefined
                : false
            }
            onOpenChange={(open) => {
              if (!open) {
                setRememberCancelChoice(false);
              }
            }}
          >
            <Button onClick={handleCancel}>取消</Button>
          </Popconfirm>

          <Popconfirm
            title={
              <div>
                <div>打印确认：打印的同时就会创建一条申请</div>
                <Checkbox
                  checked={rememberPrintChoice}
                  onChange={(e) => setRememberPrintChoice(e.target.checked)}
                  style={{ marginTop: 8 }}
                >
                  之后不再提醒我
                </Checkbox>
              </div>
            }
            onConfirm={confirmPrint}
            okText="确定"
            cancelText="取消"
            open={printConfirmVisible ? undefined : false}
            onOpenChange={(open) => {
              if (!open) {
                setRememberPrintChoice(false);
              }
            }}
          >
            <Button
              disabled={SearchTable.data?.length < 1}
              loading={loading}
              onClick={handlePrintClick}
              icon={<PrinterOutlined />}
            >
              打印
            </Button>
          </Popconfirm>

          <Button
            type="primary"
            disabled={SearchTable.data?.length < 1}
            loading={loading}
            onClick={() => handleConfirm(false)} // 传递false表示不是从打印按钮触发的
          >
            确认
          </Button>
        </Space>
      }
    >
      <div style={{ padding: '8px 16px' }}>
        <Row gutter={[16, 16]}>
          <Col span={7}>
            {/* 左侧表单部分 */}
            <BorrowForm
              proFormRef={proFormRef}
              barCodeRef={barCodeRef}
              lendSearchOpts={lendSearchOpts}
              setLendSearchOpts={setLendSearchOpts}
              defaultSearchOpts={defaultSearchOpts}
              dictData={dictData}
              loadings={loadings}
              fetchReqActionReq={fetchReqActionReq}
            />
            {/* 时间轴部分 */}
            <PatTimeline
              item={SearchTable?.clkItem}
              loading={loadings['TraceRecord/GetActions']}
              timelineItems={timelineItems}
            />
          </Col>
          <Col span={17}>
            {/* 右侧表格部分 */}
            <Card
              title="病案借阅登记"
              style={{ marginBottom: 16 }}
              extra={
                <Space>
                  <TableColumnEditButton
                    {...{
                      columnInterfaceUrl:
                        'Api/Mr/BorrowApplication/GetApplicationWithRecords',
                      onTableRowSaveSuccess: saveTableColumns,
                    }}
                  />
                </Space>
              }
            >
              <BorrowTable
                id="can-lend-table"
                columns={SearchedTableColumnsSolver}
                data={SearchTable.data}
                loadings={loadings}
                dictData={dictData}
                clkItem={SearchTable.clkItem}
                onRowClick={handleRowClick}
              />
            </Card>
            <Card title="借阅失败列表">
              <BorrowTable
                id="cant-lend-table"
                columns={UnRendTableColumns?.filter(
                  (d) =>
                    d?.dataIndex !== 'xuhao' && d?.dataIndex !== 'isCorrect',
                )} // 用同一个columns
                data={UnRendTable.data} // 唯一的区别
                loadings={loadings}
                dictData={dictData}
                clkItem={SearchTable.clkItem} // 用同一个clkItem 给时间轴的
                onRowClick={handleRowClick}
              />
            </Card>
          </Col>
        </Row>

        {/* 病案确认模态框 */}
        <ConfirmModal
          visible={ModalState.visible}
          record={ModalState.record}
          columns={modalColumns}
          selectedRowKeys={selectedRecordKey}
          loading={
            loadings['BorrowRecord/GetLendList'] ||
            loadings['TraceRecord/GetList'] ||
            loadings[`Tracing/${ReqActionType.lend}`] ||
            false
          }
          modalAlert={modalAlert}
          onOk={handleModalConfirm}
          onCancel={() => {
            // 重置节流标识
            hiddenLoadingRef.current = false;
            focusBarCode();
            resetModalState();
          }}
          onRowSelect={(selectedRowKeys, selectedRows) => {
            setSelectedRecordKey(selectedRowKeys);
            setSelectedRecordRows(selectedRows?.at(0));
            setModalAlert(false);
          }}
          onRowClick={(record) => {
            selectRecord(record?.BarCode, record);
          }}
          onAlertClose={() => setModalAlert(false)}
        />
      </div>
    </Drawer>
  );
};

export default BorrowRegister;
