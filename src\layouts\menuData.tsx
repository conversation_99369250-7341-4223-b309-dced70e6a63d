import {
  HeaderMenuItem,
  MenuData,
} from '@uni/components/src/menu-sider/MenuSider';
import {
  AimOutlined,
  AppstoreAddOutlined,
  AuditOutlined,
  CloudUploadOutlined,
  DotChartOutlined,
  FlagOutlined,
  FundProjectionScreenOutlined,
  RadarChartOutlined,
  AreaChartOutlined,
  ReconciliationOutlined,
  SolutionOutlined,
  ImportOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  RecordSvg,
  PermissionsSvg,
  LayoutsSvg,
  DictionarySvg,
  InsurDictionarySvg,
  HqmsDictionarySvg,
  WtDictionarySvg,
  EscalateDictionarySvg,
  InstitutionSvg,
  CalendarSvg,
  ManagementSvg,
  SearchSvg,
  DetailSvg,
  RuleSvg,
  BarSvg,
  PieSvg,
  ScatterSvg,
  ReportSvg,
  Report2Svg,
  OperSvg,
  QualitySvg,
  SdSvg,
  ServiceabilitySvg,
  ComplicationSvg,
  CombineQuerySvg,
  InpatientSvg,
  OutpatientSvg,
  ObspatientSvg,
  WorkLoadItemSvg,
  MonitorCameraSvg,
  RecordListSvg,
  AnalysisSvg,
  WardSvg,
  SigninSvg,
  SealSvg,
  CmiSvg,
  ImportSvg,
  TransferSvg,
  GlobalConfigurationSvg,
  ImportAndExportSvg,
  FinancingSvg,
  TransactionSvg,
  ExcelSvg,
  OutpatientDoctorSvg,
  CustomDiseaseSvg,
  ContrastSvg,
  FundSvg,
  MonitorImportantSvg,
  ExternalSvg,
  AdvManagementSvg,
  MedChrigsmSvg,
  InHospInfoSvg,
  AbnormalCaseSvg,
  RecurringJobSvg,
  printSvg,
  ODMManagementSvg,
  EnquireSvg,
  ToolSvg,
  TowelSvg,
  HisExcelSvg,
  TableSvg,
  CustomRuleSvg,
  KpiSettingsSvg,
  simpleQuerySvg,
  ODMOutManagementSvg,
  outgoingSvg,
  UploadSvg,
  DataManagementSvg,
  MaintenanceDataRecordCntSvg,
  MaintenanceDataCntSvg,
  ChineseMedicineSvg,
  DiseaseTypeSvg,
  FinalizedSvg,
} from '@uni/components/src/custom-icon/svg';
import BookOne from '@uni/commons/src/icon/BookOne';
import DataScreen from '@uni/commons/src/icon/DataScreen';
import Modify from '@uni/commons/src/icon/Modify';
import HistoryQuery from '@uni/commons/src/icon/HistoryQuery';
import DatabaseCode from '@uni/commons/src/icon/DatabaseCode';
import RefreshOne from '@uni/commons/src/icon/RefreshOne';
import DocSuccess from '@uni/commons/src/icon/DocSuccess';
import Audit from '@uni/commons/src/icon/Audit';
import DocSearchTwo from '@uni/commons/src/icon/DocSearchTwo';
import DocumentFolder from '@uni/commons/src/icon/DocumentFolder';

import findDeep from 'deepdash/findDeep';
import { isEmptyValues } from '@uni/utils/src/utils';

export const HeaderMenuKey = {
  DMR_INDEX_AND_MANAGEMENT: 'DMR_INDEX_AND_MANAGEMENT',
  DMR_INDEX_QUALITY_CONTROL: 'DMR_INDEX_QUALITY_CONTROL',
  DMR_INDEX_QUALITY_EXAMINE: 'DMR_INDEX_QUALITY_EXAMINE',
  MEDICAL_QUALITY_ANALYSIS: 'MEDICAL_QUALITY_ANALYSIS',
  ENERGY_EFFICIENCY_ANALYSIS: 'ENERGY_EFFICIENCY_ANALYSIS',
  SYSTEM_CONFIGURATION: 'SYSTEM_CONFIGURATION',
  INSURANCE_SETTLEMENT: 'INSURANCE_SETTLEMENT',
  DRG_SETTLEMENT: 'DRG_SETTLEMENT',
  DIP_SETTLEMENT: 'DIP_SETTLEMENT',
  HOSP_REFINE: 'HOSP_REFINE',
  DMR_INDEX_DOCT_QUALITY_CONTROL: 'DMR_INDEX_DOCT_QUALITY_CONTROL',
  MEDICAL_INSURANCE_FUND_MONITOR: 'MEDICAL_INSURANCE_FUND_MONITOR',
};

export const headerMenu: HeaderMenuItem[] = [
  {
    id: 'DMR',
    name: '病案统计',
    key: [HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT],
    icon: '21-management-logo.png',
    bg: 'management',
    mainShow: true,
  },
  {
    id: 'QUALITY_CONTROL',
    name: '首页质控',
    key: [
      HeaderMenuKey.DMR_INDEX_QUALITY_EXAMINE,
      HeaderMenuKey.DMR_INDEX_QUALITY_CONTROL,
      HeaderMenuKey.DMR_INDEX_DOCT_QUALITY_CONTROL,
      HeaderMenuKey.ENERGY_EFFICIENCY_ANALYSIS,
    ],
    icon: '22-control-logo.png',
    bg: 'control',
    mainShow: true,
  },
  {
    id: 'MEDICAL_QUALITY',
    name: '医疗服务与质量评价',
    key: [HeaderMenuKey.MEDICAL_QUALITY_ANALYSIS],
    icon: '23-analysis-logo.png',
    bg: 'analysis',
    mainShow: true,
  },
  {
    id: 'INSURANCE',
    name: '智慧医保',
    key: [
      HeaderMenuKey.INSURANCE_SETTLEMENT,
      HeaderMenuKey.DRG_SETTLEMENT,
      HeaderMenuKey.DIP_SETTLEMENT,
      HeaderMenuKey.MEDICAL_INSURANCE_FUND_MONITOR,
    ],
    icon: '24-settlement-logo.png',
    bg: 'settlement',
    mainShow: true,
  },
  // {
  //   id: "HOSPITAL",
  //   name: '医院精细化管控',
  //   key: [HeaderMenuKey.HOSP_REFINE],
  //   icon: '25-detail-logo.png',
  //   bg: 'detail',
  //   mainShow: true,
  // },
  {
    id: 'SYSTEM_CONFIGURATION',
    name: '系统配置',
    key: [HeaderMenuKey.SYSTEM_CONFIGURATION],
    icon: '25-detail-logo.png',
    bg: 'detail',
    mainShow: false,
  },
];

/**
 * 此处请严格遵守内层的route路由path 从外层路由pathname扩展
 * breadcrumb根据路由name 以及层级关系渲染
 */
export const menuData: MenuData[] = [
  {
    route: '/systemConfiguration',
    name: '系统配置',
    headerMenuKey: HeaderMenuKey.SYSTEM_CONFIGURATION,
    headerMenuItemActualRoute: '/systemConfiguration/permissions/menu',
    icon: <AppstoreAddOutlined />,
    isSubSystem: true,
    hideInConfiguration: false,
    children: [
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/permissions',
        name: '用户权限配置',
        icon: PermissionsSvg(),
        // <img src={require('@/assets/icons/icon_menu_user_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/permissions/menu',
            name: '菜单设置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/permissions/users',
            name: '用户管理',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/permissions/roles',
            name: '用户组管理',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/layouts',
        name: '首页登记配置',
        icon: LayoutsSvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/layouts/index',
            name: '首页布局配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/layouts/enrollment',
            name: '登记负责分配',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/path',
        name: '接口字典对照',
        icon: EscalateDictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/icde',
            name: '诊断对照配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/operation',
            name: '手术对照配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/dictionary',
            name: '字典库对照配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/department',
            name: '科室对照配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/ward',
            name: '病区对照配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/path/dynHierarchy',
            name: '动态科室对照配置',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/base',
        name: '首页字典配置',
        icon: DictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/dictionary',
            name: '字典库配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/dmrColumnDictMatch',
            name: '列字典库匹配',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/icde',
            name: '诊断编码配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/icdeCategory',
            name: '诊断类目亚目配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/operation',
            name: '手术编码配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/operCategory',
            name: '手术类目亚目配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/department',
            name: '科室对照配置',
          },
          // {
          //   hideInConfiguration: false,
          //   route: '/systemConfiguration/base/department',
          //   name: '院内科室配置',
          // },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/operSet',
            name: '手术组套配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/base/missing',
            name: '缺失导入字典对照修复',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/insur',
        name: '结算清单字典配置',
        icon: InsurDictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/icde',
            name: '诊断配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/operation',
            name: '手术配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/dictionary',
            name: '字典库配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/insurColumnDictMatch',
            name: '列字典库匹配',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/department',
            name: '科别配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/insur/missing',
            name: '缺失导入字典对照修复',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/hqms',
        name: '国考字典配置',
        icon: HqmsDictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/hqms/icde',
            name: '诊断配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/hqms/operation',
            name: '手术配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/hqms/dictionary',
            name: '字典库配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/hqms/department',
            name: '科别配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/hqms/missing',
            name: '缺失导入字典对照修复',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/wt',
        name: '卫统字典配置',
        icon: WtDictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/wt/icde',
            name: '诊断配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/wt/operation',
            name: '手术配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/wt/dictionary',
            name: '字典库配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/wt/department',
            name: '科别配置',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/institution',
        name: '医疗机构人员配置',
        icon: InstitutionSvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/hospital',
            name: '院区配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/department',
            name: '科室配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/ward',
            name: '病区配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/majorPerfDept',
            name: '学科配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/perfDept',
            name: '绩效科室配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/departmentConfig',
            name: '科别配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/employees',
            name: '人员配置',
          },
          {
            hideInConfiguration: false,
            route: '/systemConfiguration/institution/hospEmployee',
            name: '医院人员构成管理',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/reportCliDeptSettings',
        name: '统计科室配置',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M38 20H18V28H38V20Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M32 6H18V14H32V6Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M44 34H18V42H44V34Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M17 10H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17 24H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17 38H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M5 44V4"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        hideInConfiguration: false,
        route: '/systemConfiguration/calendar',
        name: '日历配置',
        icon: CalendarSvg(),
      },
    ],
  },
  // INSUR CONFIGURATION
  {
    route: '/insurConfiguration',
    name: '医保系统配置',
    headerMenuKey: HeaderMenuKey.SYSTEM_CONFIGURATION,
    headerMenuItemActualRoute: '/insurConfiguration/permissions/menu',
    icon: <AppstoreAddOutlined />,
    isSubSystem: true,
    hideInConfiguration: false,
    children: [
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/permissions',
        name: '用户权限配置',
        icon: PermissionsSvg(),
        // <img src={require('@/assets/icons/icon_menu_user_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/permissions/menu',
            name: '菜单设置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/permissions/users',
            name: '用户管理',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/permissions/roles',
            name: '用户组管理',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/insur',
        name: '结算清单字典配置',
        icon: InsurDictionarySvg(),
        // <img src={require('@/assets/icons/icon_menu_base_config.png')} />,
        children: [
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/icde',
            name: '诊断配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/icdeReference',
            name: '诊断对照',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/operation',
            name: '手术配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/operationReference',
            name: '手术对照',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/dictionary',
            name: '字典库配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/insurColumnDictMatch',
            name: '列字典库匹配',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/department',
            name: '科别配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/insur/missing',
            name: '缺失导入字典对照修复',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/medChrgitmsManagement',
        name: '收费目录管理',
        icon: MedChrigsmSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/advsManagement',
        name: '医嘱目录管理',
        icon: AdvManagementSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/drgPayStandard',
        name: 'DRG支付标准管理',
        icon: TowelSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/dipPayStandard',
        name: 'DIP支付标准管理',
        icon: TowelSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/inHospPatientInfoDict',
        name: '在院病人信息字典',
        icon: InHospInfoSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/abnormalCaseRules',
        name: '异常病例监控规则',
        icon: AbnormalCaseSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/insurConfiguration/institution',
        name: '医疗机构人员配置',
        icon: InstitutionSvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/hospital',
            name: '院区配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/department',
            name: '科室配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/ward',
            name: '病区配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/majorPerfDept',
            name: '学科配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/perfDept',
            name: '绩效科室配置',
          },
          {
            hideInConfiguration: false,
            route: '/insurConfiguration/institution/employees',
            name: '人员配置',
          },
        ],
      },
    ],
  },
  {
    route: '/dmr',
    name: '病案首页登记',
    icon: <SolutionOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT,
    headerMenuItemActualRoute: '/dmr/index',
    children: [
      {
        route: '/dmr/index',
        name: '病案首页登记',
        icon: RecordSvg(),
      },
      {
        route: '/dmr/revision',
        name: '病案首页修订',
        icon: <Modify theme="outline" size="24" fill="#757575" />,
      },
      {
        route: '/dmr/management',
        name: '首页数据管理',
        icon: ManagementSvg(),
      },
      {
        route: '/dmr/coderManagement',
        name: '已登记数据管理',
        icon: <DocSuccess theme="outline" size="24" fill="#757575" />,
      },
      {
        route: '/dmr/search',
        name: '首页数据查询',
        icon: SearchSvg(),
      },
      {
        route: '/dmr/configuration',
        name: '首页布局配置',
        icon: SearchSvg(),
      },
      // {
      //   route: '/dmr/statistic',
      //   name: '首页数据统计',
      //   icon: StatisticSvg(),
      // },
      {
        route: '/dmr/roomingInManagement',
        name: '母婴同室管理',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M38 31V44C38 44 17.375 44 13.625 44C9.875 44 8 42 8 37C8 32 8 4 8 4H20"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <circle
              cx="33"
              cy="17"
              r="9"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
            />
            <path
              d="M22 18V16"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M44 18V16"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M33 8C32.8333 7 31.8 4.8 29 4"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M33 8C33.0833 7 33.6 4.8 35 4"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <circle cx="36" cy="16" r="2" fill="#757575" />
            <circle cx="30" cy="16" r="2" fill="#757575" />
            <path
              d="M8 36H38"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/dmr/history',
        name: '历史病案数据',
        icon: <HistoryQuery theme="outline" size="24" fill="#757575" />,
      },
    ],
  },
  {
    route: '/chs/main',
    name: '医保结算清单',
    icon: <AuditOutlined />,
    isSubSystem: true,
    order: 0,
    headerMenuKey: HeaderMenuKey.INSURANCE_SETTLEMENT,
    // order: 0,
    headerMenuItemActualRoute: '/chs/main/index',
    children: [
      {
        route: '/chs/main/index',
        name: '结算清单登记',
        icon: RecordSvg(),
      },
      {
        route: '/chs/main/management',
        name: '结算清单管理',
        icon: ManagementSvg(),
      },
      // {
      //   route: '/chs/main/monitor',
      //   name: '风险病例监测',
      //   icon: MonitorSvg(),
      // },
      {
        route: '/chs/main/encode',
        name: '编码后入组对比',
        icon: BarSvg(),
        children: [
          {
            route: '/chs/main/encode/diff',
            name: '编码后入组差异统计',
          },
          {
            route: '/chs/main/encode/details',
            name: '编码后入组差异明细',
          },
        ],
      },
      {
        route: '/chs/main/match',
        name: '医保反馈明细对比',
        icon: ContrastSvg(),
      },
      {
        route: '/chs/main/settleInfo',
        name: '医保反馈明细',
        icon: DetailSvg(),
      },
      {
        route: '/chs/analysis/drg/match',
        name: '医保反馈明细对比',
        icon: ContrastSvg(),
      },
      {
        route: '/chs/main/specialDisease',
        name: '特病单议',
        icon: SdSvg(),
        children: [
          {
            route: '/chs/main/specialDisease/discussion',
            name: '病例管理',
          },
          {
            route: '/chs/main/specialDisease/reportStats',
            name: '金额统计',
          },
          // {
          //   route: '/chs/main/specialDisease/importRecord',
          //   name: '申诉查询',
          // },
        ],
      },
      // {
      //   route: '/chs/main/qcAnalysis',
      //   name: '质控结果分析',
      //   icon: BarSvg(),
      //   children: [
      //     {
      //       route: '/chs/main/qcAnalysis/hosp',
      //       name: '全院质控结果分析',
      //     },
      //     {
      //       route: '/chs/main/qcAnalysis/dept',
      //       name: '科室质控结果分析',
      //     },
      //   ],
      // },
      // {
      //   route: '/chs/main/details',
      //   name: '质控明细',
      //   icon: DetailSvg(),
      //   children: [
      //     {
      //       route: '/chs/main/details/dept',
      //       name: '科室质控明细',
      //     },
      //   ],
      // },
    ],
  },
  {
    route: '/chs/analysis',
    name: 'DRG支付监控分析',
    icon: <AuditOutlined />,
    isSubSystem: true,
    // order: 3,
    headerMenuKey: HeaderMenuKey.DRG_SETTLEMENT,
    headerMenuItemActualRoute: '/chs/analysis/drg/index',
    children: [
      // {
      //   hideInConfiguration: false,
      //   route: '/chs/analysis/pre',
      //   name: '预支付分析',
      //   icon: PieSvg(),
      //   children: [
      //     {
      //       route: '/chs/analysis/pre/index',
      //       name: '医保支付分析汇总',
      //       // icon: PieSvg(),
      //     },
      //     {
      //       route: '/chs/analysis/pre/hosp',
      //       name: '全院支付盈亏分析',
      //       // icon: LineSvg(),
      //     },
      //     {
      //       route: '/chs/analysis/pre/dept',
      //       name: '科室支付盈亏分析',
      //       // icon: ScatterSvg(),
      //     },
      //     {
      //       route: '/chs/analysis/pre/disease',
      //       name: '病种支付盈亏分析',
      //       // icon: Bar2Svg(),
      //     },
      //   ],
      // },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/drg',
        name: '支付监控分析',
        icon: ScatterSvg(),
        children: [
          {
            route: '/chs/analysis/drg/hosp',
            name: '全院结算分析',
          },
          {
            route: '/chs/analysis/drg/majorPerfDept',
            name: '学科结算分析',
          },
          {
            route: '/chs/analysis/drg/dept',
            name: '科室结算分析',
          },
          {
            route: '/chs/analysis/drg/medTeam',
            name: '医疗组结算分析',
          },
          {
            route: '/chs/analysis/drg/group',
            name: '病组结算分析',
          },
          {
            route: '/chs/analysis/drg/pay',
            name: '入组覆盖率分析',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/warning',
        name: '在院病人监控',
        icon: MonitorCameraSvg(),
        children: [
          {
            route: '/chs/analysis/warning/hosp',
            name: '全院在院监控',
          },
          {
            route: '/chs/analysis/warning/dept',
            name: '科室在院监控',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/important',
        name: '重点病例监控',
        icon: MonitorImportantSvg(),
        children: [
          {
            route: '/chs/analysis/important/hosp',
            name: '全院异常病例监控',
          },
          {
            route: '/chs/analysis/important/majorPerfDept',
            name: '学科异常病例监控',
          },
          {
            route: '/chs/analysis/important/dept',
            name: '科室异常病例监控',
          },
          {
            route: '/chs/analysis/important/medTeam',
            name: '医疗组异常病例监控',
          },
          {
            route: '/chs/analysis/important/variableConditions',
            name: '高差异度病组监控',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/payDetail',
        name: '支付明细查询',
        icon: MonitorCameraSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/finalizedDetail',
        name: '医保反馈明细查询',
        icon: FinalizedSvg(),
        children: [
          {
            route: '/chs/analysis/finalizedDetail/searchResult',
            name: '明细查询',
          },
          {
            route: '/chs/analysis/finalizedDetail/importRecord',
            name: '上报记录',
          },
        ],
      },
      // 胸科特制menuData
      {
        route: '/chs/analysis/specialDisease',
        name: '特病单议',
        icon: SdSvg(),
        children: [
          {
            route: '/chs/analysis/specialDisease/discussion',
            name: '病例管理',
          },
          {
            route: '/chs/analysis/specialDisease/reportStats',
            name: '金额统计',
          },
        ],
      },
      {
        route: '/chs/analysis/report',
        name: '院内报表',
        icon: ReportSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/chs/analysis/insurConfiguration',
        name: '编码维护',
        icon: ToolSvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/chs/analysis/insurConfiguration/icde',
            name: '疾病维护',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/insurConfiguration/icdeReference',
            name: '疾病对照',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/insurConfiguration/operation',
            name: '手术维护',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/insurConfiguration/operationReference',
            name: '手术对照',
          },
        ],
      },

      {
        hideInConfiguration: false,
        route: '/chs/analysis/wiki',
        name: '知识库',
        icon: EscalateDictionarySvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/chs/analysis/wiki/insur',
            name: 'DRG分组知识库',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/wiki/ccMcc',
            name: 'CC/MCC知识库',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/wiki/icde',
            name: '标准诊断编码知识库',
          },
          {
            hideInConfiguration: false,
            route: '/chs/analysis/wiki/oper',
            name: '标准手术编码知识库',
          },
        ],
      },

      // {
      //   hideInConfiguration: false,
      //   route: '/chs/analysis/payRuleSettings',
      //   name: '支付审核规则配置',
      //   icon: MonitorImportantSvg(),
      // },
    ],
  },
  {
    route: '/chs/dip',
    name: 'DIP支付监控分析',
    icon: <AuditOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DIP_SETTLEMENT,
    order: Number.MAX_SAFE_INTEGER,
    headerMenuItemActualRoute: '/chs/dip/analysis/hosp',
    children: [
      {
        hideInConfiguration: false,
        route: '/chs/dip/analysis',
        name: '支付监控分析',
        icon: ScatterSvg(),
        children: [
          {
            route: '/chs/dip/analysis/hosp',
            name: '全院结算分析',
          },
          {
            route: '/chs/dip/analysis/majorPerfDept',
            name: '学科结算分析',
          },
          {
            route: '/chs/dip/analysis/dept',
            name: '科室结算分析',
          },
          {
            route: '/chs/dip/analysis/medTeam',
            name: '医疗组结算分析',
          },
          {
            route: '/chs/dip/analysis/group',
            name: '病组结算分析',
          },
          // {
          //   route: '/chs/dip/analysis/pay',
          //   name: '入组分析',
          // },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/dip/exportDetail',
        name: '分组明细',
        icon: DetailSvg(),
      },
      {
        hideInConfiguration: false,
        route: '/chs/dip/finalizedDetail',
        name: '医保反馈明细查询',
        icon: FinalizedSvg(),
        children: [
          {
            route: '/chs/dip/finalizedDetail/searchResult',
            name: '明细查询',
          },
          {
            route: '/chs/dip/finalizedDetail/importRecord',
            name: '上报记录',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/dip/warning',
        name: '在院病人监控',
        icon: MonitorCameraSvg(),
        children: [
          {
            route: '/chs/dip/warning/hosp',
            name: '全院在院监控',
          },
          {
            route: '/chs/dip/warning/dept',
            name: '科室在院监控',
          },
        ],
      },
      {
        hideInConfiguration: false,
        route: '/chs/dip/important',
        name: '重点病例监控',
        icon: MonitorImportantSvg(),
        children: [
          {
            route: '/chs/dip/important/hosp',
            name: '全院异常病例监控',
          },
          {
            route: '/chs/dip/important/majorPerfDept',
            name: '学科异常病例监控',
          },
          {
            route: '/chs/dip/important/dept',
            name: '科室异常病例监控',
          },
          {
            route: '/chs/dip/important/medTeam',
            name: '医疗组异常病例监控',
          },
          // {
          //   route: '/chs/dip/important/variableConditions',
          //   name: '高差异度病组监控',
          // },
        ],
      },
      // {
      //   hideInConfiguration: false,
      //   route: '/chs/dip/payRuleSettings',
      //   name: '支付审核规则配置',
      //   icon: MonitorImportantSvg(),
      // },
    ],
  },
  {
    route: '/dmr/examine',
    name: '病案首页质量评审',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_QUALITY_EXAMINE,
    headerMenuItemActualRoute: '/dmr/examine/reviewer',
    children: [
      {
        route: '/dmr/examine/reviewer',
        icon: <DocSearchTwo theme="outline" size="24" fill="#333" />,
        name: '待审病案',
      },
      {
        route: '/dmr/examine/auditee',
        icon: <Audit theme="outline" size="24" fill="#333" />,
        name: '评审结果',
      },
      {
        route: '/dmr/examine/management',
        icon: <DocumentFolder theme="outline" size="24" fill="#333" />,
        name: '评审管理',
      },
    ],
  },
  {
    route: '/qualityControl/main',
    name: '病案首页质量监管',
    icon: <CloudUploadOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_QUALITY_CONTROL,
    headerMenuItemActualRoute: '/qualityControl/main/analysis/hosp',
    children: [
      {
        route: '/qualityControl/main/details',
        name: '质控明细',
        icon: DetailSvg(),
        children: [
          {
            route: '/qualityControl/main/details/casebook',
            name: '病案质控明细',
          },
          {
            route: '/qualityControl/main/details/type',
            name: '分类质控明细',
          },
          // {
          //   route: '/qualityControl/main/report/dmrQuality',
          //   name: ' 医生-科室对照缺失',
          // },
        ],
      },
      {
        route: '/qualityControl/main/analysis',
        name: '质控结果分析',
        icon: BarSvg(),
        children: [
          {
            route: '/qualityControl/main/analysis/hosp',
            name: '全院质控分析',
          },
          // {
          //   route: '/qualityControl/main/analysis/dept',
          //   name: '科室质控结果分析',
          // },
          {
            route: '/qualityControl/main/analysis/match',
            name: '质控结果对比分析',
          },
        ],
      },
      {
        route: '/qualityControl/main/rule',
        name: '质控审核规则配置',
        icon: RuleSvg(),
      },
      {
        route: '/qualityControl/main/efficiency',
        name: '编码能效',
        icon: TableSvg(),
        children: [
          {
            route: '/qualityControl/main/efficiency/codeWorkerLoad',
            name: '工作效率管理',
          },
          {
            route: '/qualityControl/main/efficiency/codeValueQuantification',
            name: '编码价值洞察',
          },
        ],
      },
      {
        route: '/qualityControl/main/customRule',
        name: '自定义质控规则配置',
        icon: CustomRuleSvg(),
      },
    ],
  },
  {
    route: '/qualityControl/doctor',
    name: '医生首页质量监管',
    icon: <CloudUploadOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_DOCT_QUALITY_CONTROL,
    headerMenuItemActualRoute: '/qualityControl/doctor/analysis/hosp',
    children: [
      {
        route: '/qualityControl/doctor/details',
        name: '质控明细',
        icon: DetailSvg(),
        children: [
          {
            route: '/qualityControl/doctor/details/casebook',
            name: '病案质控明细',
          },
          {
            route: '/qualityControl/doctor/details/type',
            name: '分类质控明细',
          },
        ],
      },
      {
        route: '/qualityControl/doctor/analysis/hosp',
        name: '全院质控结果分析',
        icon: BarSvg(),
      },
      {
        route: '/qualityControl/doctor/analysis/dept',
        name: '科室质控结果分析',
        icon: PieSvg(),
      },
    ],
  },
  {
    route: '/report',
    name: '上报数据管理',
    icon: <DotChartOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT,
    headerMenuItemActualRoute: '/report/wt',
    children: [
      {
        route: '/report/wt',
        name: '卫统上报',
        icon: ReportSvg(),
      },
      {
        route: '/report/hqms',
        name: '国考上报',
        icon: Report2Svg(),
      },
    ],
  },
  {
    route: '/uniHqms',
    name: '国家公立医院绩效考核',
    icon: <FlagOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.MEDICAL_QUALITY_ANALYSIS,
    headerMenuItemActualRoute: '/uniHqms/oper/hosp',
    children: [
      // 新  国考  版本 不要这个
      // {
      //   route: '/uniHqms/score',
      //   name: '国考核心指标监控',
      //   icon: ServiceabilitySvg(),
      // },
      {
        route: '/uniHqms/oper',
        name: '外科能力',
        icon: OperSvg(),
        children: [
          {
            route: '/uniHqms/oper/hosp',
            name: '全院手术',
          },
          {
            route: '/uniHqms/oper/dept',
            name: '科室手术',
          },
          {
            route: '/uniHqms/oper/medTeam',
            name: '医疗组手术',
          },
        ],
      },
      {
        route: '/uniHqms/diseaseType',
        name: '病种结构',
        icon: DiseaseTypeSvg(),
        children: [
          {
            route: '/uniHqms/diseaseType/hospLevel',
            name: '全院病种结构',
          },
          {
            route: '/uniHqms/diseaseType/deptLevel',
            name: '科室病种结构',
          },
          {
            route: '/uniHqms/diseaseType/medTeamLevel',
            name: '医疗组病种结构',
          },
        ],
      },
      {
        route: '/uniHqms/cmi',
        name: '综合能力CMI',
        icon: CmiSvg(),
        children: [
          {
            route: '/uniHqms/cmi/hospLevelCmi',
            name: '全院CMI',
          },
          {
            route: '/uniHqms/cmi/deptLevelCmi',
            name: '科室CMI',
          },
          {
            route: '/uniHqms/cmi/medTeamLevelCmi',
            name: '医疗组CMI',
          },
        ],
      },
      {
        route: '/uniHqms/quality',
        name: '医疗质量',
        icon: QualitySvg(),
        children: [
          {
            route: '/uniHqms/quality/hosp',
            name: '全院医疗质量',
          },
          {
            route: '/uniHqms/quality/dept',
            name: '科室医疗质量',
          },
          {
            route: '/uniHqms/quality/medTeam',
            name: '医疗组医疗质量',
          },
          {
            route: '/uniHqms/operComplicationComposition/hosp',
            name: '全院术后并发症',
          },
          {
            route: '/uniHqms/operComplicationComposition/dept',
            name: '科室术后并发症',
          },
          {
            route: '/uniHqms/operComplicationComposition/medTeam',
            name: '医疗组术后并发症',
          },
        ],
      },
      {
        route: '/uniHqms/sd',
        name: '单病种质量控制',
        icon: SdSvg(),
        children: [
          {
            route: '/uniHqms/sd/hosp',
            name: '全院单病种质量控制',
          },
          {
            route: '/uniHqms/sd/dept',
            name: '科室单病种质量控制',
          },
          {
            route: '/uniHqms/sd/medTeam',
            name: '医疗组单病种质量控制',
          },
        ],
      },
      {
        route: '/uniHqms/tcm',
        name: '中医指标',
        icon: ChineseMedicineSvg(),
        children: [
          {
            route: '/uniHqms/tcm/hosp',
            name: '全院中医指标',
          },
          {
            route: '/uniHqms/tcm/dept',
            name: '科室中医指标',
          },
          {
            route: '/uniHqms/tcm/medTeam',
            name: '医疗组中医指标',
          },
        ],
      },
    ],
  },
  {
    route: '/grade',
    name: '等级医院评审',
    icon: <FlagOutlined />,
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.MEDICAL_QUALITY_ANALYSIS,
    headerMenuItemActualRoute: '/grade/serviceability/hosp',
    children: [
      {
        route: '/grade/serviceability',
        name: '医疗服务能力',
        icon: ServiceabilitySvg(),
        children: [
          {
            route: '/grade/serviceability/hosp',
            name: '全院医疗服务能力',
          },
          {
            route: '/grade/serviceability/dept',
            name: '科室医疗服务能力',
          },
          {
            route: '/grade/serviceability/medTeam',
            name: '医疗组服务能力',
          },
        ],
      },
      {
        route: '/grade/quality',
        name: '医疗质量',
        icon: QualitySvg(),
        children: [
          {
            route: '/grade/quality/hosp',
            name: '全院医疗质量',
          },
          {
            route: '/grade/quality/dept',
            name: '科室医疗质量',
          },
          {
            route: '/grade/quality/medTeam',
            name: '医疗组质量',
          },
        ],
      },
      {
        route: '/grade/complication',
        name: '医疗安全',
        icon: ComplicationSvg(),
        children: [
          {
            route: '/grade/complication/hosp',
            name: '全院医疗安全',
          },
          {
            route: '/grade/complication/dept',
            name: '科室医疗安全',
          },
          {
            route: '/grade/complication/medTeam',
            name: '医疗组安全',
          },
        ],
      },
      {
        route: '/grade/sd',
        name: '单病种质量控制',
        icon: SdSvg(),
        children: [
          {
            route: '/grade/sd/hosp',
            name: '全院单病种质量控制',
          },
          {
            route: '/grade/sd/dept',
            name: '科室单病种质量控制',
          },
          {
            route: '/grade/sd/medTeam',
            name: '医疗组单病种质量控制',
          },
        ],
      },
    ],
  },
  {
    route: '/statsAnalysis',
    icon: <ReconciliationOutlined />,
    name: '病案统计分析',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT,
    headerMenuItemActualRoute: '/statsAnalysis/combineQuery',
    children: [
      {
        route: '/statsAnalysis/combineQuery',
        name: '组合查询',
        icon: CombineQuerySvg(),
      },
      {
        route: '/statsAnalysis/report/hospital',
        name: '院内报表',
        icon: ReportSvg(),
      },
      {
        route: '/statsAnalysis/report/public',
        name: '对外病案数据查询',
        icon: <DataScreen theme="outline" size="24" fill="#757575" />,
      },
      {
        route: '/statsAnalysis/customDiseaseStats',
        name: '自定义病种统计',
        icon: CustomDiseaseSvg(),
      },
      {
        route: '/statsAnalysis/surgerySequence',
        name: '手术顺位统计',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="3.60938"
              y="36.5337"
              width="38"
              height="6"
              rx="2"
              transform="rotate(-10 3.60938 36.5337)"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M44 40L40 36"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M8 4L26.3848 22.3848L22.1421 26.6274C22.1421 26.6274 12.2426 16.7279 9.41419 13.8995C6.58577 11.071 6.58575 9.65682 6.58577 8.24262C6.58579 6.82843 8 4 8 4Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M8 4L26 22L35 31"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
          </svg>
        ),
      },
      {
        route: '/statsAnalysis/diseaseSequence',
        name: '疾病顺位统计',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M24 44.0002C25.3357 44.0002 26.3638 40.25 27.6262 40.0002C28.9267 39.743 30.4588 42.979 31.6554 42.4828C32.8729 41.9779 32.4053 38.8668 33.4874 38.1424C34.5781 37.4122 37.2174 39.0671 38.1421 38.1424C39.0668 37.2177 36.6639 34.0909 37.3941 33.0002C38.1186 31.9181 41.9777 32.8732 42.4825 31.6557C42.9787 30.4591 39.179 28.1133 39.4362 26.8128C39.686 25.5504 44 25.3359 44 24.0002C44 22.6646 40.2497 21.3595 40 20.0971C39.7427 18.7966 42.9787 17.5414 42.4825 16.3448C41.9777 15.1273 38.1186 16.0824 37.3941 15.0002C36.6639 13.9096 39.0668 10.7828 38.1421 9.85811C37.2174 8.9334 34.0907 10.9491 33 10.219C31.9178 9.49451 32.8729 6.02257 31.6554 5.51772C30.4588 5.02154 28.3005 7.90462 27 7.64735C25.7376 7.39762 25.3357 4.00024 24 4.00024C22.6643 4.00024 22.12 7.39762 20.8577 7.64735C19.5571 7.90462 17.5412 5.02154 16.3446 5.51772C15.1271 6.02257 16.0822 9.13365 15 9.85811C13.9093 10.5883 10.7826 8.9334 9.85786 9.85811C8.93315 10.7828 11.5248 13.9096 10.7947 15.0002C10.0702 16.0824 6.02232 15.1273 5.51747 16.3448C5.02129 17.5414 8.25728 19.2395 8 20.54C7.75028 21.8024 4 22.6646 4 24.0002C4 25.3359 8.09721 25.9273 8.34694 27.1897C8.60421 28.4902 5.02129 30.4591 5.51747 31.6557C6.02232 32.8732 9.80669 31.9181 10.5312 33.0002C11.2613 34.0909 8.93315 37.2177 9.85786 38.1424C10.7826 39.0671 13.9093 36.855 15 37.5851C16.0822 38.3096 15.1271 41.9779 16.3446 42.4828C17.5412 42.979 19.5571 39.743 20.8577 40.0002C22.12 40.25 22.6643 44.0002 24 44.0002Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="bevel"
            />
            <path
              d="M29 29.0002C31.2091 29.0002 33 27.2094 33 25.0002C33 22.7911 31.2091 21.0002 29 21.0002C26.7909 21.0002 25 22.7911 25 25.0002C25 27.2094 26.7909 29.0002 29 29.0002Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="bevel"
            />
            <path
              d="M16.5 26.0002C17.8807 26.0002 19 24.881 19 23.5002C19 22.1195 17.8807 21.0002 16.5 21.0002C15.1193 21.0002 14 22.1195 14 23.5002C14 24.881 15.1193 26.0002 16.5 26.0002Z"
              fill="#757575"
            />
          </svg>
        ),
      },
      {
        route: '/statsAnalysis/simpleQuery',
        name: '简单查询',
        icon: simpleQuerySvg(),
      },
    ],
  },
  {
    route: '/operationalDataManagement',
    name: '运营数据管理',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT,
    headerMenuItemActualRoute: '/operationalDataManagement/in/byDay',
    icon: <FundProjectionScreenOutlined />,
    children: [
      {
        route: '/operationalDataManagement/in',
        name: '住院动态登记',
        icon: InpatientSvg(),
        children: [
          {
            route: '/operationalDataManagement/in/byDay',
            name: '每日住院登记',
          },
          {
            route: '/operationalDataManagement/in/byDept',
            name: '科室住院登记',
          },
          {
            route: '/operationalDataManagement/in/byDayWithWard',
            name: '病区每日住院登记',
          },
          {
            route: '/operationalDataManagement/in/hierarchyBed',
            name: '床位数管理',
          },
          {
            route: '/operationalDataManagement/in/proofread',
            name: '住院登记校对',
          },
          {
            route: '/operationalDataManagement/in/dailyProofread',
            name: '住院日报校对',
          },
        ],
      },
      {
        route: '/operationalDataManagement/out',
        name: '门急动态登记',
        icon: OutpatientSvg(),
        children: [
          {
            route: '/operationalDataManagement/out/byDay',
            name: '每日门急登记',
          },
          {
            route: '/operationalDataManagement/out/byDept',
            name: '科室门急登记',
          },
        ],
      },
      {
        route: '/operationalDataManagement/outDoctor',
        name: '医生门急动态登记',
        icon: OutpatientDoctorSvg(),
        children: [
          {
            route: '/operationalDataManagement/outDoctor/byDay',
            name: '每日医生门急登记',
          },
          {
            route: '/operationalDataManagement/outDoctor/byDept',
            name: '科室医生门急登记',
          },
        ],
      },
      {
        route: '/operationalDataManagement/obs',
        name: '观察动态登记',
        icon: ObspatientSvg(),
        children: [
          {
            route: '/operationalDataManagement/obs/byDay',
            name: '每日观察登记',
          },
          {
            route: '/operationalDataManagement/obs/byDept',
            name: '科室观察登记',
          },
          {
            route: '/operationalDataManagement/obs/hierarchyBed',
            name: '床位数管理',
          },
        ],
      },
      {
        route: '/operationalDataManagement/workLoad',
        name: '医技管理',
        icon: WorkLoadItemSvg(),
        children: [
          {
            route: '/operationalDataManagement/workLoad/medicalTechWorkload',
            name: '医技工作量',
          },
          {
            route: '/operationalDataManagement/workLoad/hospWorkloadItemCheck',
            name: '科室医技配置',
          },
          {
            route: '/operationalDataManagement/workLoad/workloadItemCheck',
            name: '医技项目配置',
          },
        ],
      },
      {
        route: '/operationalDataManagement/inHospManagement',
        name: '住院动态管理',
        icon: ODMManagementSvg(),
      },
      {
        route: '/operationalDataManagement/outPatientManagement',
        name: '门急动态管理',
        icon: ODMOutManagementSvg(),
      },
    ],
  },
  {
    route: '/tracer',
    name: '病案示踪',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.DMR_INDEX_AND_MANAGEMENT,
    headerMenuItemActualRoute: '/tracer/traceRecordList',
    icon: <AimOutlined />,
    children: [
      {
        route: '/tracer/traceRecordList',
        name: '示踪总体病案查询',
        icon: RecordListSvg(),
      },
      {
        route: '/tracer/mrRoomSignInAnalysis',
        name: '病案示踪分析',
        icon: AnalysisSvg(),
      },
      {
        route: '/tracer/askForPaySearch',
        name: '催缴查询',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M40 23V14L31 4H10C8.89543 4 8 4.89543 8 6V42C8 43.1046 8.89543 44 10 44H22"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M27 33H41"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M27 39H41"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M41 33L36 28"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M32 44L27 39"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M30 4V14H40"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/tracer/ward',
        name: '病区签收',
        icon: WardSvg(),
        children: [
          {
            route: '/tracer/ward/search',
            name: '病区签收查询',
          },
          {
            route: '/tracer/ward/signin',
            name: '病区签收操作',
          },
        ],
      },
      {
        route: '/tracer/mrRoom',
        name: '病案签收',
        icon: SigninSvg(),
        children: [
          {
            route: '/tracer/mrRoom/search',
            name: '病案查询管理',
          },
          {
            route: '/tracer/mrRoom/signin',
            name: '病案签收操作',
          },
          {
            route: '/tracer/mrRoom/statistic',
            name: '病案签收统计',
            children: [
              {
                route: '/tracer/mrRoom/statistic/bySignDate',
                name: '按签收日期',
              },
              {
                route: '/tracer/mrRoom/statistic/byOutDate',
                name: '按出院日期',
              },
            ],
          },
        ],
      },
      {
        route: '/tracer/archive',
        name: '病案归档',
        icon: ManagementSvg(),
        children: [
          {
            route: '/tracer/archive/search',
            name: '归档查询',
          },
          {
            route: '/tracer/archive/register',
            name: '归档登记',
          },
        ],
      },
      {
        route: '/tracer/seal',
        name: '病案封存',
        icon: SealSvg(),
        children: [
          {
            route: '/tracer/seal/search',
            name: '封存查询',
          },
          {
            route: '/tracer/seal/register',
            name: '封存登记',
          },
        ],
      },
      {
        route: '/tracer/borrow',
        name: '病案借阅',
        icon: SearchSvg(),
        children: [
          {
            route: '/tracer/borrow/search',
            name: '借阅查询',
          },
          {
            route: '/tracer/borrow/borrowRegister',
            name: '借阅登记',
          },
          {
            route: '/tracer/borrow/returnRegister',
            name: '归还登记',
          },
          {
            route: '/tracer/borrow/askForReturnSearch',
            name: '催还查询',
          },
          {
            route: '/tracer/borrow/userBorrowTasks',
            name: '医生批量借阅管理',
          },
          {
            route: '/tracer/borrow/borrowTasks',
            name: '病案室批量借阅管理',
          },
        ],
      },
      {
        route: '/tracer/print',
        name: '病案复印',
        icon: printSvg(),
        children: [
          {
            route: '/tracer/print/search',
            name: '复印记录查询',
          },
          {
            route: '/tracer/print/register',
            name: '复印登记',
          },
        ],
      },
      {
        route: '/tracer/dmrSignOut',
        name: '病案出库',
        icon: outgoingSvg(),
        children: [
          {
            route: '/tracer/dmrSignOut/search',
            name: '出库记录查询',
          },
          {
            route: '/tracer/dmrSignOut/discharge',
            name: '出库登记',
          },
        ],
      },
      // {
      //   route: '/tracer/remind',
      //   name: '病案催缴',
      //   icon: (
      //     <svg
      //       width="24"
      //       height="24"
      //       viewBox="0 0 48 48"
      //       fill="none"
      //       xmlns="http://www.w3.org/2000/svg"
      //     >
      //       <path
      //         d="M40 23V14L31 4H10C8.89543 4 8 4.89543 8 6V42C8 43.1046 8.89543 44 10 44H22"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M27 33H41"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M27 39H41"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M41 33L36 28"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M32 44L27 39"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M30 4V14H40"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //     </svg>
      //   ),
      //   children: [
      //     {
      //       route: '/tracer/remind/search',
      //       name: '催缴查询',
      //     },
      //     // {
      //     //   route: '/tracer/remind/register',
      //     //   name: '催缴登记',
      //     // },
      //   ],
      // },
    ],
  },
  // 医保基金监控
  {
    route: '/medicalInsuranceFundMonitor',
    name: '医保基金监控',
    icon: <FundProjectionScreenOutlined />,
    isSubSystem: true,
    // order: 1,
    headerMenuKey: HeaderMenuKey.MEDICAL_INSURANCE_FUND_MONITOR,
    headerMenuItemActualRoute: '/medicalInsuranceFundMonitor/appeal',
    children: [
      {
        route: '/medicalInsuranceFundMonitor/appeal',
        name: '扣费申诉',
        icon: FundSvg(),
      },
      {
        route: '/medicalInsuranceFundMonitor/rule',
        name: '审核规则',
        icon: FundSvg(),
        children: [
          {
            route: '/medicalInsuranceFundMonitor/rule/medicine/ruleControl',
            name: '限制性药品规则库',
          },
        ],
      },
      {
        route: '/medicalInsuranceFundMonitor/review/dashboard/prior',
        name: '驾驶舱测试',
        icon: FundSvg(),
      },
    ],
  },
  // DRG 数据管理
  {
    route: '/drgDataManager',
    name: 'DRG数据管理',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.MEDICAL_QUALITY_ANALYSIS,
    headerMenuItemActualRoute: '/drgDataManager/hqmsDataManagement',
    icon: (
      <AreaChartOutlined
        onPointerEnterCapture={() => {}}
        onPointerLeaveCapture={() => {}}
      />
    ),
    children: [
      {
        route: '/drgDataManager/hqmsCardImport',
        name: '数据上传',
        icon: UploadSvg(),
      },
      {
        route: '/drgDataManager/hqmsDataManagement',
        name: '数据管理',
        icon: DataManagementSvg(),
      },
      {
        route: '/drgDataManager/maintenanceDataCnt',
        name: '病案数据分组',
        icon: MaintenanceDataCntSvg(),
      },
      {
        route: '/drgDataManager/maintenanceRecordDataCnt',
        name: '病案数据分组记录',
        icon: MaintenanceDataRecordCntSvg(),
      },
    ],
  },
  {
    route: '/drgHospDecisionSupport',
    name: 'DRG医疗质量评价',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.MEDICAL_QUALITY_ANALYSIS,
    headerMenuItemActualRoute: '/drgHospDecisionSupport/hospLevel/Cmi',
    icon: <RadarChartOutlined />,
    children: [
      // {
      //   route: '/drgHospDecisionSupport/hqmsCardImport',
      //   name: '数据上传',
      //   icon: UploadSvg(),
      // },
      // {
      //   route: '/drgHospDecisionSupport/hqmsDataManagement',
      //   name: '数据管理',
      //   icon: DataManagementSvg(),
      // },
      {
        route: '/drgHospDecisionSupport/cmi',
        name: '综合能力CMI',
        icon: CmiSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/hospLevel/Cmi',
            name: '全院CMI',
          },
          {
            route: '/drgHospDecisionSupport/deptLevel/Cmi',
            name: '科室CMI',
          },
          {
            route: '/drgHospDecisionSupport/medTeamLevel/Cmi',
            name: '医疗组CMI',
          },
          {
            route: '/drgHospDecisionSupport/majoePerfDept/Cmi',
            name: '绩效科室CMI',
          },
          {
            route: '/drgHospDecisionSupport/hospLevel/diseaseType',
            name: '全院病种结构',
          },
          {
            route: '/drgHospDecisionSupport/deptLevel/diseaseType',
            name: '科室病种结构',
          },
          {
            route: '/drgHospDecisionSupport/medTeamLevel/diseaseType',
            name: '医疗组病种结构',
          },
          {
            route: '/drgHospDecisionSupport/hospLevel/groupType',
            name: '全院DRG组结构',
          },
          {
            route: '/drgHospDecisionSupport/deptLevel/groupType',
            name: '科室DRG组结构',
          },
          {
            route: '/drgHospDecisionSupport/medTeamLevel/groupType',
            name: '医疗组DRG组结构',
          },
        ],
      },
      {
        route: '/drgHospDecisionSupport/difficultCases',
        name: '疑难病例',
        icon: MonitorImportantSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/difficultCases/hosp',
            name: '全院疑难病例',
          },
          {
            route: '/drgHospDecisionSupport/difficultCases/dept',
            name: '科室疑难病例',
          },
          {
            route: '/drgHospDecisionSupport/difficultCases/medTeam',
            name: '医疗组疑难病例',
          },
        ],
      },
      {
        route: '/drgHospDecisionSupport/surgicalAbility',
        name: '外科能力',
        icon: OperSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/surgicalAbility/hosp',
            name: '全院手术',
          },
          {
            route: '/drgHospDecisionSupport/surgicalAbility/dept',
            name: '科室手术',
          },
          {
            route: '/drgHospDecisionSupport/surgicalAbility/medTeam',
            name: '医疗组手术',
          },
        ],
      },
      {
        route: '/drgHospDecisionSupport/sdComposition',
        name: '重点监控',
        icon: SdSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/sdComposition/hosp',
            name: '全院重点监控病种',
          },
          {
            route: '/drgHospDecisionSupport/sdComposition/dept',
            name: '科室重点监控病种',
          },
          {
            route: '/drgHospDecisionSupport/sdComposition/medTeam',
            name: '医疗组重点监控病种',
          },
        ],
      },
      {
        route: '/drgHospDecisionSupport/medicalQuality',
        name: '医疗质量',
        icon: SealSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/medicalQuality/hosp',
            name: '死亡病例统计',
          },
          {
            route: '/drgHospDecisionSupport/medicalQuality/dept',
            name: '科室死亡病例统计',
          },
          {
            route: '/drgHospDecisionSupport/medicalQuality/medTeam',
            name: '医疗组死亡病例统计',
          },
        ],
      },
      {
        route: '/drgHospDecisionSupport/detail',
        name: '院级明细',
        icon: DetailSvg(),
        children: [
          {
            route: '/drgHospDecisionSupport/detail/hospDrgs',
            name: 'DRG明细分组',
          },
          {
            route: '/drgHospDecisionSupport/detail/hospOper',
            name: '手术明细',
          },
          {
            route: '/drgHospDecisionSupport/detail/hospSd',
            name: '重点监控病种明细页面',
          },
        ],
      },
    ],
  },

  {
    route: '/import',
    name: '病案数据导入',
    isSubSystem: true,
    headerMenuKey: HeaderMenuKey.SYSTEM_CONFIGURATION,
    headerMenuItemActualRoute: '/import/pullDmrCards',
    icon: <ImportOutlined />,
    children: [
      {
        route: '/import/pullDmrCards',
        name: '数据批量导入',
        icon: ImportSvg(),
      },
      {
        route: '/import/pullDmrSingleCard',
        name: '数据单份导入',
        icon: TransactionSvg(),
      },
      {
        route: '/import/pullDmrCardFees',
        name: '数据费用导入',
        icon: FinancingSvg(),
      },
      {
        route: '/import/interfaceSetUpload',
        name: '文件单份导入',
        icon: ExcelSvg(),
      },
      {
        route: '/import/interfaceSetUploadCenterSettle',
        name: '反馈明细导入',
        icon: HisExcelSvg(),
      },
    ],
  },
  {
    route: '/reportSys',
    headerMenuKey: HeaderMenuKey.SYSTEM_CONFIGURATION,
    headerMenuItemActualRoute: '/reportSys/dataFlow',
    name: '维护',
    isSubSystem: true,
    icon: <SettingOutlined />,
    // headerMenuKey: HeaderMenuKey.SYSTEM_CONFIGURATION,
    children: [
      {
        route: '/reportSys/selfDefinedReport',
        name: '自定义报表配置',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M26 24L44 24"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14 24L18 24"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M18 38H44"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M6 38H10"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M18 10H44"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M6 10H10"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/statDeptsSetting',
        name: '统计科室设置',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M38 20H18V28H38V20Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M32 6H18V14H32V6Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M44 34H18V42H44V34Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              d="M17 10H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17 24H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17 38H5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M5 44V4"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/kpiSettings',
        name: 'KPI配置管理',
        icon: KpiSettingsSvg(),
      },
      {
        route: '/reportSys/dataFlow',
        name: '首页数据管理',
        icon: EscalateDictionarySvg(),
      },
      // Mark维护
      // {
      //   route: '/reportSys/UserDataCnt',
      //   name: '用户数据量管理',
      //   icon: UserDataManager(),
      // },
      // {
      //   route: '/reportSys/MaintenanceDataCnt',
      //   name: '病案数据分组',
      //   icon: MaintenanceDataCntSvg(),
      // },
      // {
      //   route: '/reportSys/MaintenanceRecordDataCnt',
      //   name: '病案数据分组记录',
      //   icon: MaintenanceDataRecordCntSvg(),
      // },
      // Mark维护结束
      {
        route: '/reportSys/dataReload',
        name: '重载系统表',
        icon: <RefreshOne theme="outline" size="24" fill="#757575" />,
      },
      {
        route: '/reportSys/insurDataFlow',
        name: '医保系数据管理',
        icon: EscalateDictionarySvg(),
      },
      {
        route: '/reportSys/backendReport',
        name: 'Udf管理',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 36V30"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M20 40H6"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M28 40H42"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M28 40C28 42.2091 26.2091 44 24 44C21.7909 44 20 42.2091 20 40C20 37.7909 21.7909 36 24 36C26.2091 36 28 37.7909 28 40Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M37 17C37 24.1797 31.1797 30 24 30C16.8203 30 11 24.1797 11 17M37 17C37 9.8203 31.1797 4 24 4C16.8203 4 11 9.8203 11 17M37 17H11"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M29 17C29 24.1797 26.7614 30 24 30C21.2386 30 19 24.1797 19 17C19 9.8203 21.2386 4 24 4C26.7614 4 29 9.8203 29 17Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M37 17H11"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/InterfaceSet',
        name: '数据导入配置',
        icon: ImportAndExportSvg(),
      },
      {
        route: '/reportSys/globalConfiguration',
        name: '全局系统设置',
        icon: GlobalConfigurationSvg(),
        children: [
          {
            hideInConfiguration: false,
            route: '/reportSys/globalConfiguration/moduleMenu',
            name: '菜单设置',
          },
          {
            hideInConfiguration: false,
            route: '/reportSys/globalConfiguration/menuOrder',
            name: '菜单顺序设置',
          },
        ],
      },
      {
        route: '/reportSys/systemInfo',
        name: '系统信息',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="round"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M24 11C25.3807 11 26.5 12.1193 26.5 13.5C26.5 14.8807 25.3807 16 24 16C22.6193 16 21.5 14.8807 21.5 13.5C21.5 12.1193 22.6193 11 24 11Z"
              fill="#757575"
            />
            <path
              d="M24.5 34V20H23.5H22.5"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M21 34H28"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/rulesScore',
        name: '质控审核规则配置',
        icon: RuleSvg(),
      },
      {
        route: '/reportSys/qualityControlRuleConfiguration',
        name: '新版质控审核规则配置',
        icon: RuleSvg(),
      },
      {
        route: '/reportSys/jsonExprRules',
        name: '自定义质控规则',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M34.0003 41L44 24L34.0003 7H14.0002L4 24L14.0002 41H34.0003Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="bevel"
            />
            <path
              d="M24 29C26.7614 29 29 26.7614 29 24C29 21.2386 26.7614 19 24 19C21.2386 19 19 21.2386 19 24C19 26.7614 21.2386 29 24 29Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinejoin="bevel"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/_health',
        name: '健康检测',
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 33V39C6 40.6569 7.34315 42 9 42H15"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M33 42H39C40.6569 42 42 40.6569 42 39V33"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M42 15V9C42 7.34315 40.6569 6 39 6H33"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M6 15V9C6 7.34315 7.34315 6 9 6H15"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
            <path
              d="M34 24.0001L24 34.0001C24 34.0001 15.0002 25.0001 14 24.0001C12.9998 23.0001 12.5 21.0001 14 19.0001C15.5 17.0001 18.5 17.0001 20 18.0001C21.5001 19.0001 22.0001 20.0001 24 20.0001C26 20.0001 26.5001 19.0001 28 18.0001C29.5 17.0001 32.5003 17.0001 34.0003 19.0001C35.5003 21.0001 35 23.0001 34 24.0001Z"
              fill="none"
              stroke="#757575"
              strokeWidth="4"
              strokeLinecap="square"
              strokeLinejoin="bevel"
            />
          </svg>
        ),
      },
      {
        route: '/reportSys/settingColumnDefs',
        name: '参数配置',
        icon: DictionarySvg(),
      },
      {
        route: '/reportSys/insurPayRuleSettings',
        name: '支付审核规则配置',
        icon: MonitorImportantSvg(),
      },
      {
        route: '/reportSys/clientApiKey',
        name: '前置接口配置',
        icon: TransferSvg(),
      },
      {
        route: '/reportSys/externalCalcRecord',
        name: '前置分组结果查询',
        icon: ExternalSvg(),
      },
      {
        route: '/reportSys/recurringJob',
        name: '周期性任务',
        icon: RecurringJobSvg(),
      },
      {
        route: '/reportSys/dictModule',
        name: '字典库汇总',
        icon: ManagementSvg(),
      },
      {
        route: '/reportSys/qualityExamine',
        name: '人工质控业务配置',
        icon: EnquireSvg(),
      },
      // {
      //   route: '/reportSys/deptSettings',
      //   name: '科室管理',
      //   icon: (
      //     <svg
      //       width="24"
      //       height="24"
      //       viewBox="0 0 48 48"
      //       fill="none"
      //       xmlns="http://www.w3.org/2000/svg"
      //     >
      //       <path
      //         d="M8 28C10.2091 28 12 26.2091 12 24C12 21.7909 10.2091 20 8 20C5.79086 20 4 21.7909 4 24C4 26.2091 5.79086 28 8 28Z"
      //         fill="none"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M8 12C9.10457 12 10 11.1046 10 10C10 8.89543 9.10457 8 8 8C6.89543 8 6 8.89543 6 10C6 11.1046 6.89543 12 8 12Z"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M8 40C9.10457 40 10 39.1046 10 38C10 36.8954 9.10457 36 8 36C6.89543 36 6 36.8954 6 38C6 39.1046 6.89543 40 8 40Z"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M20 24H44"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M20 38H44"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //       <path
      //         d="M20 10H44"
      //         stroke="#757575"
      //         strokeWidth="4"
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //       />
      //     </svg>
      //   ),
      // },
    ],
  },
  {
    route: '/wiki',
    name: '知识库',
    isSubSystem: true,
    children: [
      {
        route: '/wiki/insur',
        name: '医保DRG分组方案',
        icon: EscalateDictionarySvg(),
        children: [
          {
            route: '/wiki/insur/index',
            name: 'DRG分组知识库',
          },
          {
            route: '/wiki/insur/ccMcc',
            name: 'CC/MCC知识库',
          },
          {
            route: '/wiki/insur/icde',
            name: '标准诊断编码知识库',
          },
          {
            route: '/wiki/insur/oper',
            name: '标准手术编码知识库',
          },
        ],
      },
      {
        route: '/wiki/dip',
        name: '医保DIP分组方案',
        icon: InstitutionSvg(),
        children: [
          {
            route: '/wiki/dip/index',
            name: 'DIP分组知识库',
          },
          {
            route: '/wiki/dip/icde',
            name: '标准诊断编码知识库',
          },
          {
            route: '/wiki/dip/oper',
            name: '标准手术编码知识库',
          },
        ],
      },
      {
        route: '/wiki/dmr',
        name: '病案首页知识库',
        icon: <DatabaseCode theme="outline" size="24" fill="#333" />,
        children: [
          {
            route: '/wiki/dmr/icde',
            name: '首页诊断编码知识库',
          },
          {
            route: '/wiki/dmr/oper',
            name: '首页手术操作编码知识库',
          },
        ],
      },
    ],
  },
  {
    route: '/personalMessages',
    name: '私信',
    isSubSystem: true,
    children: [
      {
        route: '/personalMessages/index',
        name: '私信列表',
        icon: <BookOne />,
      },
    ],
  },

  {
    route: '/qcStandalone',
    name: '病案首页质控复核',
    headerMenuKey: HeaderMenuKey.DMR_INDEX_QUALITY_EXAMINE,
    headerMenuItemActualRoute: '/qcStandalone/details/type',
    isSubSystem: true,
    children: [
      {
        route: '/qcStandalone/details',
        name: '质控明细',
        icon: DetailSvg(),
        children: [
          {
            route: '/qcStandalone/details/casebook',
            name: '病案质控明细',
          },
          {
            route: '/qcStandalone/details/type',
            name: '分类质控明细',
          },
          {
            route: '/qcStandalone/details/admin/casebook',
            name: '病案质控明细监控',
          },
          {
            route: '/qcStandalone/details/admin/type',
            name: '分类质控明细监控',
          },
        ],
      },
      {
        route: '/qcStandalone/review',
        name: '首页质控结果复核',
        icon: InstitutionSvg(),
        children: [
          {
            route: '/qcStandalone/review/detail',
            name: '质控结果复核',
          },
          {
            route: '/qcStandalone/review/progress',
            name: '质控复核进度管理',
          },
          {
            route: '/qcStandalone/review/closed',
            name: '待论证质控规则分析',
          },
          {
            route: '/qcStandalone/review/invalid',
            name: '无效质控结果统计',
          },
        ],
      },
      {
        route: '/qcStandalone/rules/config',
        name: '质控审核规则配置',
        icon: <DatabaseCode theme="outline" size="24" fill="#333" />,
      },
      {
        route: '/qcStandalone/dmr/management',
        name: '已登记病案管理',
        icon: ManagementSvg(),
      },
      {
        route: '/qcStandalone/import',
        name: '病案数据导入',
        icon: <ImportOutlined />,
        children: [
          {
            route: '/qcStandalone/import/pullDmrCards',
            name: '数据批量导入',
          },
          {
            route: '/qcStandalone/import/pullDmrSingleCard',
            name: '数据单份导入',
          },
        ],
      },
    ],
  },
];

export const getBreadcrumbFromMenuData = (
  path: string,
  onlyOneHeaderId: boolean,
) => {
  let crumbLabels = [];
  let filterMenuData = (global['patchedMenuData'] ?? menuData).slice();

  let currentMenuItem = findDeep(
    filterMenuData,
    (o) => {
      return o?.route === path;
    },
    { childrenPath: ['children'] },
  );

  if (!isEmptyValues(currentMenuItem?.value)) {
    currentMenuItem?.value?.parentRoutes?.forEach((parentRoute) => {
      let parentMenuItem = findDeep(
        filterMenuData,
        (o) => {
          return o?.route === parentRoute;
        },
        { childrenPath: ['children'] },
      );

      if (!isEmptyValues(parentMenuItem?.value)) {
        crumbLabels.push(parentMenuItem?.value);
      }
    });

    crumbLabels.push(currentMenuItem?.value);
  }

  return crumbLabels;
};

const getUrlNameFromMenuDataItemsByPathItem = (
  pathItem: string,
  menuData: MenuData[],
) => {
  return menuData.find((item) => item.route === pathItem);
};
