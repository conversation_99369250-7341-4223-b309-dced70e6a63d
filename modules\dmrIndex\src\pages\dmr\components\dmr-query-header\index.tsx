import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { Card, Empty, message, Modal, Spin, Select, Button } from 'antd';
import './index.less';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { history, useLocation } from 'umi';
import qs from 'qs';
import { UniTable } from '@uni/components/src';
import { dmrPendingCardIdColumns } from '@/pages/dmr/components/dmr-query-header/columns';
import { useModel } from '@@/plugin-model/useModel';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
  valueNullOrUndefinedReturnDashWithDictionaryModule,
} from '@uni/utils/src/utils';
import { SearchOutlined } from '@ant-design/icons';
import { useRequest } from 'umi';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { dmrSearchColumns } from '@/pages/dmr/columns';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
// import { UniAntdSelect as Select } from '@uni/components/src';
export interface DmrCardsSearchResp {
  Data?: any[];
  RecordsTotal?: number;
}

const preCheckContainerPosition =
  (window as any).externalConfig?.['dmr']?.['preCheckContainerPosition'] ??
  'right';

const { Option } = Select;

export const maxResultCount = 100;

interface DmrQueryHeaderProps {
  // onDmrCardChangeInterceptor?: () => boolean;
  onDmrCardChangeInterceptor?: () => Promise<boolean>;
}

export const DmrQueryHeader = (props: DmrQueryHeaderProps) => {
  const headerSelectRef = useRef(null);

  const searchKeywordRef = useRef('');

  const location = useLocation();

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState('');

  const [value, setValue] = useState(undefined);

  const [open, setOpen] = useState(false);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [dmrQueryHeaderColumns, setDmrQueryHeaderColumns] = useState([]);

  useEffect(() => {
    dmrQueryHeaderColumnsReq();

    return () => {
      let tableContent = document.querySelector(
        '#pending-his-id-search-table .ant-table-body',
      );
      tableContent?.removeEventListener('scroll', () => {});
    };
  }, []);

  useAsyncEffect(async () => {
    if (dataSource?.length === 1) {
      if (props?.onDmrCardChangeInterceptor) {
        let result = await props?.onDmrCardChangeInterceptor();
        if (result) {
          return;
        }
      }

      let value = dataSource?.at(0)?.HisId;
      setValue(value);

      if (value) {
        let dmrParam = {
          hisId: value,
        };
        history.replace(`${location?.pathname}?${qs.stringify(dmrParam)}`);
      }

      searchKeywordRef.current = '';
      setDataSource([]);
      setOffset(0);
      setRecordTotal(0);
    }
  }, [dataSource]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_HEADER_FOCUS, () => {
      headerSelectRef?.current?.focus();
    });

    return () => {
      Emitter.off(EventConstant.DMR_HEADER_FOCUS);
    };
  }, [headerSelectRef]);

  useEffect(() => {
    let tableContent = document.querySelector(
      '#pending-his-id-search-table .ant-table-body',
    );
    tableContent?.addEventListener('scroll', (event) => {
      onDmrCardPopUpScroll(event);
    });

    return () => {
      tableContent?.removeEventListener('scroll', () => {});
    };
  }, [offset, keyword]);

  const { run: dmrQueryHeaderColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/v2/Dmr/DmrSearch/PendingHisId', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setDmrQueryHeaderColumns(
            tableColumnBaseProcessor(
              dmrPendingCardIdColumns,
              response?.data?.Columns,
              'remote',
            ),
          );
        } else {
          setDmrQueryHeaderColumns(dmrPendingCardIdColumns);
        }
      },
    },
  );

  const getDmrCardsDataSource = async (offset, keyword) => {
    if (!keyword) {
      return;
    }

    if (
      offset !== 0 &&
      (dataSource.length >= recordTotal || offset >= recordTotal)
    ) {
      // 表示 全量数据
      return;
    }

    setOpen(true);
    setLoading(true);
    let data = {
      Keyword: keyword?.trim(),
      SkipCount: offset,
      MaxResultCount: maxResultCount,
      Columns: dmrQueryHeaderColumns,
    };

    let dmrCardsResponse: RespVO<DmrCardsSearchResp> = await uniCommonService(
      'Api/v2/Dmr/DmrSearch/PendingHisId',
      {
        method: 'POST',
        data: data,
      },
    );

    if (dmrCardsResponse?.code === 0) {
      if (dmrCardsResponse?.statusCode === 200) {
        let existDataSource = offset === 0 ? [] : dataSource.slice();
        if (keyword) {
          let dataSources = existDataSource.concat(
            dmrCardsResponse?.data?.Data || [],
          );
          setDataSource(dataSources);

          if (dataSources?.length === 0) {
            let title = '未找到病案';
            let content = `查询条件： ${keyword}`;
            Modal.info({
              className: 'fourofour-container',
              closable: false,
              title: title,
              content: content,
              mask: true,
              maskClosable: false,
            });
          }

          setOffset(offset + maxResultCount);

          setRecordTotal(dmrCardsResponse?.data?.RecordsTotal);
          setKeyword(keyword);
        }
      }
    }

    setLoading(false);
  };

  const { run: getDmrCardsDataSourceWithDebounce } = useDebounceFn(
    (offset, keyword) => {
      getDmrCardsDataSource(offset, keyword);
    },
    {
      wait: 500,
    },
  );

  const onTableRowClick = (record, index) => {
    if (record?.HisId) {
      let dmrParam = {
        hisId: record?.HisId,
      };
      history.replace(`${location?.pathname}?${qs.stringify(dmrParam)}`);
      setOpen(false);
    } else {
      message.error('HisId不存在');
    }
  };

  const onDmrCardPopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 300;
    console.error('nearly end', scrollNearlyEnd);
    if (scrollNearlyEnd && !loading) {
      getDmrCardsDataSourceWithDebounce(offset, keyword);
    }
  };

  return (
    <div className="dmr-query-header-container" style={{ marginLeft: 30 }}>
      <div className={'flex-row-center'}>
        {/* <label>病案号：</label> */}
        <Select
          id={'dmr-query-header-selector'}
          ref={headerSelectRef}
          suffixIcon={null}
          style={{ minWidth: 270 }}
          showSearch
          allowClear={true}
          dropdownMatchSelectWidth={false}
          virtual={false}
          open={open}
          autoFocus={true}
          onDropdownVisibleChange={(visible) => {
            if (!visible) {
              searchKeywordRef.current = '';
              setDataSource([]);
              setKeyword('');
              setOffset(0);
              setRecordTotal(0);
              setOpen(false);
            }
          }}
          placeholder={'请输入病案号/姓名/住院号/条形码'}
          getPopupContainer={(triggerNode) => triggerNode.parentElement}
          dropdownStyle={{
            width:
              dmrQueryHeaderColumns?.filter((item) => item?.visible)?.length *
              120,
          }}
          loading={loading}
          onPopupScroll={onDmrCardPopUpScroll}
          filterOption={false}
          optionLabelProp={'value'}
          onSearch={(keyword) => {
            searchKeywordRef.current = keyword;
            if (!keyword) {
              setOpen(false);
              setDataSource([]);
              setOffset(0);
              setRecordTotal(0);
              setOpen(false);
            }
          }}
          onClear={() => {
            searchKeywordRef.current = '';
          }}
          onInputKeyDown={(event) => {
            if (event.key === 'Enter' || event.keyCode === 13) {
              if ((event?.target as any)?.value) {
                if (dataSource?.length === 0) {
                  event.stopPropagation();
                  getDmrCardsDataSourceWithDebounce(
                    0,
                    (event?.target as any)?.value,
                  );
                }
              } else {
                setTimeout(() => {
                  searchKeywordRef.current = '';
                  setDataSource([]);
                  setOffset(0);
                  setRecordTotal(0);
                  setOpen(false);
                }, 0);
              }
            }
          }}
          notFoundContent={
            loading ? (
              <Spin size="small" />
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={'暂无相关病案'}
              />
            )
          }
          onSelect={async (value: string) => {
            if (props?.onDmrCardChangeInterceptor) {
              let result = await props?.onDmrCardChangeInterceptor();
              if (result) {
                return;
              }
            }

            setValue(value);
            setOpen(false);

            if (value) {
              let dmrParam = {
                hisId: value,
              };
              history.replace(
                `${location?.pathname}?${qs.stringify(dmrParam)}`,
              );
            }
          }}
          value={value}
        >
          {dataSource?.map((item) => {
            return (
              <Option
                key={item?.HisId}
                title={`${item?.HisId} ${item.PatName}`}
                value={item?.HisId}
              >
                <tr className={'row'}>
                  {dmrQueryHeaderColumns
                    ?.filter((item) => item?.visible)
                    ?.map((columnItem) => {
                      return (
                        <td className={'cell'}>
                          {columnItem?.render
                            ? columnItem.render(item)
                            : valueNullOrUndefinedReturnDashWithDictionaryModule(
                                item,
                                columnItem,
                                globalState?.dictData,
                              )}
                        </td>
                      );
                    })}
                  <td></td>
                </tr>
              </Option>
            );
          })}
        </Select>

        <Button
          style={{
            borderRadius: '0px 5px 5px 0px',
          }}
          type={'primary'}
          icon={<SearchOutlined />}
          onClick={(event) => {
            if (!isEmptyValues(searchKeywordRef?.current)) {
              if (dataSource?.length === 0) {
                event.stopPropagation();
                getDmrCardsDataSourceWithDebounce(0, searchKeywordRef?.current);
              }
            }
          }}
        />

        <div style={{ marginLeft: 10 }}>
          <TableColumnEditButton
            columnInterfaceUrl={'Api/v2/Dmr/DmrSearch/PendingHisId'}
            onTableRowSaveSuccess={(columns) => {
              console.log('Columns', columns);
              setDmrQueryHeaderColumns(
                tableColumnBaseProcessor(
                  dmrPendingCardIdColumns,
                  columns,
                  'remote',
                ),
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default DmrQueryHeader;
