import React, { useEffect, useRef, useState, useMemo } from 'react';
import './index.less';
import { UniTable, MoreAction } from '@uni/components/src';
import { useLocation, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import {
  BasePageProps,
  RespVO,
  TableCardResp,
  TableColumns,
} from '@uni/commons/src/interfaces';
import {
  Button,
  Card,
  message,
  Modal,
  TableProps,
  Row,
  Col,
  Tabs,
  Space,
  Tooltip,
  Divider,
  Dropdown,
  MenuProps,
  Typography,
  Drawer,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  dmrManagementCardColumns,
  dmrManagementDeptCardColumns,
} from '@/pages/management/columns';
import {
  DmrManagementCardTableItem,
  DmrManagementQualityListItem,
  IUdfConfigItem,
  IUdfSendedResult,
} from '@/pages/management/interfaces';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { DmrCardOperation, DmrEventConstant } from '@/utils/constants';
import {
  batchLockDmrCard,
  batchReviewDmrCard,
  batchUnLockDmrCard,
  batchResetStatusDmrCard,
  destroyDmrCard,
  lockDmrCard,
  reviewDmrCard,
  unlockDmrCard,
  resetStatusDmrCard,
  importDmrCard,
} from '@/pages/management/service';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import SearchForm from '@/components/searchForm';
import { SearchOpts } from './formItems';
import { useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import {
  codeSdateEdateProcessor,
  codeSdateEdateProcessorViaSearchedValue,
  dataTableFilterSorterHandler,
  isEmptyObj,
  toArrayMode,
} from '@/utils/utils';
import { SorterResult } from 'antd/lib/table/interface';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { PushpinOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import ProgressTaskContainer from '@uni/components/src/progress-task-container';
import SendToTaskContainer from '@uni/components/src/send-to-task';
import ExportIconBtn from '@uni/components/src/backend-export';
import { ManagementSummaries } from './constants';
import omit from 'lodash/omit';

const showSendToBtn =
  (window as any).externalConfig?.['common']?.showSendToBtn ?? false;

//  ['appeal', 'lock', 'unlock', 'sendTo', 'reset', 'import', 'destroy', 'export', 'review']
const coderManagementEnableKeys = (window as any).externalConfig?.['dmr']
  ?.coderManagementEnableKeys ?? ['reset', 'import'];

interface DmrManagementProps extends BasePageProps {}

const DmrManagement = (props: DmrManagementProps) => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  const ref = useRef<any>();

  const [tabActiveKey, setTabActiveKey] = useState('1');

  const [dmrManagementCardsColumns, setDmrManagementCardColumns] = useState([]);

  const [selectedDmrCardIds, setSelectedDmrCardIds] = useState([]); // 选择的数据的ids
  const [selectedDmrCardRecords, setSelectedDmrCardRecords] = useState([]); // 选择的数据的records {CardId, HisId}

  const [showQuality, setShowQuality] = useState(false); // 审核进度modal open

  const [dmrCardsSummary, setDmrCardsSummary] = useState<any>({});

  const [dmrCardsSummarySelectedKey, setDmrCardsSummarySelectedKey] = useState(
    'RegisterStatusAllCnt',
  );

  const [searchedValue, setSearchedValue] = useState({
    ..._.omit(searchParams, 'hospCode', 'hospCodes'),
    Sdate: dayjs().format('YYYY-MM-DD'),
    Edate: dayjs().format('YYYY-MM-DD'),
    HospCode: toArrayMode(searchParams?.hospCode ?? searchParams?.hospCodes),
    dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    Coder: userInfo?.EmployeeCode,
  });

  const [
    dmrManagementCardTableDataSource,
    setDmrManagementCardTableDataSource,
  ] = useState([]);

  useEffect(() => {
    dmrManagementCardColumnsReq();
  }, []);

  useUpdateEffect(() => {
    let pagination = {
      ...backPagination,
      current: 1,
      total: 0,
    };

    // 这里清空rowSelection
    setSelectedDmrCardIds([]);
    setSelectedDmrCardRecords([]);
    setBackPagination(pagination);
    dmrManagementCardReq(pagination?.current, pagination?.pageSize);
  }, [dmrCardsSummarySelectedKey]);

  useEffect(() => {
    // 事件监听
    Emitter.on(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, (data) => {
      onDmrManagementCardOperation(data?.record, data?.type, data?.index);
    });

    return () => {
      Emitter.off(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION);
    };
  }, [dmrManagementCardTableDataSource, selectedDmrCardIds]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const onDmrManagementCardOperation = async (
    record?: any,
    operation?: string,
    index?: number,
  ) => {
    let currentTableDataSource = [...dmrManagementCardTableDataSource];
    switch (operation) {
      case DmrCardOperation.VIEW:
        break;
      case DmrCardOperation.LOCK: {
        if (record) {
          let lockResponse: RespVO<any> = await lockDmrCard(record?.DmrCardId);
          if (lockResponse?.statusCode === 200) {
            currentTableDataSource[index]['IsLocked'] = true;
            setDmrManagementCardTableDataSource(currentTableDataSource);
          } else if (lockResponse?.statusCode === 409) {
          } else {
          }
        }
        break;
      }
      case DmrCardOperation.UNLOCK: {
        if (record) {
          let unlockResponse: RespVO<any> = await unlockDmrCard(
            record?.DmrCardId,
          );
          if (unlockResponse?.statusCode === 200) {
            currentTableDataSource[index]['IsLocked'] = false;
            setDmrManagementCardTableDataSource(currentTableDataSource);
          } else if (unlockResponse?.statusCode === 409) {
          } else {
          }
          break;
        }
      }
      case DmrCardOperation.DESTROY: {
        let destroyResponse = await destroyDmrCard([record?.DmrCardId]);
        if (
          destroyResponse?.code === 0 &&
          destroyResponse?.statusCode === 200
        ) {
          dmrManagementCardReq(backPagination.current, backPagination.pageSize);
        }
        break;
      }
      case DmrCardOperation.BATCH_LOCK:
        if (selectedDmrCardIds?.length > 0) {
          let batchLockResponse = await batchLockDmrCard(selectedDmrCardIds);
          if (
            batchLockResponse?.code === 0 &&
            batchLockResponse?.statusCode === 200
          ) {
            message.success('锁定操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('锁定出现错误');
          }
        }
        break;
      case DmrCardOperation.BATCH_UNLOCK:
        if (selectedDmrCardIds?.length > 0) {
          let batchUnLockResponse = await batchUnLockDmrCard(
            selectedDmrCardIds,
          );
          if (
            batchUnLockResponse?.code === 0 &&
            batchUnLockResponse?.statusCode === 200
          ) {
            message.error('解锁操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('解锁出现错误');
          }
        }
        break;
      case DmrCardOperation.BATCH_DESTROY:
        if (selectedDmrCardIds?.length > 0) {
          let batchLockResponse = await destroyDmrCard(selectedDmrCardIds);
          if (
            batchLockResponse?.code === 0 &&
            batchLockResponse?.statusCode === 200
          ) {
            message.success('作废操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('作废出现错误');
          }
        }
        break;
      case DmrCardOperation.BATCH_APPEAL:
        // 批量审核多一个description:最旧的一个日期 + 最新的一个日期
        if (selectedDmrCardIds?.length > 0) {
          let batchLockResponse = await batchReviewDmrCard(
            selectedDmrCardIds,
            handleTimeDescription(),
          );
          if (
            batchLockResponse?.code === 0 &&
            batchLockResponse?.statusCode === 200
          ) {
            message.success('审核操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('审核出现错误');
          }
        }
        break;
      case DmrCardOperation.BATCH_RESETSTATUS:
        // 重置
        if (selectedDmrCardIds?.length > 0) {
          let batchLockResponse = await batchResetStatusDmrCard(
            selectedDmrCardIds,
          );
          if (
            batchLockResponse?.code === 0 &&
            batchLockResponse?.statusCode === 200
          ) {
            message.success('重置操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('重置出现错误');
          }
        }
        break;
      case DmrCardOperation.DMR_IMPORT:
        // 再次导入
        if (selectedDmrCardIds?.length > 0) {
          let singleImportResponse = await importDmrCard(
            selectedDmrCardIds?.map((item) => item?.toString()),
          );
          if (
            singleImportResponse?.code === 0 &&
            singleImportResponse?.statusCode === 200
          ) {
            message.success('导入操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('导入出现错误');
          }
        }
        break;
      // 2个全量操作
      case DmrCardOperation.TOTAL_REVIEW:
        if (dmrManagementCardTableDataSource?.length > 0) {
          let totalReviewResponse = await reviewDmrCard(
            searchedValue.Sdate,
            searchedValue.Edate,
            searchedValue.HospCode,
          );
          if (
            totalReviewResponse?.code === 0 &&
            totalReviewResponse.statusCode === 200
          ) {
            message.success('全量审核操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('全量审核出现错误');
          }
        }
        break;
      case DmrCardOperation.TOTAL_RESETSTATUS:
        if (dmrManagementCardTableDataSource?.length > 0) {
          let totalResetResponse = await resetStatusDmrCard(
            searchedValue.Sdate,
            searchedValue.Edate,
            searchedValue.HospCode,
          );
          if (
            totalResetResponse?.code === 0 &&
            totalResetResponse.statusCode === 200
          ) {
            message.success('全量重置操作成功');
            dmrManagementCardReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
            ref?.current?.clearSelected();
          } else {
            message.error('全量重置出现错误');
          }
        }
        break;
      // 发送到...
      case DmrCardOperation.BATCH_SENDTO:
        if (selectedDmrCardIds?.length > 0 && record) {
          doSendToReq(
            selectedDmrCardRecords?.map((d) => d?.HisId),
            record?.Id,
          );
        }
        break;
      default:
        break;
    }
  };

  // table columns（会根据location处理columns）
  const {
    data: dmrManagementOriginalColumns,
    run: dmrManagementCardColumnsReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrDataQuery/GetCardsCurrentCoder', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          return response?.data?.Columns;
        } else {
          return [];
        }
      },
      onSuccess(data, params) {
        setDmrManagementCardColumns(
          tableColumnBaseProcessor(dmrManagementCardColumns(true), data),
        );
      },
    },
  );

  // table 数据
  const { loading: dmrManagementCardLoading, run: dmrManagementCardReq } =
    useRequest(
      (current, pageSize, otherData = {}) => {
        let data = {
          // skipFilterSorterMiddleware: true,
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
            ...otherData,
          },
          ...searchedValue,
        };

        if (!isEmptyValues(data?.ErrorLevels)) {
          if (!Array.isArray(data?.ErrorLevels)) {
            data['ErrorLevels'] = [data['ErrorLevels']];
          }
        } else {
          delete data?.['ErrorLevels'];
        }

        delete data?.['Coder'];

        // 表示用的是 登记时间 所以用的是CodeSdate CodeEdate
        codeSdateEdateProcessor(data, searchedValue?.UseRegistDate === true);

        data = dmrCardsSummarySelectedKeyHandler(data);

        return uniCommonService('Api/Dmr/DmrDataQuery/GetCardsCurrentCoder', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableCardResp<any, DmrManagementCardTableItem>>,
        ) => {
          if (response.code === 0) {
            setDmrManagementCardTableDataSource(response?.data?.data);
            setBackPagination({
              ...backPagination,
              total:
                response?.data?.recordsFiltered ||
                response?.data?.recordsTotal ||
                0,
            });
            // setSelectedDmrCardIds([]);
            setDmrCardsSummary({
              RegisterStatusAllCnt: response?.data?.RegisterStatusAllCnt,
              RegisterStatusUnUpdatedCnt:
                response?.data?.RegisterStatusUnUpdatedCnt,
              RegisterStatusRegisteredCnt:
                response?.data?.RegisterStatusRegisteredCnt,
              RegisterStatusReviewFailedCnt:
                response?.data?.RegisterStatusReviewFailedCnt,
              UnlockCnt: response?.data?.UnlockCnt,
            });
          } else {
            setDmrManagementCardTableDataSource([]);
            // setDmrCardsSummary([])
          }
        },
      },
    );

  // 选择全部
  const {
    data: dmrManagementSelectAll,
    loading: dmrManagementSelectAllLoading,
    run: dmrManagementSelectAllReq,
  } = useRequest(
    () => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: 0,
          Length: backPagination.total,
        },
        ...searchedValue,
      };

      delete data?.['Coder'];

      // 表示用的是 登记时间 所以用的是CodeSdate CodeEdate
      codeSdateEdateProcessor(data, searchedValue?.UseRegistDate === true);

      data = dmrCardsSummarySelectedKeyHandler(data);

      return uniCommonService(
        'Api/Dmr/DmrDataQuery/GetCardsCurrentCoderAllId',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (
        response: RespVO<TableCardResp<any, DmrManagementCardTableItem>>,
      ) => {
        if (response.code === 0) {
          return response?.data?.data;
        } else {
          return [];
        }
      },
    },
  );

  // handle dmrCardsSummarySelectedKey （上面4个summary切换key）
  const dmrCardsSummarySelectedKeyHandler = (data) => {
    if (dmrCardsSummarySelectedKey) {
      let summaryItem = ManagementSummaries?.find(
        (item) => item?.key === dmrCardsSummarySelectedKey,
      );
      if (summaryItem) {
        data['CustomRegisterStatus'] = summaryItem?.status;
        data['IsLocked'] = summaryItem?.isLocked ?? undefined;
      }
    }
    return data;
  };
  // 病案列表 后端分页onchange
  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    dmrManagementCardReq(
      pagi.current,
      pagi.pageSize,
      dataTableFilterSorterHandler(
        dmrManagementOriginalColumns,
        filters,
        sorter as SorterResult<any>,
      ),
    );
  };

  // 病案列表 选择框处理
  const rowSelection = {
    columnWidth: 50,
    preserveSelectedRowKeys: true,
    selectedRowKeys: selectedDmrCardIds,
    onChange: (selectedRowKeys: number[], selectedRows: any[]) => {
      setSelectedDmrCardIds(selectedRowKeys);
      setSelectedDmrCardRecords(selectedRows);
    },
    selections: [
      {
        key: 'backendSelectAll',
        text: '全选当前所有已登记',
        onSelect: async () => {
          let result: any = await dmrManagementSelectAllReq(); // {DmrCardId, HisId}[]
          if (result?.length) {
            setSelectedDmrCardIds(
              _.union(
                selectedDmrCardIds,
                result?.map((d) => d?.DmrCardId),
              ),
            );
            setSelectedDmrCardRecords(_.union(selectedDmrCardRecords, result));
          } else {
            console.error('failed');
          }
        },
      },
      {
        key: 'backendClearAll',
        text: '清空所有',
        onSelect: async () => {
          setSelectedDmrCardIds([]);
          setSelectedDmrCardRecords([]);
        },
      },
    ],
  };

  // 病案列表 操作按钮（4个）
  const onDmrCardToolBarOperationClick = (label, operation) => {
    if (selectedDmrCardIds?.length === 0) {
      message.warn('请选择病案');
      return;
    }
    Modal.confirm({
      title: `确定${label}选定病案`,
      width: 500,
      content:
        operation === DmrCardOperation.BATCH_APPEAL ? (
          <>
            {handleTimeDescription()}
            <br />
            （可点击列表右上角的 <PushpinOutlined /> 以查看进度）
          </>
        ) : (
          ''
        ), // 时间拼接
      onOk: () => {
        Emitter.emit(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, {
          type: operation,
        });
      },
    });
  };

  // 时间拼接处理
  const handleTimeDescription = () => {
    //
    if (searchedValue?.UseRegistDate) {
      // 按登记时间
      return `登记日期在 ${valueNullOrUndefinedReturnDash(
        searchedValue?.Sdate,
        'Date',
      )}~${valueNullOrUndefinedReturnDash(searchedValue?.Edate, 'Date')} 的${
        selectedDmrCardIds?.length
      }份病案`;
    } else {
      return `出院日期在 ${valueNullOrUndefinedReturnDash(
        searchedValue?.Sdate,
        'Date',
      )}~${valueNullOrUndefinedReturnDash(searchedValue?.Edate, 'Date')} 的${
        selectedDmrCardIds?.length
      }份病案`;
    }
  };

  // 病案列表 轮询列表 取消某条审核
  const {
    data: cancelQualityOne,
    loading: cancelQualityOneLoading,
    run: cancelQualityOneReq,
  } = useRequest(
    (masterId, cb = null) => {
      return uniCommonService(
        'Api/Dmr/DmrDataManagement/CancelQualityResultMaster',
        {
          method: 'POST',
          data: { masterId },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<DmrManagementQualityListItem[]>) => {
        if (response.code === 0) {
          console.log(response);
          message.success('取消成功');
          // pollingQualityListReq();
          return response?.data;
        } else {
          return [];
        }
      },
      onSuccess(data, params) {
        if (params?.at(1)) {
          params?.at(1)();
        }
      },
    },
  );

  // 获取“发送到”配置
  const { data: sendToConfigs } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrDataManagement/SendTo', {
        method: 'GET',
      });
    },
    {
      // manual: true,
      formatResult: (response: RespVO<IUdfConfigItem[]>) => {
        if (response.code === 0 && response.statusCode === 200) {
          console.log('beforesendToConfigs', response.data);
          return response.data;
        } else {
          return [];
        }
      },
    },
  );

  // 执行 “发送到”
  const { loading: doSendToLoading, run: doSendToReq } = useRequest(
    (HisIds, UdfConfigId) => {
      return uniCommonService('Api/Dmr/DmrDataManagement/SendTo', {
        method: 'POST',
        data: { HisId: HisIds, UdfConfigId },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<IUdfSendedResult>) => {
        if (response.code === 0 && response.statusCode === 200) {
          message.success('已发送到后台，开始处理...');
          return response?.data;
        } else {
          return null;
        }
      },
    },
  );

  const sendToItems: MenuProps['items'] = useMemo(
    () =>
      (sendToConfigs || [])?.map((d) => ({
        key: d?.Id,
        label: (
          <Button
            type="text"
            onClick={(e) => {
              if (selectedDmrCardIds?.length === 0) {
                message.warn('请选择病案');
                return;
              }
              Modal.confirm({
                title: (
                  <>
                    确定发送选定病案到
                    <Typography.Text type="success">{d?.Title}</Typography.Text>
                  </>
                ),
                content: '', // 时间拼接
                onOk: () => {
                  Emitter.emit(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, {
                    type: DmrCardOperation.BATCH_SENDTO,
                    record: d,
                  });
                },
              });
            }}
          >
            {d?.Title}
          </Button>
        ),
      })),
    [sendToConfigs, selectedDmrCardIds],
  );

  useEffect(() => {
    Emitter.on(EventConstant.PROGRESS_DELETE, ({ item, cb }) => {
      cancelQualityOneReq(item?.Id, cb);
    });
    return () => {
      Emitter.off(EventConstant.PROGRESS_DELETE);
    };
  }, []);

  return (
    <div className={'dmr-management-container'}>
      <Row wrap={false} gutter={8} style={{ overflow: 'hidden' }}>
        <Col flex="330px">
          <Card title="查询条件" className="dmr-management-search-card">
            <SearchForm
              classNames={['noMarginBottom']}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              searchOpts={SearchOpts(
                searchedValue,
                {
                  hospOpts: dictData.Hospital,
                  deptOpts: dictData.CliDepts,
                  wardOpts: dictData?.['Dmr']?.Wards,
                  outTypeOpts: dictData?.['Dmr']?.LYFS,
                  ylfkfsOpts: dictData?.['Dmr']?.YLFKFS,
                  coderOpts: dictData?.['Dmr']?.Coder,
                },
                true,
              )}
              onFinish={async (values) => {
                if (
                  values.UseRegistDate === 'UseRegistDate' &&
                  (!values.Sdate || !values.Edate)
                ) {
                  // do nth
                } else {
                  setSearchedValue({
                    ...values,
                    UseRegistDate:
                      values.UseRegistDate === 'UseRegistDate' ? true : false,
                  });
                  console.log(values);
                  setQiankunGlobalState({
                    dictData: dictData,
                    searchParams: {
                      ...searchParams,
                      ..._.omit(values, ['Sdate', 'Edate', 'HospCode']),
                      dateRange: [values.Sdate, values.Edate],
                      hospCodes: values?.HospCode,
                      UseRegistDate:
                        values.UseRegistDate === 'UseRegistDate' ? true : false,
                    },
                  });

                  setTimeout(() => {
                    dmrManagementCardReq(1, backPagination.pageSize);
                  }, 0);
                }
              }}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{ width: '100%', marginTop: '8px' }}
                      key="reset"
                      onClick={() => {
                        props.form?.submit?.();
                        // 点击查询的时候重置已经选择的项目
                        setSelectedDmrCardRecords([]);
                        setSelectedDmrCardIds([]);
                      }}
                    >
                      查询
                    </Button>,
                  ];
                },
              }}
            />
          </Card>
        </Col>
        <Col flex="auto">
          {/* 登记管理part TODO 等有时间了将其与查询分开 */}
          <Card className="dmr-management-tabs-container">
            <Tabs
              className="dmr-management-tabs"
              activeKey={tabActiveKey}
              onChange={(key) => setTabActiveKey(key)}
              tabBarExtraContent={{
                right: (
                  <Space>
                    {dmrCardsSummarySelectedKey ===
                      'RegisterStatusRegisteredCnt' &&
                      coderManagementEnableKeys?.includes('appeal') && (
                        <Button
                          key="batch_appeal"
                          disabled={selectedDmrCardIds?.length === 0}
                          loading={false}
                          onClick={(e) => {
                            onDmrCardToolBarOperationClick(
                              '审核',
                              DmrCardOperation.BATCH_APPEAL,
                            );
                          }}
                        >
                          审核
                        </Button>
                      )}
                    {coderManagementEnableKeys?.includes('lock') && (
                      <Button
                        key="batch_lock"
                        disabled={selectedDmrCardIds?.length === 0}
                        loading={false}
                        onClick={(e) => {
                          onDmrCardToolBarOperationClick(
                            '锁定',
                            DmrCardOperation.BATCH_LOCK,
                          );
                        }}
                      >
                        锁定
                      </Button>
                    )}
                    {coderManagementEnableKeys?.includes('unlock') && (
                      <Button
                        key="batch_unlock"
                        disabled={selectedDmrCardIds?.length === 0}
                        loading={false}
                        onClick={(e) => {
                          onDmrCardToolBarOperationClick(
                            '解锁',
                            DmrCardOperation.BATCH_UNLOCK,
                          );
                        }}
                      >
                        解锁
                      </Button>
                    )}
                    {coderManagementEnableKeys?.includes('sendTo') && (
                      <Dropdown
                        key="batch_sendTo"
                        disabled={selectedDmrCardIds?.length === 0}
                        menu={{ items: sendToItems }}
                      >
                        <Button loading={doSendToLoading}>发送到</Button>
                      </Dropdown>
                    )}
                    {coderManagementEnableKeys?.includes('reset') && (
                      <Button
                        key="batch_reset"
                        disabled={selectedDmrCardIds?.length === 0}
                        loading={false}
                        onClick={(e) => {
                          onDmrCardToolBarOperationClick(
                            '重置',
                            DmrCardOperation.BATCH_RESETSTATUS,
                          );
                        }}
                      >
                        重置
                      </Button>
                    )}

                    {coderManagementEnableKeys?.includes('import') && (
                      <Button
                        key="dmr_import"
                        disabled={selectedDmrCardIds?.length === 0}
                        loading={false}
                        onClick={(e) => {
                          onDmrCardToolBarOperationClick(
                            '导入',
                            DmrCardOperation.DMR_IMPORT,
                          );
                        }}
                      >
                        导入
                      </Button>
                    )}

                    {coderManagementEnableKeys?.includes('destroy') && (
                      <Button
                        key="batch_destroy"
                        disabled={selectedDmrCardIds?.length === 0}
                        loading={false}
                        onClick={(e) => {
                          onDmrCardToolBarOperationClick(
                            '作废',
                            DmrCardOperation.BATCH_DESTROY,
                          );
                        }}
                      >
                        作废
                      </Button>
                    )}

                    {(coderManagementEnableKeys?.includes('export') ||
                      coderManagementEnableKeys?.includes('review')) && (
                      <Divider type="vertical" />
                    )}
                    {coderManagementEnableKeys?.includes('export') && (
                      <ExportIconBtn
                        isBackend={true}
                        backendObj={{
                          url: 'Api/Dmr/DmrDataQuery/ExportGetCardsCurrentCoder',
                          method: 'POST',
                          data: dmrCardsSummarySelectedKeyHandler({
                            skipFilterSorterMiddleware: true,
                            ...omit(searchedValue, ['Coder']),
                          }),
                          fileName: '病案列表',
                        }}
                        btnDisabled={
                          dmrManagementCardTableDataSource?.length < 1
                        }
                      />
                    )}
                    {coderManagementEnableKeys?.includes('review') && (
                      <Tooltip title={showQuality ? '关闭进度' : '查看进度'}>
                        <Button
                          type="text"
                          shape="circle"
                          icon={<PushpinOutlined />}
                          onClick={(e) => {
                            setShowQuality(!showQuality);
                          }}
                        />
                      </Tooltip>
                    )}
                  </Space>
                ),
              }}
              items={[
                {
                  label: (
                    <Space>
                      <span>病案列表</span>
                    </Space>
                  ),
                  key: '1',
                  children: (
                    <>
                      <div className={'dmr-management-summary-container'}>
                        <Row gutter={[16, 16]}>
                          {ManagementSummaries?.map((item) => {
                            return (
                              // <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                              <DmrManagementSummaryItem
                                className={
                                  item?.key === dmrCardsSummarySelectedKey
                                    ? 'card-selected'
                                    : ''
                                }
                                label={item?.label}
                                itemKey={item?.key}
                                count={dmrCardsSummary?.[item?.key] ?? 0}
                                onClick={() => {
                                  setDmrCardsSummarySelectedKey(item?.key);
                                }}
                              />
                              // </Col>
                            );
                          })}
                        </Row>
                      </div>

                      <UniTable
                        actionRef={ref}
                        id={'dmr-management-card-table-manage'}
                        rowKey={'DmrCardId'}
                        rowSelection={{
                          type: 'checkbox',
                          ...rowSelection,
                        }}
                        dictionaryData={dictData}
                        scroll={{ x: 'max-content', y: '580px' }}
                        loading={
                          dmrManagementCardLoading ||
                          dmrManagementSelectAllLoading
                        }
                        columns={dmrManagementCardsColumns}
                        dataSource={dmrManagementCardTableDataSource}
                        pagination={backPagination}
                        onChange={backTableOnChange}
                        toolBarRender={null}
                        widthCalculate={true}
                        widthDetectAfterDictionary={true}
                        isBackPagination
                      />
                    </>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
        <Drawer
          width={'450px'}
          className="progress_task_container_drawer"
          open={showQuality}
          onClose={() => {
            setShowQuality(!showQuality);
          }}
          destroyOnClose
          title="进程进度"
        >
          <Tabs
            items={[
              {
                label: '24小时内审核进程进度',
                key: 'item-1',
                children: (
                  <ProgressTaskContainer
                    dataApiObj={{
                      url: 'Api/Dmr/DmrDataManagement/GetRecentQualityResultMasters',
                      method: 'POST',
                    }}
                    isBackend={false}
                    taskType="dmrManagement"
                  />
                ),
              },
              {
                label: '"发送到"进程进度',
                key: 'item-2',
                children: (
                  <SendToTaskContainer
                    triggerApiUrl={'Api/Dmr/DmrDataManagement/SendTo'}
                    taskApiUrl={'Api/Dmr/DmrDataManagement/GetSendToRecord'}
                  />
                ),
              },
            ]}
          />
        </Drawer>
      </Row>
    </div>
  );
};

interface DmrManagementSummaryItemProps {
  className?: string;
  label?: string;
  count?: number;
  itemKey?: string;
  onClick?: () => any;
}

export const DmrManagementSummaryItem = (
  props: DmrManagementSummaryItemProps,
) => {
  return (
    <div
      className={`dmr-management-summary-item-container ${props?.className}`}
      onClick={() => {
        props?.onClick && props?.onClick();
      }}
    >
      <span className={'label'}>{props?.label}：</span>
      <span className={'value'}>
        {props?.count === null || props?.count === undefined
          ? '-'
          : props?.count}
      </span>
    </div>
  );
};

export default DmrManagement;
