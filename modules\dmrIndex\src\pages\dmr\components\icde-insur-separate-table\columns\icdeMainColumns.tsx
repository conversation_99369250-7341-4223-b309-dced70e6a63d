import { PlusCircleTwoTone } from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import {
  IcdeOperationInputSelector,
  IcdeFieldInput,
  IcdeOperationReadonlyItem,
  IcdeExtraTagsItem,
  icdeExtraMap,
  IcdeOperCheckbox,
} from '@uni/grid/src/components/icde-oper-input/input';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import IconBtn from '@uni/components/src/iconBtn';
import { extraTitle, DragHandler, nonAddCell } from '../../../columns';
import { noNeedReadOnlyColumns, readOnlyTextCenterColumns } from './constants';
import _ from 'lodash';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;

const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;

const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

// 主表格专用的列定义（过滤掉医保相关列）
export const icdeMainColumnsBase = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(
      _.omitBy(icdeExtraMap, (item) => item.isInsur === true),
      {
        InsurIsObsolete: {
          prompt: '医保置灰',
        },
      },
    ),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisMainTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }

      return (
        <IcdeExtraTagsItem
          value={record?.['IcdeExtra']}
          nameKey={'IcdeExtra'}
          extraMap={_.omitBy(icdeExtraMap, (item) => item.isInsur === true)}
        />
      );
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  {
    key: 'rowSelection',
    dataIndex: 'RowSelection',
    title: (
      <RowSelectionHeader
        tableId="diagnosisMainTable"
        onSelectAll={(checked) => {
          console.log('全选/反选:', checked);
        }}
      />
    ),
    visible: icdeOperRowSelection || false,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: false,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return (
        <RowSelectionCheckbox
          id={`formItem#RowSelection#${index}#IcdeMainTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            console.log('asddsadadasdacheckbox');
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisMainTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }

      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>;
      }
      if (record?.id !== 'ADD') {
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '出院诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'diagnosisMainTable'}
          componentId={`MainIcdeCode#${index}`}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG
              ? {
                  points: ['tl', 'bl'], // 下拉对齐到输入框的左上角 (tl) → 下拉的左下角 (bl)
                  offset: [-65, 4], // y 方向下移 4px，避免顶到输入框
                  overflow: {
                    adjustY: false, // 纵向溢出时自动反向
                  },
                }
              : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '出院诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'diagnosisMainTable'}
          componentId={`MainIcdeName#${index}`}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG
              ? {
                  points: ['tl', 'bl'], // 下拉对齐到输入框的左上角 (tl) → 下拉的左下角 (bl)
                  offset: [-65, 4], // y 方向下移 4px，避免顶到输入框
                  overflow: {
                    adjustY: false, // 纵向溢出时自动反向
                  },
                }
              : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisMainTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '诊断描述',
    visible: true,
    width: 160,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'IcdeNote'}
          index={index}
          tableId={'diagnosisMainTable'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '治疗情况',
    visible: true,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IcdeOutcome',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisMainTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  // {
  //   key: 'IsMain',
  //   dataIndex: 'IsMain',
  //   title: '医保主诊',
  //   visible: true,
  //   align: 'center',
  //   width: 50,
  //   renderColumnFormItem: (node, record, index, dataIndex) => {
  //     if (record?.id === 'ADD') {
  //       return null;
  //     }

  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsMain#${index}#IcdeMainTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_MAIN_INSURE_MAIN, {
  //             id: record?.id,
  //             values: {
  //               IsMain: checked,
  //             },
  //             index: index,
  //           });
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  // {
  //   key: 'IsReported',
  //   dataIndex: 'IsReported',
  //   title: '上报',
  //   visible: true,
  //   align: 'center',
  //   width: 60,
  //   readonly: false,
  //   renderColumnFormItem: (node, record, index, dataIndex, form) => {
  //     if (record?.id === 'ADD') {
  //       return null;
  //     }

  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsMain#${index}#IcdeMainTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         dependencyKey={'IsMain'}
  //         dependencyValue={true}
  //         form={form}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_MAIN_REPORT, checked);
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    dataIndex: 'InsurCode',
    visible: false,
    readonly: true,
    width: 0,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    visible: false,
    readonly: true,
    width: 0,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsCode',
    title: '国家临床版编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsName',
    title: '国家临床版名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsInfects',
    title: '院感',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IsInfects',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisMainTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IsInfects'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IsInfects'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsOperComplication',
    title: '术后并发症',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'IsOperComplication',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisMainTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IsOperComplication'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IsOperComplication'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'DiagAccord',
    title: '符合标识',
    visible: false,
    align: 'center',
    width: 90,
    conditionDictionaryKey: 'DiagAccord',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisMainTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'DiagAccord'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'DiagAccord'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="diagnosisMainTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_MAIN_COPY, {
                id: record?.['id'],
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_MAIN_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const getIcdeMainColumns = (insurSeparateTableLogic?: {
  canEditMainTable?: boolean;
}) => {
  const canEditMainTable = insurSeparateTableLogic?.canEditMainTable ?? true;

  const processed = (icdeMainColumnsBase as any[])
    .map((col) => {
      if (!canEditMainTable) {
        if (
          noNeedReadOnlyColumns?.findIndex((d) => d === col?.dataIndex) === -1
        ) {
          const base: any = { ...col, readonly: true };
          return {
            ...base,
            renderColumnFormItem: (
              node: any,
              record: any,
              index: number,
              dataIndex: string,
              form: any,
              extraItem: any,
            ) => (
              <IcdeOperationReadonlyItem
                conditionDictionaryKey={base?.conditionDictionaryKey}
                conditionDictionaryGroup={base?.conditionDictionaryGroup}
                extraItem={extraItem}
                style={
                  readOnlyTextCenterColumns?.findIndex(
                    (d) => d === col?.dataIndex,
                  ) !== -1
                    ? { justifyContent: 'center' }
                    : {}
                }
              />
            ),
          };
        }
      }
      return col;
    })
    .filter((c) => !(c?.dataIndex === 'operation' && !canEditMainTable));

  // 当无编辑权限时，禁用“序”列的拖拽手柄，仅显示标签
  const updated = processed.map((col: any) => {
    if (col?.key === 'sort' || col?.dataIndex === 'IcdeSort') {
      if (!canEditMainTable) {
        return {
          ...col,
          render: (_node: any, record: any, index: number) => {
            if (record?.id === 'ADD') {
              return null;
            }
            const labelNode =
              index === 0 ? (
                <span>主</span>
              ) : (
                <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>
              );
            // 无权限时不包裹 DragHandler，避免可拖拽
            return labelNode;
          },
        };
      }
    }
    return col;
  });

  return updated;
};
