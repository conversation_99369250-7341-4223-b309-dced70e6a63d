import React, { useEffect, useRef, useState } from 'react';
import { Dropdown, Input, Select, Spin, Form, Tooltip } from 'antd';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  IcdeOperItem,
  IcdeOperResp,
  OperComboItem,
  OperComboResp,
} from '@/pages/dmr/network/interfaces';
import ex from 'umi/dist';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  getSelectorDropdownContainerNode,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
} from '@uni/grid/src/utils';
import { UniAntdSelect } from '@uni/components/src';
import { operationExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import { operTypeToClassName } from '@/pages/dmr/components/oper-table';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  FormTableItemBaseProps,
  useDmrDragEditOnlyTableContext,
} from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import cloneDeep from 'lodash/cloneDeep';
import { useDebounceFn } from 'ahooks';
import { IcdeOperKeyboardFriendlyDropdown } from '@uni/grid/src/components/dmr-select/keyboard';
import isEqual from 'lodash/isEqual';

const { Option } = Select;

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['dmr']?.inputFocusNotSelectAll ?? false;

const leftRightSwitchPage =
  (window as any).externalConfig?.['dmr']?.leftRightSwitchPage ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

const enableKeyboardFriendlySelect =
  (window as any).externalConfig?.['dmr']?.enableKeyboardFriendlySelect ??
  false;

const operationComboInput =
  (window as any).externalConfig?.['dmr']?.operationComboInput ?? false;

export interface OperationFormKeysItem {
  [key: string]: string;
}

export interface OperationComboProps {
  columnType: 'Dmr' | 'Insur';
}

export interface OperationSelectProps
  extends FormTableItemBaseProps,
    OperationComboProps {
  form?: any;

  recordId: string;

  dataIndex: string;

  parentId?: string;

  formKeys?: OperationFormKeysItem; // 暂定

  componentId: string;

  onOperationSelect?: (data) => void;

  getPopupContainer?: (trigger) => HTMLElement;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  interfaceUrl?: string;
  disabled?: boolean;

  // 为了兼容 置灰的手术 / 诊断用于立即搜索
  instantSelect?: boolean;

  rowDataKey?: string;

  dropdownAlign?: any;

  onChangeValueProcessor?: (value: any) => string;

  tableId?: string;

  numberSelectItem?: boolean;

  codeColumnWidth?: number | string;
}

export const removeOperationColorClassName = (rowItem: any) => {
  let waitForDeleteClassName = '';
  for (let classItem of rowItem?.classList) {
    if (classItem.startsWith('operation-tr-color')) {
      waitForDeleteClassName = classItem;
      break;
    }
  }
  if (waitForDeleteClassName) {
    rowItem?.classList?.remove(waitForDeleteClassName);
  }
};

const maxResultCount = 100;
const OperationSelect = (props: OperationSelectProps) => {
  const operInputRef = useRef(null);

  const dmrTableContext = useDmrDragEditOnlyTableContext();

  const selectorContainerRef =
    dmrTableContext?.columnItemRefMapGetter?.(
      `${props?.dataIndex}~${props?.tableId}`,
    ) ?? React.createRef<any>();
  dmrTableContext?.columnItemRefMapSetter?.(
    `${props?.dataIndex}~${props?.tableId}`,
    selectorContainerRef,
  );

  const [dataSource, setDataSource] = useState<IcdeOperItem[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState(undefined);

  const [hasSearched, setHasSearched] = useState(false);

  const operationCodeValue = Form.useWatch([props?.recordId, props?.dataIndex]);

  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  //  keyboard friendly
  const [optionOpen, setOptionOpen] = useState(false);
  const listRef = React.useRef(null);

  console.log('operationCodeValue', operationCodeValue);

  const getOperationDataSource = async (searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    setLoading(true);
    let data = {
      Keyword: searchKeyword?.trim(),
      HasInsurCompare: true,
      HasHqmsCompare: true,
      HasDrgsCompare: true,
    };

    let operationDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(props?.interfaceUrl ?? 'Api/Dmr/DmrSearch/Oper', {
        params: data,
      });

    // 手术组套 operationComboInput: true
    let operComboData = [];
    if (operationComboInput === true && props?.columnType === 'Dmr') {
      let operationComboDataSourceResponse: RespVO<OperComboResp> =
        await uniCommonService('Api/Dmr/DmrSearch/SearchOperCombo', {
          params: data,
        });

      operComboData = (
        operationComboDataSourceResponse?.data?.Data ?? []
      )?.filter((item) => !isEmptyValues(item?.RelatedOperCodes));
    }

    if (operationDataSourceResponse?.code === 0) {
      if (operationDataSourceResponse?.statusCode === 200) {
        let operationData = [];

        // 先行concat OperCombo 的数据
        if (!isEmptyValues(operComboData)) {
          operationData = operationData.concat(
            operComboData?.map((item) => {
              return {
                ...item,
                type: 'OperationCombo',
                key: item?.Code,
                value: item?.Code,
                label: `${item?.Code} ${item?.Name} ${
                  !isEmptyValues(item?.Remark) ? `备注：${item?.Remark}` : ''
                }`,
                OperCode: item?.Code,
                OperName: item?.Name,
              };
            }),
          );
        }

        setDataSource(
          operationData?.concat(
            operationDataSourceResponse?.data?.Data?.map((item) => {
              return {
                ...item,
                key: item?.Code,
                value: item?.Code,
                label: `${item?.Code} ${item?.Name}`,
                OperCode: item?.Code,
                OperName: item?.Name,
              };
            }) || [],
          ),
        );
      }
    }

    setLoading(false);
  };

  const { run: getOperationDataSourceWithDebounce } = useDebounceFn(
    (keyword) => {
      getOperationDataSource(keyword);
    },
    {
      wait: 200,
    },
  );

  const onOperationPopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 100;
    if (scrollNearlyEnd && !loading) {
      getOperationDataSource(keyword);
    }
  };

  const onOperSelectClear = () => {
    let fieldsValue = {};
    if (props?.formKeys) {
      // 第一个是文本 第二个是编码
      Object.keys(props?.formKeys).forEach((key) => {
        fieldsValue[key] = '';
      });
    }
    if (props?.form) {
      if (props?.recordId) {
        let recordFieldValue = cloneDeep(fieldsValue);
        Object.keys(recordFieldValue)?.forEach((key) => {
          props?.form.setFieldValue(
            [props?.recordId, key],
            recordFieldValue[key],
          );
        });
      } else {
        props?.form.setFieldsValue(fieldsValue);
      }
    }

    if (props?.onChangeValueProcessor) {
      fieldsValue = props?.onChangeValueProcessor(fieldsValue);
    }

    props?.onChange && props?.onChange(fieldsValue);

    // 清一下dataSource  keyword offset total
    setDataSource([]);
    setHasSearched(false);
    setKeyword(undefined);
    setOffset(0);
    setRecordTotal(0);

    setOptionOpen(false);

    listRef?.current?.clear();

    props?.onOperationSelect && props?.onOperationSelect(fieldsValue);
  };

  const onOperComboSelect = async (comboItem: any) => {
    setDataSource([]);
    setErrorTooltipOpen(false);
    setHasSearched(false);
    setOffset(0);
    setRecordTotal(0);
    setKeyword(undefined);

    setTimeout(() => {
      operInputRef?.current?.focus();
    }, 0);

    let operRelatedCodesInfoResp: RespVO<IcdeOperResp> = await uniCommonService(
      'Api/Dmr/DmrSearch/OperReorder',
      {
        params: {
          StrictMode: true,
          OperCodes: comboItem?.RelatedOperCodes,
          Keyword: 'OperationCombo',
        },
      },
    );
    console.log('operRelatedCodesInfo', operRelatedCodesInfoResp);
    if (!isEmptyValues(operRelatedCodesInfoResp?.data?.Data)) {
      let waitForAddToTableData = [];
      operRelatedCodesInfoResp?.data?.Data?.forEach((item) => {
        waitForAddToTableData.push(constructFieldValue(item));
      });

      // 按照 comboItem?.RelatedOperCodes 排序
      waitForAddToTableData = waitForAddToTableData.sort(
        (a, b) =>
          comboItem?.RelatedOperCodes.indexOf(a?.OperCode) -
          comboItem?.RelatedOperCodes.indexOf(b?.OperCode),
      );

      dmrTableContext?.dmrTableContainerRef?.current?.onOperationComboItemSelect(
        comboItem,
        waitForAddToTableData,
        props?.recordId,
      );
    }
  };

  // TODO 当这个页面显示的时候会出问题的  select option 是没有的  需要在做定制

  const constructFieldValue = (currentItem: any) => {
    let fieldsValue = {};
    if (props?.formKeys) {
      // 第一个是文本 第二个是编码
      Object.keys(props?.formKeys).forEach((key) => {
        // 微创手术这个单独做处理
        if (key === 'OperExtra') {
          fieldsValue[key] = Object.keys(operationExtraMap)?.filter((key) => {
            if (key === 'InsurIsObsolete') {
              return currentItem?.['IsObsolete'] ?? false;
            } else {
              return currentItem?.[key] ?? false;
            }
          });
        } else if (key === 'RowClassName') {
          if (currentItem?.OperType) {
            let operTypeClassName =
              operTypeToClassName?.[currentItem?.OperType];
            if (operTypeClassName) {
              fieldsValue[key] = operTypeClassName;

              if (props?.rowDataKey) {
                let rowItem = document.querySelector(
                  `div#${props?.parentId} tr[data-row-key='${props?.rowDataKey}']`,
                );
                if (rowItem) {
                  removeOperationColorClassName(rowItem);
                  rowItem?.classList?.add(operTypeClassName);
                }
              }
            } else {
              if (props?.rowDataKey) {
                let rowItem = document.querySelector(
                  `tr[data-row-key='${props?.rowDataKey}']`,
                );
                if (rowItem) {
                  removeOperationColorClassName(rowItem);
                }
              }
            }
          }
        } else {
          fieldsValue[key] = currentItem?.[props?.formKeys[key]];
        }
      });
    }

    return fieldsValue;
  };

  const onOperSelect = (value: string, externalDataSource?: any[]) => {
    let fieldsValue = {};
    let currentSelected = (externalDataSource ?? dataSource)?.find(
      (item) => item.Code === value,
    );
    if (currentSelected) {
      fieldsValue = constructFieldValue(currentSelected);
      let recordFieldValue = cloneDeep(fieldsValue);
      if (props?.form) {
        if (props?.recordId) {
          Object.keys(recordFieldValue)?.forEach((key) => {
            props?.form.setFieldValue(
              [props?.recordId, key],
              recordFieldValue[key],
            );
          });
        } else {
          props?.form.setFieldsValue(fieldsValue);
        }
      }

      if (props?.onChangeValueProcessor) {
        fieldsValue = props?.onChangeValueProcessor(fieldsValue);
      }

      if (!isEmptyValues(props?.tableId)) {
        // 相同的时候还是保留标记
        // 保证 所有 code 相同才能return
        let currentCodes = {
          Code: props?.value,
          InsurCode: recordFieldValue?.InsurCode,
          HqmsCode: recordFieldValue?.HqmsCode,
          WtCode: recordFieldValue?.WtCode,
        };
        let newCodes = {
          Code: value,
          InsurCode: currentSelected?.InsurCode,
          HqmsCode: currentSelected?.HqmsCode,
          WtCode: currentSelected?.WtCode,
        };
        if (isEqual(currentCodes, newCodes)) {
          return;
        }
        let tableItem = document.getElementById(props?.tableId);
        if (!isEmptyValues(tableItem)) {
          tableItem
            ?.querySelectorAll('.table-duplicate-highlight-item')
            ?.forEach((item) => {
              item?.classList.remove('table-duplicate-highlight-item');
            });
        }
      }

      props?.onChange && props?.onChange(fieldsValue);

      setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
      setOffset(0);
      setRecordTotal(0);
      setKeyword(undefined);

      setTimeout(() => {
        operInputRef?.current?.focus();
      }, 0);
    }
  };

  useEffect(() => {
    requestAnimationFrame(() => {
      let dropDownElement = document.getElementById(
        'oper-select-dropdown-container',
      );
      if (dropDownElement) {
        let firstLi = dropDownElement?.querySelector('ul li');
        if (firstLi) {
          (firstLi as any)?.focus();
          setTimeout(() => {
            (firstLi as any)?.scrollIntoView({
              block: 'center',
              inline: 'center',
            });
          }, 100);
        }
      }
    });
  }, [dataSource]);

  const operKeyboardFriendlyProps = props?.numberSelectItem
    ? {
        dropdownRender: (menu: any) => {
          return (
            <IcdeOperKeyboardFriendlyDropdown
              type={'Oper'}
              columnType={props?.columnType}
              enableKeyboardFriendlySelect={enableKeyboardFriendlySelect}
              listRef={listRef}
              interfaceUrl={
                props?.interfaceUrl ?? 'Api/Dmr/DmrSearch/OperReorder'
              }
              value={props?.value}
              onSelectChange={async (value, item, dataSources) => {
                if (item?.type === 'OperationCombo') {
                  // 手术组套
                  // TODO
                  // 通过RelatedOperCodes换 信息 然后 新增 data
                  onOperComboSelect(item);
                } else {
                  onOperSelect(value, dataSources);
                }
              }}
              optionOpen={optionOpen}
              setOptionOpen={setOptionOpen}
              searchKeyword={keyword}
              setSearchKeyword={setKeyword}
              instantSelect={props?.instantSelect}
              codeColumnWidth={props?.codeColumnWidth}
              leftRightSwitchPage={leftRightSwitchPage ?? false}
            />
          );
        },
        open: optionOpen && !isEmptyValues(keyword),
        onDropdownVisibleChange: (visible: boolean) => setOptionOpen(visible),
        onKeyboardNumberSelect: (event: any, index: any) => {
          listRef?.current?.onKeyboardNumberSelect(event, index);
        },
        onKeyboardPageFlip: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
        onPopupScroll: () => {},
        leftRightSwitchPage: leftRightSwitchPage ?? false,
        onLeftRightKeyDownSwitchPage: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
      }
    : {};

  return (
    <div className={'form-content-item-container'}>
      <Tooltip
        open={errorTooltipOpen}
        color={'rgba(235, 87, 87, 0.85)'}
        title={'手术不存在，请检查后重新选择'}
      >
        <UniAntdSelect
          ref={selectorContainerRef}
          showArrow={false}
          id={`formItem#${props?.componentId}#OperSelect`}
          className={`select operation-item-container`}
          value={props?.value}
          showSearch
          showAction={props?.instantSelect ? ['focus'] : []}
          allowClear={false}
          disabled={props?.disabled}
          getPopupContainer={(trigger) =>
            (props.getPopupContainer && props?.getPopupContainer(trigger)) ||
            getSelectorDropdownContainerNode()
          }
          onInputKeyDown={(event) => {
            if (hasSearched) {
              if (event.key === 'Enter' && dataSource?.length === 0) {
                setErrorTooltipOpen(true);
                event.preventDefault();
                event.stopPropagation();
              }
            }
          }}
          enterSwitch={true}
          contentEditable={!inputFocusNotSelectAll}
          dropdownStyle={props?.dropdownStyle || {}}
          listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
          placeholder={'请选择手术'}
          onSearch={(searchKeyword) => {
            if (enableKeyboardFriendlySelect === true) {
              setKeyword(searchKeyword);
              return;
            }

            if (props?.numberSelectItem !== true) {
              setHasSearched(true);
              if (searchKeyword) {
                getOperationDataSourceWithDebounce(searchKeyword);
              } else {
                setDataSource([]);
                setOffset(0);
                setRecordTotal(0);
              }
            }
            setKeyword(searchKeyword);
          }}
          filterOption={false}
          optionLabelProp={'value'}
          dropdownMatchSelectWidth={false}
          dropdownAlign={props?.dropdownAlign}
          notFoundContent={loading ? <Spin size="small" /> : null}
          onFocus={() => {
            if (props?.instantSelect) {
              if (!isEmptyValues(props?.value)) {
                setKeyword(props?.value);
                if (props?.numberSelectItem !== true) {
                  getOperationDataSource(props?.value);
                }
              }
            }

            listRef?.current?.onFocus(null, props?.value);
          }}
          onBlur={(event) => {
            if (props?.numberSelectItem !== true) {
              setTimeout(() => {
                setDataSource([]);
                setHasSearched(false);
                setErrorTooltipOpen(false);
                setKeyword('');
                setOffset(0);
                setRecordTotal(0);
              }, 0);
            }
          }}
          onClear={() => {
            onOperSelectClear();
          }}
          onKeyDown={(event) => {
            console.log('onKeyDown', event?.key);

            // 当且仅当
            if (props?.numberSelectItem === true) {
              listRef?.current?.onKeyDown(event);
            }

            if (
              props?.tableId &&
              (event as any)?.hosted !== true &&
              event?.ctrlKey === false
            ) {
              if (event?.key === 'ArrowUp') {
                Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                  event: event,
                  type: 'UP',
                  trigger: 'selectkeydown',
                });
              }

              if (event?.key === 'ArrowDown') {
                Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                  event: event,
                  type: 'DOWN',
                  trigger: 'selectkeydown',
                });
              }
            }
          }}
          onSelect={(value, option) => {
            if (option?.type === 'OperationCombo') {
              onOperComboSelect(option);
            } else {
              onOperSelect(value);
            }
          }}
          options={
            props?.numberSelectItem
              ? [
                  {
                    Code: 'Default',
                    Name: 'Default',
                  },
                ]
              : dataSource
          }
          dumbOnComposition={true}
          mousedownOptionOpen={false}
          doubleClickPopUp={false}
          numberSelectItem={props?.numberSelectItem}
          // keyboard friendly
          {...operKeyboardFriendlyProps}
        />
      </Tooltip>
    </div>
  );
};

export default OperationSelect;
