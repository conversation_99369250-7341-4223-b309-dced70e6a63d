import { Affix, Steps } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { DmrConfigurationConstants } from '@/pages/configuration/constants';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { isEmptyValues } from '@uni/utils/src/utils';
import { getDmrSavedMenu } from '@/pages/dmr/network/get';
import {
  defaultPageUpDownHandler,
  waitFocusElementRefocus,
} from '@uni/grid/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import {
  scrollIntoViewWaitingToEnd,
  scrollToPositionWaitingToEnd,
} from '@/pages/dmr/components/right-menu/utils';

const menuViewTop =
  (window as any).externalConfig?.['dmr']?.menuViewTop ?? false;

const menuScrollTopPadding =
  (window as any).externalConfig?.['dmr']?.menuScrollTopPadding ?? 40;

interface RightMenuProps {
  // topMenus: TopMenuItem[];
  defaultTopMenuKeys: TopMenuItem[];

  initialLayout: any[];

  inDmrRegister?: boolean;

  isFullscreen?: boolean;

  containerRef?: any;
}

export interface TopMenuItem {
  id?: string;
  title?: string;
  key?: string;

  focusId?: string;

  top?: number;

  bottom?: number;

  style?: React.CSSProperties;

  className?: string;
}

const RightMenu = (props: RightMenuProps) => {
  // menu current
  const [menuCurrent, setMenuCurrent] = useState(0);

  const [topMenus, setTopMenus] = useState<TopMenuItem[]>([]);

  const [topMenuKeys, setTopMenuKeys] = useState<TopMenuItem[]>([]);

  const [layouts, setLayouts] = useState<any>(props?.initialLayout);

  if (props?.containerRef) {
    React.useImperativeHandle(props?.containerRef, () => {
      return {
        onLeftMenuClickOffset: (offset: number) => {
          let newIndex = menuCurrent + offset;
          if (newIndex > -1 && newIndex < topMenus?.length) {
            onMenuChange(newIndex);
          } else {
            // 不合法的话调用原有的page up down
            defaultPageUpDownHandler(offset < 0);
          }
        },
      };
    });
  }

  useEffect(() => {
    // 获取保存的menu
    if (props?.inDmrRegister === true) {
      dmrSavedMenuReq();
    }
  }, []);

  const { run: dmrSavedMenuReq } = useRequest(
    () => {
      return getDmrSavedMenu();
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (!isEmptyValues(response?.data?.DmrLeftMenus?.DmrLeftMenus)) {
            setTopMenuKeys(response?.data?.DmrLeftMenus?.DmrLeftMenus);
          }
        }
      },
    },
  );

  useEffect(() => {
    // 当且仅当 不存在layouts 以及存在initial layout的时候才做
    if (props?.initialLayout && isEmptyValues(layouts)) {
      setLayouts(props?.initialLayout);
    }
  }, [props?.initialLayout]);

  useEffect(() => {
    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_MENU_EDIT_COMPLETE,
      (topMenuKeys) => {
        setTopMenuKeys(topMenuKeys);
      },
    );

    return () => {
      Emitter.off(
        DmrConfigurationConstants.DMR_CONFIGURATION_MENU_EDIT_COMPLETE,
      );
    };
  }, [topMenus]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_TABLE_LAYOUT_CHANGE_MENU, (layouts) => {
      // setReRenderValue((reRenderValue) => reRenderValue + 1);
      setLayouts(layouts);
    });

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_CHANGE_MENU);
    };
  }, [topMenuKeys]);

  useEffect(() => {
    setTopMenuKeys(props?.defaultTopMenuKeys);
  }, [props?.defaultTopMenuKeys]);

  useEffect(() => {
    generateTopMenus(layouts);

    Emitter.on(EventConstant.DMR_MENU_POSITION_RECALCULATE, () => {
      generateTopMenus(layouts);
    });

    return () => {
      Emitter.off(EventConstant.DMR_MENU_POSITION_RECALCULATE);
    };
  }, [topMenuKeys, layouts]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_LEFT_MENU_CLICK, (current) => {
      if (current > -1 && current < topMenus?.length) {
        onMenuChange(current);
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_LEFT_MENU_CLICK);
    };
  }, [topMenus]);

  const generateTopMenus = (layouts) => {
    let topMenus: TopMenuItem[] = [];
    topMenuKeys.forEach((item) => {
      let currentRowLayouts = layouts?.find(
        (layoutItem) => item.key === layoutItem?.i,
      );

      if (currentRowLayouts) {
        let currentRowItemElement = document.getElementById(item.key);

        if (currentRowItemElement) {
          let transformHeight = currentRowItemElement?.offsetTop;
          let itemBottom =
            currentRowItemElement?.offsetTop +
            currentRowItemElement?.offsetHeight;

          console.log('currentRowItemElement?.offsetTop', itemBottom);

          if (!isEmptyValues(transformHeight)) {
            console.log('topMenus', transformHeight, item);
            topMenus.push({
              title: currentRowLayouts?.data?.prefix,
              top: transformHeight,
              bottom: itemBottom,
              ...item,
            });
          }
        }
      }
    });

    setTopMenus(topMenus);
  };

  useEffect(() => {
    document
      .getElementById('dmr-content-container')
      ?.addEventListener('scroll', (event) => {
        let scrollTop =
          document.getElementById('dmr-content-container')?.scrollTop || 0;

        let contentElement = document.getElementById('dmr-content-container');
        let scrollAtEnd =
          Math.abs(
            contentElement.scrollHeight -
              contentElement.clientHeight -
              contentElement.scrollTop,
          ) < 1;

        let menuPositionIndex = cloneDeep(topMenus)
          ?.reverse()
          ?.findIndex((item) => item.top <= scrollTop + menuScrollTopPadding);

        if (menuPositionIndex > -1) {
          let actualIndex = topMenus?.length - (menuPositionIndex + 1);

          if (scrollAtEnd) {
            setMenuCurrent(topMenus?.length - 1);
          } else {
            setMenuCurrent(actualIndex);
          }
        }
      });

    return () => {
      document
        .getElementById('dmr-content-container')
        ?.removeEventListener('scroll', (event) => {});
    };
  }, [topMenus]);

  // menu change
  const onMenuChange = (current) => {
    let currentSelectMenuItem = topMenus?.at(current);
    let scrollContainer = document.getElementById('dmr-content-container');

    if (currentSelectMenuItem) {
      if (current === 0 || current === topMenus?.length - 1 || menuViewTop) {
        scrollToPositionWaitingToEnd(
          currentSelectMenuItem?.top,
          scrollContainer,
        )?.then(() => {
          if (currentSelectMenuItem?.focusId) {
            waitFocusElementRefocus(currentSelectMenuItem?.focusId);
          }
        });
      } else {
        scrollIntoViewWaitingToEnd(
          currentSelectMenuItem?.focusId,
          scrollContainer,
        )?.then(() => {
          if (currentSelectMenuItem?.focusId) {
            waitFocusElementRefocus(currentSelectMenuItem?.focusId);
          }
        });
      }

      setMenuCurrent(current);
    }
  };

  return (
    <Affix
      className={`dmr-menu-container ${
        props?.isFullscreen === true
          ? 'dmr-menu-container-doctor-emr-enable'
          : ''
      }`}
      offsetTop={10}
    >
      <Steps
        progressDot
        current={menuCurrent}
        onChange={onMenuChange}
        direction="vertical"
        items={topMenus}
      />
    </Affix>
  );
};

export default RightMenu;
