import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src/commonService';
import { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useRequest } from 'umi';
import { DetailColumnItem } from './interface';
import {
  defaultCheckedColumnStateProcessor,
  selectedTableColumnsProcessor,
} from './utils';
import _ from 'lodash';
import { columns } from '../DetailColumnSettingContent/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import md5 from 'crypto-js/md5';
import {
  Button,
  Divider,
  message,
  Modal,
  Space,
  TableProps,
  Tooltip,
} from 'antd';
import { ExportIconBtn, UniTable } from '@uni/components/src/index';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import './index.less';
import { Emitter } from '@uni/utils/src/emitter';
import { DetailColumnSettingContentConstants } from '../DetailColumnSettingContent/constants';
import React from 'react';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import DetailColumnsSettingModal from '@uni/components/src/query-detail-columns-settings';

const pageSize = 9;

export interface IDetailColumnsModalProps {
  type: string;
  detailColumns: any[]; // columns 默认值
  dictData: any; // 字典数据
  columnState: any; // columnState
  record?: any;
  modalTitle?: string;
  onClose?: () => void;
  onOk?: (selectedItems: any[]) => void;
}

const DetailTableModal = ({
  tableParams,
  visible,
  record,
  modalTitle,
  dictData,
  onClose,
}) => {
  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
  // 添加AbortController引用，用于取消请求
  const abortControllerRef = useRef<AbortController | null>(null);
  const [detailColumns, setDetailColumns] = useState<DetailColumnItem[]>([]);
  const [detailTableDataSource, setDetailTableDataSource] = useState([]);
  const [columnsState, setColumnState] = useState<any>({});

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: pageSize,
    pageSizeOptions: ['9', '10', '20', '50', '100'],
    hideOnSinglePage: false,
  });
  const [sorterFilter, setSorterFilter] = useState({});

  // total retColSubject
  // 获取所有列定义
  const {
    data: retColSubject,
    loading: combineQueryDataColumnLoading,
    run: combineQueryDataColumnReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Analysis/AnaModelDef/GetRetColSubjectList', {
        params: { TableName: 'OmniCard' },
      }),
    {
      manual: true,
      formatResult: (r) =>
        r.code === 0 && r.statusCode === 200
          ? r.data.map((c) => ({
              ...c,
              data: c.name,
              dataIndex: c.name,
              originTitle: c.title,
            }))
          : [],
    },
  );

  // column template 列配置 // 获取模板列
  const {
    loading: columnsReqLoading,
    run: columnsReq,
    data: columnsData,
  } = useRequest(
    (metricName) => {
      return uniCommonService(`Api/DmrAnalysis/CoderEfficiency/GetTemplate`, {
        method: 'GET',
        params: {
          metricName,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return response.data;
        }
        return [];
      },
    },
  );

  // 获取数据
  const {
    loading: combineQueryDataLoading,
    run: combineQueryDataReq,
    cancel: cancelCombineQueryDataReq,
  } = useRequest(
    (reqData, current, pageSize) => {
      // 创建新的AbortController
      const controller = new AbortController();
      abortControllerRef.current = controller;

      let data = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        basicArgs: {
          ...tableParams,
          ...reqData.data,
        },
        MetricName: reqData?.MetricName,
        outputColumns: reqData?.outputColumns,
      };

      if (!isEmptyValues(sorterFilter)) {
        delete global['tableParameters'];
        data['DtParam'] = {
          ...data['DtParam'],
          ...sorterFilter,
        };
      }

      return uniCommonService('Api/DmrAnalysis/CoderEfficiency/GetDetails', {
        method: 'POST',
        requestType: 'json',
        data: data,
        signal: controller.signal, // 使用AbortController的signal
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableResp<any, any>>) => {
        return response;
      },
      onSuccess: (response: RespVO<TableResp<any, any>>, params) => {
        // 检查请求是否已被取消
        if (response?.cancelled === true) {
          return;
        }

        if (response?.code === 0 && response?.statusCode === 200) {
          if (isEmptyValues(response?.data?.data)) {
            if (params[1] === 1) {
              setDetailTableDataSource([]);
            }
            return;
          }

          setDetailTableDataSource(response?.data?.data);

          setBackPagination({
            ...backPagination,
            current: backPagination?.current,
            total: response?.data?.recordsTotal || 0,
          });
        } else {
          setDetailTableDataSource([]);
          setBackPagination({
            ...backPagination,
            current: 1,
            total: 0,
          });
        }
      },
    },
  );

  // 保存 column template
  const { run: saveColumnsReq } = useRequest(
    (data) => {
      return uniCommonService(`Api/DmrAnalysis/CoderEfficiency/SaveTemplate`, {
        method: 'POST',
        data: {
          ...data,
          MetricName: record?.MetricName,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          // 使用setTimeout延迟事件触发，避免同步更新引起的问题
          Emitter.emit(
            `${DetailColumnSettingContentConstants.MODAL_CLOSE}_${record?.MetricName}`,
          );
          // reload columns
          columnsReq(record?.MetricName);
        }
      },
    },
  );

  // 处理columns
  useEffect(() => {
    if (!retColSubject || retColSubject?.length < 1 || !columnsData) return;

    // 创建新的状态对象，避免引用关系
    const processedColumnState = defaultCheckedColumnStateProcessor(
      retColSubject,
      columnsData,
    );

    // 使用函数式更新，避免闭包问题
    setColumnState(() => ({ ...processedColumnState }));

    // 使用函数式更新，避免可能的引用问题
    setDetailColumns(() =>
      tableColumnBaseProcessor(
        [],
        retColSubject?.map(
          (col) =>
            ({
              ...col,
              title: processedColumnState?.[col.name]?.title ?? col?.title,
            } as DetailColumnItem),
        ),
      ),
    );
  }, [retColSubject, columnsData]);

  // 初始化获取列配置
  useEffect(() => {
    if (record?.MetricName) {
      combineQueryDataColumnReq();
      columnsReq(record?.MetricName);
    }
  }, [record?.MetricName]);

  const getSelectedColumns = (value = null) => {
    return selectedTableColumnsProcessor(value ?? columnsState);
  };

  // 获取当前表格中所有的HisId
  const currentHisIds = detailTableDataSource
    ?.map((item) => item?.HisId)
    ?.filter((item) => !isEmptyValues(item));

  // 处理切换到最后一个病案的逻辑
  const onExtraSearchedHisIdLastOne = async () => {
    // 这里可以添加当切换到最后一个病案时需要执行的逻辑
    // 直接获取下一页 然后吐回去
    if (detailTableDataSource?.length >= backPagination?.total) {
      return;
    }
    if (!combineQueryDataLoading) {
      let outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        message.error('请选择需要展示的列');
        setDetailTableDataSource([]);
        return;
      }

      // 使用localRecord代替record
      const data = { ...(record?.specialData || {}) };
      if (record?.Coder) {
        data['Coders'] = [record?.Coder];
      } else if (record?.CliDept) {
        data['CliDepts'] = [record?.CliDept];
      } else if (record?.Chief) {
        data['Chiefs'] = [record?.Chief];
      } else if (record?.MedTeam) {
        data['MedTeams'] = [record.MedTeam];
      }

      let tableResponse = await combineQueryDataReq(
        {
          data,
          outputColumns,
          MetricName: record?.MetricName,
        },
        backPagination.current,
        backPagination.pageSize,
      );

      if (tableResponse?.code === 0 && tableResponse?.statusCode === 200) {
        let tableData = tableResponse?.data?.data;
        return tableData
          ?.map((item) => item?.HisId)
          ?.filter((item) => !isEmptyValues(item));
      } else {
        return [];
      }
    }

    return [];
  };

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorters,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    const outputColumns = getSelectedColumns();
    if (outputColumns?.length === 0) {
      message.error('请选择需要展示的列');
      setDetailTableDataSource([]);
      return;
    }

    // 特殊处理 sorter
    const sorterFilter = {
      order: [],
      columns: [],
    };

    if (!isEmptyValues(sorters)) {
      const sorterArray = Array.isArray(sorters) ? sorters : [sorters];
      sorterArray.forEach((sorter) => {
        if (sorter?.order) {
          const orderIndex = outputColumns?.findIndex(
            (item) => item.Name === (sorter?.columnKey || sorter?.field),
          );

          if (orderIndex !== -1) {
            sorterFilter.order.push({
              column: orderIndex,
              dir: sorter?.order === 'ascend' ? 'asc' : 'desc',
            });
          }
        }
      });
    }

    setSorterFilter(sorterFilter);

    setTimeout(() => {
      // 使用localRecord代替record
      const data = { ...(record?.specialData || {}) };
      if (record?.Coder) {
        data['Coders'] = [record?.Coder];
      } else if (record?.CliDept) {
        data['CliDepts'] = [record?.CliDept];
      } else if (record?.Chief) {
        data['Chiefs'] = [record?.Chief];
      } else if (record?.MedTeam) {
        data['MedTeams'] = [record.MedTeam];
      }

      combineQueryDataReq(
        {
          data,
          outputColumns,
          MetricName: record?.MetricName,
        },
        pagi.current,
        pagi.pageSize,
      );
    }, 0);
  };

  const getExportCaptionByColumns = (
    columns: any[],
    selectedColumns: any[],
  ) => {
    let exportCaption = selectedColumns?.map((d) => {
      let columnItem = columns?.find((item) => item?.id === d?.Id);

      return {
        ...d,
        columnScale: columnItem?.scale,
        columnPrecision: columnItem?.precision,
        ExportTitle: d?.CustomTitle ?? columnItem?.title ?? '',
      };
    });

    return exportCaption;
  };

  // data fetch  请求数据 & 重置分页
  useEffect(() => {
    if (record && record?.MetricName && !isEmptyValues(columnsState)) {
      // 创建本地副本而不是直接使用外部对象
      const pagination = {
        ...backPagination,
        current: 1,
        total: 0,
      };

      const outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        if (!isEmptyValues(columnsState)) {
          message.error('请选择需要展示的列');
        }
        setDetailTableDataSource([]);
        return;
      }

      // 创建data的副本
      const data = { ...(record?.specialData || {}) };
      if (record?.Coder) {
        data['Coders'] = [record?.Coder];
      } else if (record?.CliDept) {
        data['CliDepts'] = [record?.CliDept];
      } else if (record?.Chief) {
        data['Chiefs'] = [record?.Chief];
      } else if (record?.MedTeam) {
        data['MedTeams'] = [record.MedTeam];
      }

      // 使用setTimeout延迟API调用，避免引起父组件的重渲染
      setTimeout(() => {
        combineQueryDataReq(
          {
            data,
            outputColumns,
            MetricName: record?.MetricName,
          },
          pagination.current,
          pagination.pageSize,
        );
      }, 0);

      setBackPagination(pagination);
    }
  }, [columnsState, record]);

  // 自定义取消函数
  const customCancelRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    // 同时调用 useRequest 提供的 cancel 函数
    cancelCombineQueryDataReq();
  };

  // 保存列配置
  const onSaveCols = (items) => {
    saveColumnsReq({
      Subjects: items?.map((item) => {
        return {
          SubjectId: item.id,
          ColumnSort: item?.order,
          CustomTitle: item?.customTitle,
        };
      }),
    });
  };

  const tableProps = {
    id: 'combine-query-table',
    rowKey: 'Id',
    scroll: {
      x: 'max-content',
      // y: 400,
    },
    columns: [
      ...columns(currentHisIds, onExtraSearchedHisIdLastOne),
      ...detailColumns,
    ] as any[],
    dataSource: detailTableDataSource,
    loading: combineQueryDataLoading || combineQueryDataColumnLoading,
    clickable: false,
    pagination: backPagination,
    dictionaryData: dictData,
    widthCalculate: true,
    onChange: backTableOnChange,
    bordered: true,
    columnsState: {
      value: columnsState,
    },
    forceColumnsUpdate: true,
    infiniteScroll: false,
    infiniteScrollPageSize: pageSize,
    bottomReachMargin: 200,
    emptyDataYHeight: true,
    enableHeaderNoWrap: true,
    enableMaxCharNumberEllipses: true,
  };

  return (
    <>
      <Modal
        className="detail-modal"
        width={'calc(100% - 50px)'}
        style={{ top: 20 }}
        title={
          <div className="detail-modal-header d-flex">
            <h3 style={{ marginBottom: 0 }}>{`${modalTitle} 病案明细`}</h3>
            <Space.Compact className="btn_space">
              {(userInfo?.Roles ?? [])?.includes('Admin') && (
                <Tooltip title={'列配置'}>
                  <Button
                    type="text"
                    shape="circle"
                    icon={
                      <SettingOutlined
                        onPointerEnterCapture
                        onPointerLeaveCapture
                        className="infinity_rotate"
                      />
                    }
                    onClick={() => {
                      Emitter.emit(
                        `${DetailColumnSettingContentConstants.MODAL_OPEN}_${record?.MetricName}`,
                        {
                          status: true,
                        },
                      );
                    }}
                  />
                </Tooltip>
              )}

              <ExportIconBtn
                isBackend={true}
                backendObj={{
                  url: 'Api/DmrAnalysis/CoderEfficiency/ExportGetDetails',
                  method: 'POST',
                  data: {
                    outputColumns: getExportCaptionByColumns(
                      detailColumns,
                      getSelectedColumns(),
                    ),
                    basicArgs: {
                      ...tableParams,
                      ...(record?.specialData || {}),
                      ...{
                        ...(record?.Coder ? { Coders: [record.Coder] } : {}),
                        ...(record?.CliDept
                          ? { CliDepts: [record.CliDept] }
                          : {}),
                        ...(record?.Chief ? { Chiefs: [record.Chief] } : {}),
                        ...(record?.MedTeam
                          ? { MedTeams: [record.MedTeam] }
                          : {}),
                      },
                    },
                    MetricName: record?.MetricName,
                  },
                  fileName: `病案明细`,
                }}
                btnDisabled={detailTableDataSource?.length < 1}
              />
              <Divider type="vertical" />
              <CloseOutlined
                className="detail-modal-close-x"
                onPointerEnterCapture
                onPointerLeaveCapture
                onClick={(e) => {
                  e.stopPropagation();
                  customCancelRequest();
                  setDetailTableDataSource([]);
                  onClose && onClose();
                }}
              />
            </Space.Compact>
          </div>
        }
        open={visible}
        closable={false}
        footer={null}
        forceRender={true}
        destroyOnClose={true}
        zIndex={99}
        onCancel={() => {
          customCancelRequest();
          setDetailTableDataSource([]);
          onClose && onClose();
        }}
      >
        <UniTable {...tableProps} />
      </Modal>

      <DetailColumnsSettingModal
        type={record?.MetricName}
        detailColumns={tableProps.columns}
        columnState={columnsState}
        onOk={onSaveCols}
      />
    </>
  );
};

export default React.memo(DetailTableModal);
