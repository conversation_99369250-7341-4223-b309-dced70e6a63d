export interface DmrCardCheck {
  DmrCardId?: number;
  Reviews?: DmrCardReviewItem[];
  ScoreLevel?: string;
  TotalScore?: string;

  MaxAllowedCheckErrorType?: string;
  PassCheckFlag?: boolean;
  SucceededFlag?: boolean;
}

interface DmrCardReviewItem {
  CheckCategory?: string;
  CheckCategoryName?: string;
  Details?: DmrCardReviewDetailItem[];
  DisplayErrMsg?: string;
  EntityId?: number;
  ErrorLevel?: string;
  RuleColumnName?: string;
  RuleType?: string;
  SubType?: string;
  SuperviseStage?: string;
}

interface DmrCardReviewDetailItem {
  ColumnDesc?: string;
  ColumnName?: string;
  ErrMsg?: string;
  OriginalInputValue?: string;
  RuleCode?: string;
  RuleId?: number;
}

export interface IcdeOperResp {
  Data?: IcdeOperItem[];
  RecordsTotal?: number;
}

export interface OperComboResp {
  Data?: OperComboItem[];
  RecordsTotal?: number;
}

export interface OperComboItem {
  Code?: string;
  Name?: string;
  OperRate?: string;
  PrimaryOperCode?: string;
  RelatedDepts?: string[];
  RelatedOperCodes?: string[];
  Remark?: string;
}

export interface IcdeOperItem {
  Code?: string;
  Name?: string;

  InsurCode?: string;
  InsurName?: string;
  HqmsCode?: string;
  HqmsName?: string;

  IsObsolete?: boolean;
  IsDaySurgery?: boolean;
  IsMicro?: boolean;

  Degree?: string;
  DegreeRemark?: string;
  OperType?: string;

  key?: string;
  label?: any;

  IcdeCode?: string;
  IcdeName?: string;
  OperCode?: string;
  OperName?: string;

  TcmIcdeCode?: string;

  type?: string;
}

export interface IcdeResp {
  IcdeAdms?: IcdeRespItem[];
  IcdeDamgs?: IcdeRespItem[];
  IcdeDscgs?: IcdeRespItem[];
  IcdeOtps?: IcdeRespItem[];
  IcdePathos?: IcdeRespItem[];
  IcdeTcms?: IcdeRespItem[];
  IcdeBabys?: IcdeRespItem[];
}

export interface TcmIcdeResp {
  TcmIcdeAdms?: IcdeRespItem[];
  TcmIcdeDscgs?: IcdeRespItem[];
  TcmIcdeOtps?: IcdeRespItem[];
}

export class IcdeRespItem {
  DmrCardId?: number;
  HighDiagEvid?: string;
  HisId?: string;
  IcdeCode?: string;
  IcdeCond?: string;
  IcdeDate?: string;
  IcdeId?: number;
  IcdeName?: string;
  IcdeNote?: string;
  IcdeOutcome?: string;
  IcdePert?: string;
  IcdeSort?: number;
  InhospIcdeCode?: string;
  InhospIcdeName?: string;
  IsMain?: boolean;
  IsReported?: boolean;
  PalgNo?: string;

  UniqueId?: string;

  BkupDegCode?: string;
  TumorIcdeCode?: string;
  TumorIcdeName?: string;
  TumorDiagEvid?: string;
  TumorStaging_T?: string;
  TumorStaging_N?: string;
  TumorStaging_M?: string;
  TumorStagingType?: string;
  TumorTrtCond?: string;
  TumorTrtItem?: string;
  TumorTrtDesc?: string;
  TumorMultPrimary?: string;
  TumorLateralPosition?: string;
  TumorInitialTrtHosp?: string;
  TumorInitialTrtItem?: string;

  TcmIcdeCategory?: string;
  BabySort?: number;
}

export interface DmrChsGroup {
  BmResult?: DmrChsGroupBm;
  Card?: any;
  ChsDrgsResult?: DmrChsResult[];
  ChsSettleResults?: any;
  AdjustDrgsResult?: any[];
  IsCalculated?: boolean;
}

export interface DmrChsGroupBm {
  BmAvgInPeriod?: number;
  BmAvgMaterialFee?: number;
  BmAvgMedicineFee?: number;
  BmAvgTotalFee?: number;
  BmDeathRatio?: number;
  BmMaterialFeeRatio?: number;
  BmMedicineFeeRatio?: number;
}

export interface DmrChsResult {
  DrgCode?: string;
  DrgName?: string;
  ADrgCode?: string;
  GType?: string;
  GroupType?: string;
}

export interface DmrInsuranceHqmsIcdeItem {
  HisId?: string;
  IcdeCode?: string;
  IcdeCond?: string;
  IcdeId?: number;
  Id?: number;
  IcdeName?: string;
  IcdeSort?: number;
  InsurCardId?: number;
  IsMain?: boolean;
  IsReported?: boolean;
  SrcIcdeCode?: string;
  SrcIcdeName?: string;
  IcdeDiscriminator?: string;

  UniqueId?: string;

  IcdeOutcome?: string;
  IcdeDate?: string;
  HqmsCardId?: number;

  insuranceMetaData?: IcdeOperItem;
  hqmsMetaData?: IcdeOperItem;
}

export interface DmrInsuranceHqmsOperItem {
  Id?: number;
  InsurCardId?: number;
  HisId?: string;
  IsMain?: boolean;
  OperGroupNo?: string;
  OperSort?: number;
  OperCode?: string;
  OperName?: string;
  SrcOperCode?: string;
  SrcOperName?: string;
  OperRate?: string;
  SrcOperRate?: string;
  OperType?: string;
  Operator?: string;
  Firstasst?: string;
  Secondasst?: string;
  WoundHealingRateClass?: string;
  OprnOprtBegntime?: string;
  OprnOprtEndtime?: string;
  AnaType?: string;
  AnaDoc?: string;
  AnstLvCode?: string;
  AnstBegntime?: string;
  AnstEndtime?: string;
  IsReported?: boolean;
}

export interface TcmIcde {
  TcmIcdeOtps?: any[];
  TcmIcdeAdms?: any[];
  TcmIcdeDscgs?: any[];
}

export interface MedicalRecord {
  state?: number;
  payload?: string;
}

export class CardBundleInfo {
  HisId: string;
  Babys: any[];
  Bloods: any[];
  CardFlat: any;
  HqmsIcdeDscgs: DmrInsuranceHqmsIcdeItem[];
  HqmsOpers: DmrInsuranceHqmsOperItem[];
  IcdeResult: IcdeResp;
  TcmIcdeResult: TcmIcdeResp;
  Icus: any[];
  InsurIcdeDscgs: DmrInsuranceHqmsIcdeItem[];
  InsurOpers: DmrInsuranceHqmsOperItem[];
  Opers: any[];
  Transfers: any[];

  DmrIcdeOperMetaData: IcdeOperMetaData;
  HqmsIcdeOperMetaData: IcdeOperMetaData;
  InsurIcdeOperMetaData: IcdeOperMetaData;

  // Preg
  Preg?: Preg;
}

export interface IcdeOperMetaData {
  IcdeMetaData?: IcdeOperResp;
  OperMetaData?: IcdeOperResp;
}

export interface CardBundleCheck {
  CenterSettleResult?: CenterSettleResult;
  ChsMetricsResult?: DmrChsGroup;
  DrgsMetricsResult?: any;
  QualityCheckResult?: DmrCardCheck;
  HqmsMetricsResult?: any;
}

export interface CenterSettleResult {
  AbnFeeType?: string;
  BaseCwPoint?: number;
  ChsDrgCode?: string;
  ChsDrgName?: string;
  CwPoint?: number;
  CwPointCoefficient?: number;
  SetlId?: string;
}

export interface Preg {
  PatInBorn?: string;
  PatDanger?: string;
  ProlongBore?: string;
  DelayBore?: string;
  HurryBore?: string;
  PregScred?: string;
  Gestation?: string;
  AmniFluid?: string;
  BpRessure?: string;
  PatBroken?: string;
  MatBroken?: string;
  PregTimes?: number;
  PregNumb?: number;
  BearNumb?: number;
  BirthNum?: number;
  Flooding?: number;
  Midwifes?: string;
  InMethod?: string;
  InReason?: string;
  InMale?: string;
  InFemale?: string;
  GenderAmb?: string;
  InducSex?: string;
  InCertif?: string;
  InducWeight?: number;
  PostpartumDiag?: string;
  FirstStageOfLabor?: string;
  FirstStageOfLaborTreatCodes?: string;
  FirstStageOfLaborTreatNames?: string;
  SecondStageOfLabor?: string;
  SecondStageOfLaborTreatCodes?: string;
  SecondStageOfLaborTreatNames?: string;
  ThirdStageOfLabor?: string;
  ThirdStageOfLaborTreatCodes?: string;
  ThirdStageOfLaborTreatNames?: string;
  IndicationsForSurgery?: string;
  GestationDay?: number;
  BirthNumStr?: string;
  ROAMMethod?: string;
  PrematureRuptureHour?: number;
  AmniFluidVolume?: number;
}
