import { ExportIconBtn, UniTable } from '@uni/components/src';
import React, { useEffect, useState } from 'react';
import {
  CombineQueryDetail,
  DetailColumnItem,
} from '@/pages/combine-query/interfaces';
import { useModel, useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import {
  Button,
  Card,
  Divider,
  message,
  Select,
  Space,
  TableProps,
  Tag,
} from 'antd';
import { StatsAnalysisEventConstant } from '@/constants';
import {
  activeColumnsWithIndexProcessor,
  columnDataCustomTitleStateProcessor,
  columnSettingColumnsProcessor,
  defaultCheckedColumnStateProcessor,
  selectedTableColumnsProcessor,
  tableToEchartsTableColumnsProcessor,
} from '@/pages/combine-query/processor';
import ColumnSetting, { Container } from '@uni/components/src/column-setting';
import { downloadFile } from '@uni/utils/src/download';
import { isEmptyValues } from '@uni/utils/src/utils';
import ReactDragColumnView from '@/pages/combine-query/components/drag-listview/ReactDragColumnView';
import _, { cloneDeep } from 'lodash';
import './index.less';
import { columns } from '@/pages/combine-query/containers/table/columns';
import CombineQueryDetailColumnSettings from '@/pages/combine-query/containers/table/detail-columns';
// import CombineQueryDetailColumnSettings from '@/pages/combine-query/containers/table/columns-setting';
import ColumnTemplateTitleAdd from '@/pages/combine-query/containers/column-save';
interface CombineQueryTableProps {
  tableName: string;
  searchedFormValue: any;
  templateDisease: string[];
  totalDiseaseList?: any;
}

const CombineQueryTable = (props: CombineQueryTableProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [detailColumns, setDetailColumns] = useState<DetailColumnItem[]>([]);

  const [detailTableDataSource, setDetailTableDataSource] = useState([]);

  const [columnsState, setColumnState] = useState<any>({});

  const [defaultColumnsState, setDefaultColumnState] = useState<any>({});

  const [query, setQuery] = useState(undefined);

  const [combineQueryDetail, setCombineQueryDetail] =
    useState<CombineQueryDetail>({});

  const [combineQueryTitle, setCombineQueryTitle] = useState('');

  const [
    combineQueryDetailColumnTemplateTitle,
    setCombineQueryDetailColumnTemplateTitle,
  ] = useState('');

  const [
    combineQueryDetailColumnTemplateId,
    setCombineQueryDetailColumnTemplateId,
  ] = useState(undefined);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // new 病种过滤select
  const [filterOptions, setFilterOptions] = useState([
    { Code: 'ALL', Name: '全部病种' },
  ]);
  const [selectedFilter, setSelectedFilter] = useState('ALL');

  useEffect(() => {
    combineQueryDataColumnReq();

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      (data: CombineQueryDetail) => {
        setQuery(data?.Expr);
        setCombineQueryDetail(data);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TITLE,
      (title: string) => {
        setCombineQueryTitle(title);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_CLICK,
      (item) => {
        if (item?.Id) {
          detailColumnsTemplatedGetReq(item?.Id);
          setCombineQueryDetailColumnTemplateId(item?.Id);
          setCombineQueryDetailColumnTemplateTitle(item?.Title);
        }
      },
    );

    // 查询模板点击后，明细模板的点击要清空
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK,
      (item) => {
        setCombineQueryDetailColumnTemplateTitle(undefined);
      },
    );

    // 新增病种结束后重新调getStats
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_DISEASE_DATA_RELOAD,
      (diseases) => {
        // console.log('diseases', diseases);
        onDetailLoad(diseases);
      },
    );

    Emitter.on(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET, () => {
      // 清除数据
      let pagination = {
        ...backPagination,
        current: 1,
        pageSize: 10,
        total: 0,
      };
      setQuery(undefined);
      setCombineQueryDetail({});
      setCombineQueryTitle('');
      setBackPagination(pagination);
      setDetailTableDataSource([]);
    });

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      );

      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TITLE,
      );

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK);

      Emitter.off(StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_CLICK);

      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_DISEASE_DATA_RELOAD,
      );

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET);
    };
  }, []);

  useEffect(() => {
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-detail`,
      (item) => {
        if (item?.Id === combineQueryDetailColumnTemplateId) {
          setCombineQueryDetailColumnTemplateTitle(item?.Title);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS,
      (id) => {
        if (id === combineQueryDetailColumnTemplateId) {
          setCombineQueryDetailColumnTemplateId(undefined);
          // setCombineQueryDetailColumnTemplateTitle('');
        }
      },
    );

    return () => {
      Emitter.off(
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-detail`,
      );
      Emitter.off(
        StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS,
      );
    };
  }, [combineQueryDetailColumnTemplateId]);

  useEffect(() => {
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE,
      columnsState,
    );
  }, [columnsState]);

  useEffect(() => {
    Emitter.on('COLUMN_SETTING_TITLE_EDIT', (data) => {
      if (data?.columnKey && data?.title) {
        let columnItem = detailColumns?.find(
          (item) => item.name === data?.columnKey,
        );
        if (columnItem) {
          // columnItem['originTitle'] = columnItem['title'];
          columnItem['title'] = data?.title;
          columnItem['customTitle'] = data?.title;
        }

        setDetailColumns([...detailColumns]);
      }
    });

    return () => {
      Emitter.off('COLUMN_SETTING_TITLE_EDIT');
    };
  }, [detailColumns]);

  const onDetailLoad = (diseases = undefined) => {
    let pagination = {
      ...backPagination,
      current: 1,
      pageSize: 10,
      total: 0,
    };

    console.log('onDetailLoad', columnsState);
    if (
      props.searchedFormValue &&
      (diseases?.length > 0 || props.templateDisease?.length > 0) &&
      !_.isEmpty(columnsState)
    ) {
      let outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        message.error('请选择需要展示的列');
        setDetailTableDataSource([]);
        return;
      }

      combineQueryDataReq(
        props.searchedFormValue,
        selectedFilter === 'ALL'
          ? diseases?.length > 0
            ? diseases
            : props.templateDisease
          : [selectedFilter],
        pagination.current,
        pagination.pageSize,
        outputColumns,
      );
    }

    setBackPagination(pagination);
  };

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY,
      (query) => {
        // setQuery(query);
        // onDetailLoad();
      },
    );
    // console.log(columnsState, props.searchedFormValue, selectedFilter);
    // 获取details内容的
    onDetailLoad();

    return () => {
      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY);
    };
  }, [columnsState, props.searchedFormValue, selectedFilter]);

  useEffect(() => {
    if (props?.totalDiseaseList?.length > 0) {
      setFilterOptions([
        {
          Name: '全部病种',
          Code: 'ALL',
        },
        ...props?.templateDisease?.map((d) => ({
          Name: props?.totalDiseaseList?.find((v) => v.Id === d)?.Title,
          Code: d,
        })),
      ]);
    }
  }, [props?.templateDisease, props?.totalDiseaseList]);

  useEffect(() => {
    let columnState = defaultCheckedColumnStateProcessor(
      detailColumns,
      combineQueryDetail?.SubjectOutputColumns,
      null,
      'diseaseSignificant',
    );
    // console.log('columnState', columnState);
    setColumnState(columnState);
    setDefaultColumnState(cloneDeep(columnState));
  }, [detailColumns, combineQueryDetail]);

  useEffect(() => {
    let columnData = columnDataCustomTitleStateProcessor(
      detailColumns,
      combineQueryDetail?.SubjectOutputColumns,
    );

    setDetailColumns(columnData?.slice());
  }, [combineQueryDetail]);

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    if (props.searchedFormValue && props.templateDisease?.length > 0) {
      let outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        message.error('请选择需要展示的列');
        setDetailTableDataSource([]);
        return;
      }

      combineQueryDataReq(
        props.searchedFormValue,
        props.templateDisease,
        pagi.current,
        pagi.pageSize,
        outputColumns,
      );
    }
  };

  const { loading: combineQueryDataLoading, run: combineQueryDataReq } =
    useRequest(
      (searchedValues, diseaseList, current, pageSize, columns) => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          outputColumns: columns,
          basicArgs: searchedValues,
          diseases: diseaseList,
          //   expr: JSON.stringify(query),
        };

        return uniCombineQueryService(
          'Api/DmrAnalysis/DiseaseAnalysis/GetDetails',
          {
            method: 'POST',
            requestType: 'json',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableResp<any, any>>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setDetailTableDataSource(response?.data?.data);

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsTotal || 0,
            });
          } else {
            setDetailTableDataSource([]);
            setBackPagination({
              ...backPagination,
              total: 0,
            });
          }
        },
      },
    );

  const {
    loading: combineQueryDataColumnLoading,
    run: combineQueryDataColumnReq,
  } = useRequest(
    () => {
      let data = {
        TableName: props?.tableName,
      };

      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetRetColSubjectList',
        {
          params: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let tableColumns = response?.data?.slice();
          tableColumns?.forEach((item, index) => {
            item['width'] = 200;
            item['dataIndex'] = item?.name;
            item['originTitle'] = item?.title;
          });
          setDetailColumns(tableColumns);
        } else {
          setDetailColumns([]);
          setColumnState({});
          setDefaultColumnState({});
        }
      },
    },
  );

  const {
    loading: detailColumnsTemplateGetLoading,
    run: detailColumnsTemplatedGetReq,
  } = useRequest(
    (id) => {
      return uniCombineQueryService(
        'Api/DmrAnalysis/DiseaseAnalysisTemplate/Get',
        {
          params: {
            id: id,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<CombineQueryDetail>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (!isEmptyValues(response?.data?.SubjectOutputColumns)) {
            let currentDetail = Object.assign({}, combineQueryDetail);
            currentDetail['SubjectOutputColumns'] =
              response?.data?.SubjectOutputColumns;

            setCombineQueryDetail(currentDetail);
            setCombineQueryDetailColumnTemplateTitle(response?.data?.Title);
          } else {
            message.warn('当前模板不存在列');
          }
        }
      },
    },
  );

  const {
    loading: detailColumnsTemplateSaveLoading,
    run: detailColumnsTemplatedSaveReq,
  } = useRequest(
    (data) => {
      return uniCombineQueryService(
        'Api/DmrAnalysis/DiseaseAnalysisTemplate/SaveDetailOutputTemplate',
        {
          method: 'POST',
          requestType: 'json',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<string>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('保存成功');
          setCombineQueryDetailColumnTemplateId(response?.data);
          Emitter.emit(
            StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS,
            response?.data,
          );
          Emitter.emit(
            StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_EXPAND,
          );
          return 'success';
        } else {
          message.error('保存失败');
          return 'fail';
        }
      },
      onSuccess: (data, params) => {
        // 保存 另存为 在这边更新title
        if (data === 'success') {
          setCombineQueryDetailColumnTemplateTitle(params?.at(0)?.Title);
        }
      },
    },
  );

  const getSelectedColumns = () => {
    return selectedTableColumnsProcessor(columnsState);
  };

  const getExportCaptionByColumns = (
    columns: any[],
    selectedColumns: any[],
  ) => {
    let exportCaption = selectedColumns?.map((d) => {
      let columnItem = columns?.find((item) => item?.id === d?.Id);
      return {
        ...d,
        ExportTitle: d?.CustomTitle ?? columnItem?.title ?? '',
      };
    });

    return exportCaption;
  };

  // 导出数据
  const exportDetailTable = async () => {
    message.success('明细导出中....');
    let outputColumns = getSelectedColumns();
    let data = {
      basicArgs: props.searchedFormValue,
      diseases: props.templateDisease,
      outputColumns: getExportCaptionByColumns(
        detailColumns,
        getSelectedColumns(),
      ), // getSelectedColumns(),
      //   expr: JSON.stringify(query),
      exportCaption: getExportCaptionByColumns(detailColumns, outputColumns),
    };

    let detailExportResponse = await uniCombineQueryService(
      'Api/DmrAnalysis/DiseaseAnalysis/ExportDetails',
      {
        method: 'POST',
        requestType: 'json',
        data: data,
      },
    );

    if (detailExportResponse?.response) {
      message.success('明细导出成功');
      downloadFile(
        `组合查询明细-${
          combineQueryTitle || combineQueryDetail?.Title || '未命名'
        }`,
        detailExportResponse?.response,
      );
    } else {
      message.success('明细导出失败，请联系管理员');
    }
  };

  // 这个是保存
  const saveDetailColumnsTemplate = async (id, title) => {
    message.success('列模板保存中....');
    let outputColumns = getSelectedColumns();
    let data = {
      subjectOutputColumns: outputColumns,
    };

    if (id) {
      data['id'] = id;
    }

    data['Title'] = title ?? combineQueryDetailColumnTemplateTitle;
    // todo ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    detailColumnsTemplatedSaveReq(data);
  };

  // 这个是另存为
  const saveAsDetailColumnsTemplate = async (title) => {
    message.success('列模板另存为中....');
    let outputColumns = getSelectedColumns();
    let data = {
      subjectOutputColumns: outputColumns,
    };
    data['Title'] = title ?? combineQueryDetailColumnTemplateTitle;
    // todo ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    detailColumnsTemplatedSaveReq(data);
  };

  const tableProps = {
    id: 'combine-query-table',
    rowKey: 'HisId',
    scroll: { x: 'max-content', y: 240 },
    columns: [
      {
        data: 'AnaDiseaseName',
        title: '病种名称',
        dataIndex: 'AnaDiseaseName',
        width: 200,
        visible: true,
        order: 0,
        fixed: 'left',
      },
      ...detailColumns,
    ] as any[],
    dataSource: detailTableDataSource,
    loading:
      combineQueryDataLoading ||
      combineQueryDataColumnLoading ||
      detailColumnsTemplateGetLoading,
    clickable: false,
    pagination: backPagination,
    // TODO dict data
    dictionaryData: globalState?.dictData,
    onChange: backTableOnChange,
    bordered: true,
    // 用于 columns控制显隐等
    columnsState: {
      value: columnsState,
    },
    forceColumnsUpdate: true,
  };

  console.log('columns', tableProps?.columns);

  return (
    <Container initValue={tableProps}>
      <Card
        className={'combine-query-detail-table-container'}
        title={
          <Space>
            <span>明细</span>
            <Tag>
              {combineQueryDetailColumnTemplateTitle
                ? `模板：${combineQueryDetailColumnTemplateTitle}`
                : '无模板'}
            </Tag>
            <Select
              style={{ minWidth: 100 }}
              value={selectedFilter}
              onChange={(value) => {
                setSelectedFilter(value);
              }}
              options={filterOptions}
              fieldNames={{ label: 'Name', value: 'Code' }}
            />
          </Space>
        }
        extra={
          <Space>
            <Button
              key="button"
              loading={detailColumnsTemplateSaveLoading}
              onClick={() =>
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS,
                  {
                    originTitle: combineQueryDetailColumnTemplateTitle,
                    type: 'saveAs',
                  },
                )
              }
            >
              另存为模板
            </Button>
            <Button
              key="button"
              loading={detailColumnsTemplateSaveLoading}
              onClick={() =>
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD,
                  {
                    originTitle: combineQueryDetailColumnTemplateTitle,
                    type: 'save',
                  },
                )
              }
            >
              保存列模板
            </Button>
            <CombineQueryDetailColumnSettings
              nextGeneration={false}
              columns={columnSettingColumnsProcessor(
                detailColumns,
                columnsState,
              )}
              columnsMap={columnsState}
              onColumnSelect={(value) => {
                // null 表示重置
                if (value) {
                  setColumnState(value);
                } else {
                  setColumnState(defaultColumnsState);
                }
              }}
            />
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/DmrAnalysis/DiseaseAnalysis/ExportGetDetails',
                method: 'POST',
                data: {
                  basicArgs: props.searchedFormValue,
                  diseases: props.templateDisease,
                  outputColumns: getExportCaptionByColumns(
                    detailColumns,
                    getSelectedColumns(),
                  ), // getSelectedColumns(),
                  //   expr: JSON.stringify(query),
                  // exportCaption: getExportCaptionByColumns(
                  //   detailColumns,
                  //   getSelectedColumns(),
                  // ),
                },
                fileName: `病种统计明细-${
                  combineQueryTitle || combineQueryDetail?.Title || '未命名'
                }`,
              }}
              btnDisabled={detailTableDataSource?.length < 1}
            />
          </Space>
        }
      >
        {/* <ReactDragColumnView
          onDragEnd={onDragEnd}
          nodeSelector="th"
          scrollElement={document.querySelector(
            "div#combine-query-table div[class='ant-table-body']",
          )}
        >

        </ReactDragColumnView> */}
        <UniTable
          {...tableProps}
          // toolBarRender={() => [
          // ]}
        />
      </Card>

      <ColumnTemplateTitleAdd
        eventName={[
          StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD,
          StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS,
        ]}
        onTemplateTitleFillIn={(title, type) => {
          if (type === 'save') {
            saveDetailColumnsTemplate(
              combineQueryDetailColumnTemplateId,
              title,
            );
          } else if (type === 'saveAs') {
            saveAsDetailColumnsTemplate(title);
          }
        }}
      />
    </Container>
  );
};

export default CombineQueryTable;
