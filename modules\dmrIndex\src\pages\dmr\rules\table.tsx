import { icdeColumns, operationColumns } from '@/pages/dmr/columns';
import { showNotification } from '@/utils/notification';
import flatMap from 'lodash/flatMap';
import { filterIdAddAndAllCellEmptyRow } from '@/pages/dmr/processors/processors';
import { isEmptyValues } from '@uni/utils/src/utils';

// let icdeRequiredKeys = ['IcdeCode', 'IcdeCond', 'IcdeOutcome'];
let icdeRequiredKeys = ['IcdeCode'];

// let operationRequiredKeys = ['OperCode', 'OprnOprtBegntime'];

let operationRequiredKeys = ['OperCode'];

let operationInterventionalKeys = ['Operator', 'Firstasst', 'Secondasst'];

let operationWoundKeys = [
  ...operationInterventionalKeys,
  'WoundHealingRateClass',
  'AnaType',
  'AnaDoc',
];

let insuranceRequiredKeys = ['InsurCode'];

export const icdeTableRules = (form: any, formFieldValues: any) => {
  let icdeInvalidateKeys = [];

  let filteredEmptyIcdes = formFieldValues?.['diagnosis-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );

  filteredEmptyIcdes?.forEach((tableItem, index) => {
    icdeRequiredKeys?.forEach((key) => {
      if (isEmptyValues(tableItem?.[key])) {
        // TODO push 什么
        let keyInTableIndex = icdeColumns
          ?.filter((item) => item?.visible)
          ?.findIndex((item) => item.dataIndex === key);
        icdeInvalidateKeys.push(`Table#${tableItem?.id}#${keyInTableIndex}`);
      }
    });
  });

  // 额外的一个条件：保证IsReported 至少有一个
  let icdeIsReportedCnt = 0;
  filteredEmptyIcdes?.forEach((tableItem, index) => {
    if (tableItem?.IsReported === true) {
      icdeIsReportedCnt++;
    }
  });

  if (filteredEmptyIcdes?.length > 0 && icdeIsReportedCnt === 0) {
    // 提示 至少需要一个
    showNotification('请检查诊断表格中必须存在一条医保上报诊断');
  }

  if (icdeInvalidateKeys?.length > 0) {
    showNotification(
      tableErrorKeysToLabels(
        '请检查诊断表格中必填项是否都填写完成',
        icdeInvalidateKeys,
        icdeColumns,
        formFieldValues?.['diagnosis-table'],
      ),
    );
  }

  return icdeInvalidateKeys;
};

export const icdeTableIsMainRequiredRules = (
  form: any,
  formFieldValues: any,
) => {
  let filteredEmptyOpers = formFieldValues?.['diagnosis-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );

  let icdeIsMainCnt = 0;

  filteredEmptyOpers?.forEach((tableItem, index) => {
    if (tableItem?.IsMain === true) {
      icdeIsMainCnt++;
    }
  });

  if (filteredEmptyOpers?.length > 0) {
    if (icdeIsMainCnt === 0) {
      // 提示 至少需要一个
      showNotification('请检查诊断表格中必须存在一条医保主诊');

      let columns = flatMap(icdeColumns?.slice(), (item) => {
        if (item?.children) {
          return item?.children;
        }
        return item;
      })?.slice();

      let keyInTableIndex = columns
        ?.filter((item) => item?.visible)
        ?.findIndex((item) => item.dataIndex === 'IsMain');

      return [`Table#${filteredEmptyOpers?.at(0)?.id}#${keyInTableIndex}`];
    }
  }

  return [];
};

export const icdeTableInsuranceRules = (form: any, formFieldValues: any) => {
  let icdeInvalidateKeys = [];

  let filteredEmptyIcdes = formFieldValues?.['diagnosis-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );

  filteredEmptyIcdes?.forEach((tableItem, index) => {
    insuranceRequiredKeys?.forEach((key) => {
      if (isEmptyValues(tableItem?.[key])) {
        // TODO push 什么
        let keyInTableIndex = icdeColumns
          ?.filter((item) => item?.visible)
          ?.findIndex((item) => item.dataIndex === key);
        icdeInvalidateKeys.push(`Table#${tableItem?.id}#${keyInTableIndex}`);
      }
    });
  });

  if (icdeInvalidateKeys?.length > 0) {
    showNotification(
      tableErrorKeysToLabels(
        '请检查诊断表格中必填项是否都填写完成',
        icdeInvalidateKeys,
        icdeColumns,
        formFieldValues?.['diagnosis-table'],
      ),
    );
  }

  return icdeInvalidateKeys;
};

export const operationTableRules = (form: any, formFieldValues: any) => {
  let operationInvalidateKeys = [];

  let filteredEmptyOpers = formFieldValues?.['operation-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );
  filteredEmptyOpers?.forEach((tableItem, index) => {
    operationInvalidateKeys.push(
      ...operationNullColumnItemKey(operationRequiredKeys, tableItem),
    );
  });

  if (operationInvalidateKeys?.length > 0) {
    showNotification(
      tableErrorKeysToLabels(
        '请检查手术表格中必填项是否都填写完成',
        operationInvalidateKeys,
        flatMap(operationColumns?.slice(), (item) => {
          if (item?.children) {
            return item?.children;
          }

          return item;
        })?.slice(),
        formFieldValues?.['operation-table'],
      ),
    );
  }

  return operationInvalidateKeys;
};

export const operationTableIsMainRequiredRules = (
  form: any,
  formFieldValues: any,
) => {
  let filteredEmptyOpers = formFieldValues?.['operation-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );

  // 额外的一个条件：当存在 IsReported > 0 时 必须存在至少一个IsMain
  let operIsReportedCnt = 0;
  let operIsMainCnt = 0;

  filteredEmptyOpers?.forEach((tableItem, index) => {
    if (tableItem?.IsReported === true) {
      operIsReportedCnt++;
    }
    if (tableItem?.IsMain === true) {
      operIsMainCnt++;
    }
  });

  if (filteredEmptyOpers?.length > 0) {
    if (operIsReportedCnt > 0) {
      if (operIsMainCnt === 0) {
        // 提示 至少需要一个
        showNotification('请检查手术表格中必须存在一条医保主手术');

        let columns = flatMap(operationColumns?.slice(), (item) => {
          if (item?.children) {
            return item?.children;
          }
          return item;
        })?.slice();

        let keyInTableIndex = columns
          ?.filter((item) => item?.visible)
          ?.findIndex((item) => item.dataIndex === 'IsMain');

        return [`Table#${filteredEmptyOpers?.at(0)?.id}#${keyInTableIndex}`];
      }
    }
  }

  return [];
};

export const operationTableInsuranceRules = (
  form: any,
  formFieldValues: any,
) => {
  let operationInvalidateKeys = [];

  let filteredEmptyOpers = formFieldValues?.['operation-table']?.filter(
    (item) => filterIdAddAndAllCellEmptyRow(item),
  );
  filteredEmptyOpers?.forEach((tableItem, index) => {
    operationInvalidateKeys.push(
      ...operationNullColumnItemKey(insuranceRequiredKeys, tableItem),
    );
  });

  if (operationInvalidateKeys?.length > 0) {
    showNotification(
      tableErrorKeysToLabels(
        '请检查手术表格中必填项是否都填写完成',
        operationInvalidateKeys,
        flatMap(operationColumns?.slice(), (item) => {
          if (item?.children) {
            return item?.children;
          }

          return item;
        })?.slice(),
        formFieldValues?.['operation-table'],
      ),
    );
  }

  return operationInvalidateKeys;
};

const operationNullColumnItemKey = (
  needValidateKeys: string[],
  tableItem: any,
) => {
  let invalidateKeys = [];

  let columns = flatMap(operationColumns?.slice(), (item) => {
    if (item?.children) {
      return item?.children;
    }

    return item;
  })?.slice();

  needValidateKeys?.forEach((key) => {
    if (isEmptyValues(tableItem?.[key])) {
      // TODO push 什么
      let keyInTableIndex = columns
        ?.filter((item) => item?.visible)
        ?.findIndex((item) => item.dataIndex === key);
      invalidateKeys.push(`Table#${tableItem?.id}#${keyInTableIndex}`);
    }
  });

  return invalidateKeys;
};

export const operationTableExtraRules = (form: any, formFieldValues: any) => {
  let operationExtraInvalidateKeys = [];

  console.error('operationtable', formFieldValues?.['operation-table']);
  formFieldValues?.['operation-table']
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item))
    ?.forEach((tableItem) => {
      // 介入治疗
      if (tableItem?.OperType?.toString() === '3') {
        operationExtraInvalidateKeys.push(
          ...operationNullColumnItemKey(operationInterventionalKeys, tableItem),
        );
      }

      // 手术治疗
      if (tableItem?.OperType?.toString() === '4') {
        operationExtraInvalidateKeys.push(
          ...operationNullColumnItemKey(operationWoundKeys, tableItem),
        );
      }
    });

  return operationExtraInvalidateKeys;
};

export const tableErrorKeysToLabels = (
  description: string,
  keys: string[],
  columns: any[],
  tableDataSource: any[],
) => {
  let labelsWithIndex = [];
  keys?.map((key) => {
    let keyArr = key.split('#');
    let tableRowId = keyArr[1];
    let columnIndexInTableColumns = parseInt(keyArr[2]);

    let columnTitleInTableDataSource = columns?.filter(
      (item) => item?.visible,
    )?.[columnIndexInTableColumns]?.title;
    let tableRowIndex = tableDataSource?.findIndex(
      (item) => item?.id?.toString() === tableRowId,
    );

    if (columnTitleInTableDataSource && tableRowIndex !== -1) {
      labelsWithIndex.push({
        label: columnTitleInTableDataSource,
        index: tableRowIndex,
      });
    }
  });

  return buildInTableNotificationDescription(description, labelsWithIndex);
};

export const buildInTableNotificationDescription = (
  description: string,
  labelsWithIndex: any[],
) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {description && <span>{description}</span>}
      <span style={{ fontWeight: 'bold' }}>未填写项：</span>
      {labelsWithIndex?.map((item) => {
        return (
          <span>
            第{item?.index + 1}行 {item?.label}
          </span>
        );
      })}
    </div>
  );
};
