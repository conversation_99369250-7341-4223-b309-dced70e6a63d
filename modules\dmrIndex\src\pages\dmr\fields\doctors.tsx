import { IGridItem } from '@/pages/dmr/interfaces';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';

export const doctors: IGridItem[][] = [
  [
    {
      data: {
        prefix: '科主任',
        key: 'Director',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Director',
          modelDataKey: 'Employee',
          placeholder: '请选择科主任',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 8,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '主任(副主任)医师',
        key: 'Chief',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Chief',
          modelDataKey: 'Employee',
          placeholder: '请选择',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 6,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 8,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '主治医师',
        key: 'Attending',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Attending',
          modelDataKey: 'Employee',
          placeholder: '请选择主治医师',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '住院医师',
        key: 'Resident',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Resident',
          modelDataKey: 'Employee',
          placeholder: '请选择住院医师',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
  ],
  [
    {
      data: {
        prefix: '责任护士',
        key: 'RespNurse',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'RespNurse',
          modelDataKey: 'Employee',
          placeholder: '请选择责任护士',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '进修医师',
        key: 'PracDoctor',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '实习医师',
        key: 'InternDoc',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
    {
      // TODO 登录用户限定
      data: {
        prefix: '编码员',
        key: 'Coder',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          disabled: true,
          formKey: 'Coder',
          modelDataKey: 'Coder',
          placeholder: '请选择编码员',
          optionNameKey: 'label',
          optionValueKey: 'Code',
          optionTitleKey: 'Name',
          optionLabelProp: 'title',
          labelFormat: '{Code} {Name}',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 6,
      },
      xxs: {
        w: 10,
      },
    },
  ],
];
