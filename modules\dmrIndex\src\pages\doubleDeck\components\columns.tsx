import {
  drgsDegreeMap,
  hqmsDegreeMap,
  icdeExtraMap,
  IcdeExtraTagsItem,
  IcdeOperationInputSelector,
  IcdeOperationReadonlyItem,
  IcdeOperCheckbox,
  IcuDurationFieldInput,
  operationExtraMap,
  OperationExtraTagsItem,
  OperationFieldInput,
  OperIcdeExtraMapItem,
  PathologyIcdeFieldInput,
} from '@uni/grid/src/components/icde-oper-input/input';
import {
  DeleteOutlined,
  InfoCircleTwoTone,
  PlusCircleTwoTone,
} from '@ant-design/icons';
import React from 'react';
import { Popconfirm, Tag, Tooltip } from 'antd';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import OperationSelect from '@/pages/dmr/components/oper-select';
import DateSelect from '@uni/grid/src/components/date-select';
import RestrictInputNumber from '@uni/grid/src/components/restrict-number';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';
import IconBtn from '@uni/components/src/iconBtn';
import dayjs from 'dayjs';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import { UniReadOnlySelect } from '@/pages/doubleDeck/components/readonly';

interface ExtraTitlePromptItem {
  [key: string]: any;
}

const extraTitlePrompt = (
  prompts: ExtraTitlePromptItem,
  extraMap: { [key: string]: OperIcdeExtraMapItem },
) => {
  return (
    <>
      {Object.keys(extraMap)?.map((key, index) => {
        if (key === 'HqmsDegree') {
          return (
            <>
              {Object.keys(hqmsDegreeMap)
                ?.slice(-1)
                ?.map((hqmsKey, index) => {
                  return (
                    <PromptItem
                      color={hqmsDegreeMap?.[hqmsKey]?.color}
                      display={hqmsDegreeMap?.[hqmsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        hqmsDegreeMap?.[hqmsKey]?.label
                      }
                      extraLine={true}
                    />
                  );
                })}
            </>
          );
        }

        if (key === 'DrgsDegree') {
          return (
            <>
              {Object.keys(drgsDegreeMap)
                ?.slice(-1)
                ?.map((drgsKey, index) => {
                  return (
                    <PromptItem
                      color={drgsDegreeMap?.[drgsKey]?.color}
                      display={drgsDegreeMap?.[drgsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        drgsDegreeMap?.[drgsKey]?.label
                      }
                      extraLine={
                        index !== Object.keys(drgsDegreeMap)?.length - 1
                      }
                    />
                  );
                })}
            </>
          );
        }
        return (
          <PromptItem
            color={extraMap?.[key]?.color}
            display={extraMap?.[key]?.display}
            prompt={prompts?.[key]?.prompt ?? extraMap?.[key]?.label}
            extraLine={index !== Object.keys(extraMap)?.length - 1}
          />
        );
      })}
    </>
  );
};

const PromptItem = ({ color, display, prompt, extraLine }) => {
  return (
    <>
      <Tag
        style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
        color={color}
      >
        {display}
      </Tag>
      <span>{prompt}</span>
      {extraLine && <br />}
    </>
  );
};

const extraTitle = (
  extraMap?: { [key: string]: OperIcdeExtraMapItem },
  prompts?: ExtraTitlePromptItem,
) => {
  return (
    <Tooltip title={extraTitlePrompt(prompts, extraMap)}>
      <span>
        注<InfoCircleTwoTone style={{ marginLeft: 3 }} />
      </span>
    </Tooltip>
  );
};

export const icdeColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    render: (node, record, index) => {
      return (
        <IcdeExtraTagsItem
          value={record?.['IcdeExtra']}
          nameKey={'IcdeExtra'}
        />
      );
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '出院诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    render: (node, record, index) => {
      return <span>{record?.['IcdeCode']}</span>;
    },
  },
  {
    dataIndex: 'IcdeName',
    title: '出院诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    render: (node, record, index) => {
      return <span>{record?.['IcdeName']}</span>;
    },
  },
  {
    dataIndex: 'IsMain',
    title: '医保主诊',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      return <span>{record?.['IsMain'] ? '是' : '否'}</span>;
    },
  },
  {
    dataIndex: 'IsReported',
    title: '医保上报',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      return <span>{record?.['IsReported'] ? '是' : '否'}</span>;
    },
  },
];

export const operationColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'OperExtra',
    title: extraTitle(operationExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
      IsMicro: {
        prompt: '微创手术',
      },
      HqmsDegree: {
        prompt: '国考手术等级',
      },
      DrgsDegree: {
        prompt: 'DRG手术等级',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    render: (node, record, index) => {
      return (
        <OperationExtraTagsItem
          record={record}
          eventName={`${EventConstant.DMR_OPER_SELECT_ADD}#${record?.id}`}
          nameKey={'OperExtra'}
          conditionDictionaryKey={'SSJB'}
          conditionDictionaryGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'OperCode',
    title: '手术及操作编码',
    visible: true,
    fixed: 'left',
    width: 120,
    render: (node, record, index) => {
      return <span>{record?.['OperCode']}</span>;
    },
  },
  {
    dataIndex: 'OperName',
    title: '手术及操作名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 130,
    render: (node, record, index) => {
      return <span>{record?.['OperName']}</span>;
    },
  },
  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术及操作日期',
    visible: true,
    align: 'center',
    width: 166,
    render: (node, record, index) => {
      return (
        <span>
          {record?.['OprnOprtBegntime']
            ? dayjs(record?.['OprnOprtBegntime'])?.format('YYYY-MM-DD HH:mm:ss')
            : record?.['OprnOprtBegntime']}
        </span>
      );
    },
  },
  {
    dataIndex: 'OperRate',
    title: '手术级别',
    visible: true,
    align: 'center',
    width: 50,
    render: (node, record, index) => {
      return (
        <IcdeOperationReadonlyItem
          value={record?.OperRate}
          conditionDictionaryKey={'SSJB'}
          conditionDictionaryGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'WoundHealingRateClass',
    title: '手术切口愈合等级',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      return (
        <UniReadOnlySelect
          value={record?.WoundHealingRateClass}
          modelDataKey={'QKYHLB'}
          modelDataGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'OprnPatnTypeCode',
    title: '手术类别',
    visible: true,
    width: 100,
    align: 'center',
    render: (node, record, index) => {
      return (
        <UniReadOnlySelect
          value={record?.OprnPatnTypeCode}
          modelDataKey={'OprnPatnType'}
          modelDataGroup={'Dmr'}
        />
      );
    },
  },
];

export const pathologyIcdeColumns = [
  {
    dataIndex: 'PathologyIcdeCode',
    title: '病理诊断编码',
    align: 'center',
    visible: true,
    width: '20%',
    render: (node, record, index) => {
      return <span>{record?.['PathologyIcdeCode']}</span>;
    },
  },
  {
    dataIndex: 'PathologyIcdeName',
    title: '病理诊断名称',
    visible: true,
    readonly: true,
    width: '50%',
    render: (node, record, index) => {
      return <span>{record?.['PathologyIcdeName']}</span>;
    },
  },
  {
    dataIndex: 'PalgNo',
    title: '病理号',
    visible: true,
    width: '15%',
    render: (node, record, index) => {
      return <span>{record?.['PalgNo']}</span>;
    },
  },
  {
    dataIndex: 'IcdeNote',
    title: '病理结果',
    visible: true,
    width: '15%',
    render: (node, record, index) => {
      return <span>{record?.['IcdeNote']}</span>;
    },
  },
];

export const icuColumns = [
  {
    dataIndex: 'IcuSort',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    render: (node, record, index) => {
      return <span>{index + 1}</span>;
    },
  },
  {
    dataIndex: 'InpoolIcuTime',
    title: '进重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>
          {record?.['InpoolIcuTime']
            ? dayjs(record?.['InpoolIcuTime'])?.format('YYYY-MM-DD HH:mm:ss')
            : record?.['InpoolIcuTime']}
        </span>
      );
    },
  },
  {
    dataIndex: 'OutIcuTime',
    title: '出重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>
          {record?.['OutIcuTime']
            ? dayjs(record?.['OutIcuTime'])?.format('YYYY-MM-DD HH:mm:ss')
            : record?.['OutIcuTime']}
        </span>
      );
    },
  },
  {
    dataIndex: 'IcuCategory',
    title: '监护类型',
    visible: true,
    align: 'center',
    width: 200,
    render: (node, record, index) => {
      return (
        <IcdeOperationReadonlyItem
          value={record?.IcuCategory}
          conditionDictionaryKey={'IcuCategory'}
          conditionDictionaryGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'IcuCode',
    title: '重症监护病房类型',
    visible: true,
    align: 'center',
    width: 180,
    render: (node, record, index) => {
      return (
        <IcdeOperationReadonlyItem
          value={record?.IcuCode}
          conditionDictionaryKey={'IcuType'}
          conditionDictionaryGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'IcuDuration',
    title: '重症监护使用时长（小时）',
    visible: true,
    width: 150,
    render: (node, record, index) => {
      return <span>{record?.['IcuDuration']}</span>;
    },
  },
];

export const tcmIcdeColumns = [
  {
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    render: (node, record, index) => {
      return (
        <IcdeExtraTagsItem
          value={record?.['IcdeExtra']}
          nameKey={'IcdeExtra'}
        />
      );
    },
  },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主病</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span>主证</span>;
      }

      return labelNode;
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '中医诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    render: (node, record, index) => {
      return <span>{record?.['IcdeCode']}</span>;
    },
  },
  {
    dataIndex: 'IcdeName',
    title: '中医诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    render: (node, record, index) => {
      return <span>{record?.['IcdeName']}</span>;
    },
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'tcmDiagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '出院情况',
    visible: true,
    align: 'center',
    width: 90,
    render: (node, record, index) => {
      return (
        <IcdeOperationReadonlyItem
          value={record?.IcdeOutcome}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Dmr'}
        />
      );
    },
  },
  {
    dataIndex: 'TreatMethodCode',
    title: '治则治法编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    render: (node, record, index) => {
      return <span>{record?.['TreatMethodCode']}</span>;
    },
  },
  {
    dataIndex: 'TreatMethodName',
    title: '治则治法名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    render: (node, record, index) => {
      return <span>{record?.['TreatMethodName']}</span>;
    },
  },
];

export const departmentTransferColumns = [
  {
    dataIndex: 'InCliDept',
    visible: false,
  },
  {
    dataIndex: 'InDeptHours',
    visible: false,
  },
  {
    dataIndex: 'OutCliDept',
    visible: false,
  },
  {
    dataIndex: 'TransferInDate',
    visible: false,
  },
  {
    dataIndex: 'TransferOutDate',
    visible: false,
  },
  {
    dataIndex: 'TransferSort',
    visible: false,
  },
];
