@import '~@uni/commons/src/style/variables.less';
.province-selector-container {
}

.separate-province-selector-container {
  display: flex;
  flex-direction: row;
  align-items: center;

  .selector-item-container {
    display: flex;
    flex: 2;
    align-items: center;
    flex-flow: row wrap;
  }

  .select-container {
    width: auto !important;
    //min-width: 100px !important;
  }

  .border-none {
    border: none !important;
    box-shadow: none !important;
  }

  .label {
    flex: 0 1 0;
    white-space: nowrap;
  }

  .selector-container {
    //max-width: 120px !important;
    border-bottom: 1px solid @border-color;

    .ant-select-selector {
      padding: 0px !important;
    }
  }

  .ant-select-focused .ant-select-selector,
  .ant-select-selector:focus,
  .ant-select-selector:hover,
  .ant-select-selector:active,
  .ant-select-open .ant-select-selector {
    border-color: #d9d9d9 !important;
    box-shadow: none !important;
  }

  .detail-address {
    //min-width: 180px;
    min-width: 140px;
    border-bottom: 1px solid @border-color;
  }

  .detail-address:focus,
  .detail-address:active,
  .detail-address:hover {
    border-bottom: 1px solid @border-color;
  }
}
