import { useEffect, useState } from 'react';
import { ColumnItem } from '@uni/commons/src/interfaces';
import { Tree } from './Tree';
import { Table } from './Table';
import { QueryDetailColumnSettingsProps } from '../types/interfaces';

export const Content = (props: QueryDetailColumnSettingsProps) => {
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  useEffect(() => {
    let checkedKeys = [];
    if (props?.columnsMap) {
      Object.keys(props?.columnsMap)?.forEach((key) => {
        if (props?.columnsMap?.[key]?.show) {
          checkedKeys.push(key);
        }
      });
    }

    setCheckedKeys(checkedKeys);
  }, [props?.columnsMap]);

  return (
    <div className={'detail-columns-setting-info-container'}>
      <Tree
        columnTreeContainerRef={props?.columnTreeContainerRef}
        treeData={props?.treeData}
        checkKeys={checkedKeys}
        extraData={props?.extraData}
      />
      {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
        <>
          <div className={'detail-columns-separator'} />
          <Table treeData={props?.treeData} />
        </>
      )}
    </div>
  );
};
