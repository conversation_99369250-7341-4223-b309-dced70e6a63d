import { useEffect, useState } from 'react';
import {
  Modal,
  Button,
  Space,
  Radio,
  message,
  Tooltip,
  Typography,
} from 'antd';
import UniTable from '@uni/components/src/table';
import { UniSelect } from '@uni/components/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useModel, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { debounce } from 'lodash';
import WhitelistSelect from './WhitelistSelect';

enum QualityCheckCategory {
  Custom = '0',
  CodeReview = '9',
}

interface RuleSettingsModalProps {
  visible: boolean;
  record: any;
  cliDepts: any[];
  onClose: (hasChanges: boolean) => void;
  selectErrorLevelDataSource: any[];
  onUpdateRule: (data: any) => void;
  onUpdateCategory: (data: { ruleCode: string; checkCategory: string }) => void;
  onUpdatePick: (data: any) => void;
}

const RuleSettingsModal: React.FC<RuleSettingsModalProps> = ({
  visible,
  record,
  cliDepts,
  onClose,
  selectErrorLevelDataSource,
  onUpdateRule,
  onUpdateCategory,
  onUpdatePick,
}) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const [hasChanges, setHasChanges] = useState(false);
  const [localRecord, setLocalRecord] = useState<any>(null);

  const [selectedCliDepts, setSelectedCliDepts] = useState<string[]>([]);
  const [selectUser, setSelectUser] = useState({
    UserIds: [],
    RoleNames: [],
  });

  // UpdateQualityCheckCategory
  const { run: updateQualityCheckCategoryReq } = useRequest(
    (data: { ruleCode: string; checkCategory: string }) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckCategory`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>, params) => {
        setHasChanges(true);
      },
      onError: (res: RespVO<any>) => {
        message.error('修改失败，请联系管理员');
      },
    },
  );
  const updateWhitelist = debounce((type, data) => {
    updatePicks(type, data);
  }, 500);

  // CliDepts、UserIds、RoleNames
  const { run: updatePicks } = useRequest(
    (type: string, data: any) => {
      const requestUrl = `Api/Sys/QualitySys/UpdateQualityCheck${type}Picks`;
      return uniCommonService(requestUrl, {
        method: 'POST',
        data: {
          RuleCode: localRecord?.RuleCode,
          PickMode: 'White',
          ...data,
        },
      });
    },
    {
      manual: true,
      onSuccess: () => {
        setHasChanges(true);
      },
      onError: () => {
        message.error('更新失败');
      },
    },
  );

  // 初始化白名单数据
  const initWhitelistData = (record: any) => {
    // 处理科室白名单
    if (record?.CliDeptPicks) {
      const whitePicks = record.CliDeptPicks.filter(
        (item) => item.PickMode === 'White',
      );
      setSelectedCliDepts(whitePicks.map((item) => item.CliDept));
    } else {
      setSelectedCliDepts([]);
    }
    // 处理用户和用户组白名单
    if (record?.IdentityPicks) {
      const whitePicks = record.IdentityPicks.filter(
        (item) => item.PickMode === 'White',
      );
      setSelectUser({
        UserIds: whitePicks?.[0]?.UserIds,
        RoleNames: whitePicks?.[0]?.RoleNames,
      });
    } else {
      setSelectUser({
        UserIds: [],
        RoleNames: [],
      });
    }
  };

  useEffect(() => {
    setLocalRecord(record);
    initWhitelistData(record);
  }, [record]);

  if (!localRecord) return null;

  const handleClose = () => {
    onClose(hasChanges);
    setTimeout(() => {
      setHasChanges(false);
    }, 20);
  };

  // console.log('picksColumns record', record);

  const picksColumns = () => {
    return [
      {
        dataIndex: 'Description',
        title: '描述详情',
        visible: true,
      },
      {
        width: 250,
        title: '规则等级',
        visible: true,
        dataIndex: 'ErrorLevel',
        render: (_, record, index) => {
          const hasTooltip = !isEmptyValues(record?.['tooltipLabel']);
          const borderStyle = hasTooltip
            ? { border: '1px solid #eb5757' }
            : { border: '1px solid #d9d9d9' };

          const handleChange = (value) => {
            setHasChanges(true);
            onUpdatePick({
              RuleCode: localRecord.RuleCode,
              ...localRecord.Picks[index],
              ErrorLevel: value,
              IsValid: value !== '0', // 当value为'0'时IsValid为false，否则为true
            });
          };

          return (
            <Tooltip
              color={'#eb5757'}
              title={record?.['tooltipLabel']}
              open={hasTooltip}
            >
              <UniSelect
                style={borderStyle}
                allowClear={false}
                placeholder={'请选择'}
                optionValueKey={'Code'}
                optionNameKey={'Name'}
                optionLabelProp={'title'}
                bordered={false}
                defaultValue={record?.IsValid ? _ : '0'}
                showSearch={false}
                dataSource={selectErrorLevelDataSource}
                onChange={handleChange}
              />
            </Tooltip>
          );
        },
      },
      // {
      //   visible: true,
      //   title: '是否有效',
      //   align: 'center',
      //   width: 100,
      //   dataIndex: 'IsValid',
      //   render: (_, record, index) => {
      //     return (
      //       <Switch
      //         defaultChecked={_ || false}
      //         onChange={(value) => {
      //           setHasChanges(true);
      //           onUpdatePick({
      //             RuleCode: localRecord.RuleCode,
      //             ...localRecord.Picks[index],
      //             IsValid: value,
      //           });
      //         }}
      //       />
      //     );
      //   },
      // },
    ];
  };

  return (
    <Modal
      title={
        <Typography.Text
          ellipsis={{ tooltip: localRecord?.DisplayErrMsg }}
          style={{ maxWidth: 'calc(100% - 30px)', display: 'inline-block' }}
        >
          {localRecord?.DisplayErrMsg}
        </Typography.Text>
      }
      open={visible}
      onCancel={handleClose}
      destroyOnClose
      footer={[
        <Button key="close" onClick={handleClose}>
          关闭
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: '100%', gap: '16px' }}>
        <div
          style={{
            marginBottom: 16,
          }}
        >
          <WhitelistSelect
            label="科室白名单"
            placeholder="请选择科室白名单"
            dataSource={cliDepts}
            value={selectedCliDepts}
            onChange={(value) => {
              setHasChanges(true);
              setSelectedCliDepts(value);
              updateWhitelist('CliDept', {
                CliDepts: value,
              });
            }}
          />

          <WhitelistSelect
            label="用户白名单"
            placeholder="请选择用户白名单"
            dataSource={globalState?.dictData?.['Mr']?.User}
            value={selectUser.UserIds}
            onChange={(value) => {
              setHasChanges(true);
              setSelectUser({
                ...selectUser,
                UserIds: value,
              });
              updateWhitelist('RuleIdentity', {
                ...selectUser,
                UserIds: value,
              });
            }}
          />

          <WhitelistSelect
            label="用户组白名单"
            placeholder="请选择用户组白名单"
            dataSource={globalState?.dictData?.RoleWithDesc}
            value={selectUser?.RoleNames}
            marginBottom={0}
            onChange={(value) => {
              setHasChanges(true);
              setSelectUser({
                ...selectUser,
                RoleNames: value,
              });
              updateWhitelist('RuleIdentity', {
                ...selectUser,
                RoleNames: value,
              });
            }}
          />
        </div>

        <div>
          <span style={{ marginRight: '8px' }}>监控类别：</span>
          <Radio.Group
            value={
              localRecord?.RuleCode?.startsWith('9-')
                ? localRecord?.CheckCategory
                : QualityCheckCategory.Custom
            }
            disabled={!localRecord?.RuleCode?.startsWith('9-')}
            onChange={(e) => {
              updateQualityCheckCategoryReq({
                ruleCode: localRecord?.RuleCode,
                checkCategory: e.target.value,
              });
              setLocalRecord((prev) => ({
                ...prev,
                CheckCategory: e.target.value,
              }));
            }}
          >
            <Radio value={QualityCheckCategory.Custom}>首页规则</Radio>
            <Radio value={QualityCheckCategory.CodeReview}>编码规则</Radio>
          </Radio.Group>
        </div>

        {localRecord?.Picks?.length > 0 && (
          <div>
            <span style={{ marginBottom: '8px', display: 'block' }}>
              规则等级设置：
            </span>
            <UniTable
              id={`picks-table-in-form-${localRecord?.RuleCode}`}
              rowKey={'RuleTemplate'}
              columns={picksColumns()}
              dataSource={localRecord?.Picks || []}
              size="small"
            />
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default RuleSettingsModal;
