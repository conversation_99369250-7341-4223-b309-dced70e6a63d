import { requiredKeys } from '@/pages/dmr/constants';
import { feeRules } from '@/pages/dmr/rules/fee';
import {
  buildNotificationDescription,
  searchLabelByKeys,
  showNotification,
} from '@/utils/notification';
import { unconscious } from '@/pages/dmr/rules/unconscious';
import { outTypeRules } from '@/pages/dmr/rules/out-type';
import { allergyRules } from '@/pages/dmr/rules/allergy';
import { returnPlanRules } from '@/pages/dmr/rules/return-plan';
import {
  babyBwRules,
  babyIwRules,
  babyMonthDayRules,
} from '@/pages/dmr/rules/baby';
import { idCardRules } from '@/pages/dmr/rules/idCard';
import {
  bornDateRules,
  inHospOutHospDateRules,
  patAgeAIORules,
} from '@/pages/dmr/rules/date';
import {
  icdeTableRules,
  operationTableExtraRules,
  operationTableRules,
  icdeTableInsuranceRules,
  operationTableInsuranceRules,
  operationTableIsMainRequiredRules,
  icdeTableIsMainRequiredRules,
} from '@/pages/dmr/rules/table';
import { isEmptyValues } from '@uni/utils/src/utils';
import { ReactNode } from 'react';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  getDmrIndexLayoutConfig,
  getDmrPreCheckRulesConfig,
} from '@/pages/dmr/network/get';
import { PreCheckRule } from '@/pages/dmr/interfaces';
import { valueDependencyRules } from '@/pages/dmr/rules/value-dependency';

export const PreCheckRules: PreCheckRule[] = [
  {
    keys: ['出生日期'],
    description: '出生日期早于入院时间',
    enable: true,
    ruleKey: 'bornDateRules',
  },
  {
    keys: ['新生儿出生天月'],
    description: '新生儿出生月小于11大于0，天小于30大于0',
    enable: true,
    ruleKey: 'babyMonthDayRules',
  },
  {
    keys: ['新生儿出生体重'],
    description: '新生儿出生体重大于100克并小于9999克',
    enable: true,
    ruleKey: 'babyBwRules',
  },
  {
    keys: ['新生儿入院体重'],
    description: '新生儿出生体重大于100克并小于9999克',
    enable: true,
    ruleKey: 'babyIwRules',
  },
  {
    keys: ['证件号码'],
    description: '证件号码在身份证下为15位或者18位',
    enable: false,
    ruleKey: 'idCardRules',
  },
  {
    keys: ['出院时间'],
    description: '出院时间须晚于入院时间',
    enable: true,
    ruleKey: 'inHospOutHospDateRules',
  },
  {
    keys: ['医嘱转院'],
    description: '医嘱转院在特定类型下需要填写字段',
    enable: true,
    ruleKey: 'outTypeRules',
  },
  {
    keys: ['过敏药物'],
    description: '过敏药物在”是“下需要填写',
    enable: true,
    ruleKey: 'allergyRules',
  },
  {
    keys: ['昏迷时间'],
    description: '昏迷时间在时间范围内',
    enable: false,
    ruleKey: 'unconscious',
  },
  {
    keys: ['再住院目的'],
    description: '再住院目的在特定情况下填写',
    enable: true,
    ruleKey: 'returnPlanRules',
  },
  {
    keys: ['自付金额，总费用，费用'],
    description: '自付金额小于总费用以及费用不能小于0',
    enable: true,
    ruleKey: 'feeRules',
  },
  {
    keys: ['诊断表格'],
    description: '诊断表格中部分字段必填',
    enable: true,
    ruleKey: 'icdeTableRules',
  },
  {
    keys: ['诊断表格'],
    description: '诊断表格中必须存在医保主诊',
    enable: true,
    ruleKey: 'icdeTableIsMainRequiredRules',
  },
  {
    keys: ['诊断表格 医保编码'],
    description: '诊断表格中医保编码必填',
    enable: false,
    ruleKey: 'icdeTableInsuranceRules',
  },
  {
    keys: ['手术表格'],
    description: '手术表格中部分字段必填',
    enable: true,
    ruleKey: 'operationTableRules',
  },
  {
    keys: ['手术表格'],
    description: '手术表格中必须存在医保主手术',
    enable: true,
    ruleKey: 'operationTableIsMainRequiredRules',
  },
  {
    keys: ['手术表格 医保编码'],
    description: '手术表格中医保编码必填',
    enable: false,
    ruleKey: 'operationTableInsuranceRules',
  },
  // {
  //   keys: ['手术表格'],
  //   description: '手术表格部分其他预定义规则',
  //   enable: false,
  //   ruleKey: 'operationTableExtraRules',
  // },
  {
    keys: ['年龄错误'],
    description: '年龄计算错误',
    enable: false,
    ruleKey: 'patAgeAIORules',
  },
];

// TODO 规则
export const checkErrorOrNot = (errorKeys: string[], currentKey: string) => {
  return errorKeys?.includes(currentKey);
};

const requiredRules = async (form: any, formFieldValues: any) => {
  let notFilledRequiredKeys = [];
  let requiredKeys: string[] = form.getFieldValue('requiredKeys') ?? [];
  // requiredKeys?.forEach((key) => {
  requiredKeys?.forEach((key) => {
    if (isEmptyValues(formFieldValues?.[key])) {
      notFilledRequiredKeys.push(key);
    }
  });

  return notFilledRequiredKeys;
};

export const getRequiredKeys = (dmrLayout: any, dmrHeaderLayout: any) => {
  let requiredKeys: string[] = ['OutType', 'MedfeeSumamt', 'SelfpayAmt'];
  let largeLayouts = [
    ...(dmrLayout?.['lg'] ?? []),
    ...(dmrHeaderLayout?.['lg'] ?? []),
  ];
  if (largeLayouts) {
    let requiredLayoutItems =
      largeLayouts?.filter((item) => item?.data?.required === true) ?? [];
    requiredLayoutItems?.forEach((item) => {
      // 此处用component 来做区分 很大一部分东西 formKey 是自定义的
      switch (item?.data?.component) {
        case 'IcdeSelect': {
          if (item?.data?.props?.selectFormKey) {
            requiredKeys.push(item?.data?.props?.selectFormKey);
          }
          break;
        }
        case 'ProvinceSeparateSelector': {
          if (item?.data?.props?.itemFormKeys) {
            requiredKeys.push(...(item?.data?.props?.itemFormKeys ?? []));
          }
          break;
        }
        case 'TimeRangeStats': {
          if (item?.data?.props?.items) {
            item?.data?.props?.items?.forEach((item) => {
              requiredKeys.push(...(item?.formKeys ?? []));
            });
          }
          break;
        }
        default:
          if (item?.data?.props?.formKey) {
            requiredKeys.push(item?.data?.props?.formKey);
          } else {
            requiredKeys.push(item?.data?.key);
          }
          break;
      }
    });
  }

  let extraRequiredKeys = extraRequiredKeysProcessor(largeLayouts);

  return requiredKeys.concat(extraRequiredKeys);
};

const basicRules = [requiredRules, valueDependencyRules];
const allRules = [
  { key: 'bornDateRules', method: bornDateRules },
  { key: 'babyMonthDayRules', method: babyMonthDayRules },
  { key: 'babyBwRules', method: babyBwRules },
  { key: 'babyIwRules', method: babyIwRules },
  { key: 'idCardRules', method: idCardRules },
  { key: 'inHospOutHospDateRules', method: inHospOutHospDateRules },
  { key: 'outTypeRules', method: outTypeRules },
  { key: 'allergyRules', method: allergyRules },
  { key: 'unconscious', method: unconscious },
  { key: 'returnPlanRules', method: returnPlanRules },
  { key: 'feeRules', method: feeRules },

  { key: 'icdeTableRules', method: icdeTableRules },
  {
    key: 'icdeTableIsMainRequiredRules',
    method: icdeTableIsMainRequiredRules,
    defaultEnabled: true,
  },
  { key: 'icdeTableInsuranceRules', method: icdeTableInsuranceRules },
  { key: 'operationTableRules', method: operationTableRules },
  {
    key: 'operationTableIsMainRequiredRules',
    method: operationTableIsMainRequiredRules,
    defaultEnabled: true,
  },
  { key: 'operationTableInsuranceRules', method: operationTableInsuranceRules },
  // { key: 'operationTableExtraRules', method: operationTableExtraRules },
  { key: 'patAgeAIORules', method: patAgeAIORules },
];

export const formValidation = async (form: any) => {
  let errorKeys = [];

  for (let method of basicRules) {
    let validationResult = await method(form, form.getFieldsValue());
    if (validationResult?.length > 0) {
      errorKeys.push(...validationResult);
    }
  }

  if (errorKeys?.length > 0) {
    showNotification(
      errorKeysToDescription('请检查是否有数据未填写', errorKeys),
    );
  }

  // 当所有必填字段填写之后再 检查特定规则
  if (errorKeys?.length === 0) {
    // 实时获取rules是否有效
    let enabledRules = undefined;
    let ruleConfig = form.getFieldValue('preCheckRules');
    if (!isEmptyValues(ruleConfig)) {
      enabledRules = [];
      Object.keys(ruleConfig).forEach((key) => {
        if (ruleConfig?.[key] === true) {
          enabledRules.push(allRules?.find((item) => item?.key === key));
        }
      });
    }

    // 默认开启的 rules 主要用于 医保主手术 情况
    let defaultEnableRules = allRules?.filter(
      (item) => item?.defaultEnabled === true,
    );
    if (!isEmptyValues(defaultEnableRules)) {
      enabledRules = enabledRules ?? [];
      defaultEnableRules?.forEach((ruleItem) => {
        if (isEmptyValues(ruleConfig?.[ruleItem?.key])) {
          // 当且仅当 ruleConfig 中没有 然后还是defaultEnable的时候
          enabledRules.push(ruleItem);
        }
      });
    }

    (enabledRules ?? allRules).forEach((methodItem) => {
      if (methodItem?.method !== undefined) {
        let validationResult = methodItem?.method(form, form.getFieldsValue());
        if (validationResult?.length > 0) {
          errorKeys.push(...validationResult);
        }
      }
    });
  }

  return errorKeys;
};

export const errorKeysToDescription = (description: string, keys: string[]) => {
  // 通过keys 搜索 标记文案
  let labels = searchLabelByKeys(keys);

  let customDescription: string | ReactNode = description;
  if (labels?.length > 0) {
    customDescription = buildNotificationDescription(description, labels);
  }

  return customDescription;
};

const extraRequiredKeysProcessor = (layouts: any[]) => {
  let extraRequiredKeys = [];

  // 省市区 独立的 requireKeys 处理
  let provinceSelectors = layouts?.filter(
    (item) =>
      item?.data?.component === 'ProvinceSeparateSelector' &&
      item?.data?.required !== true,
  );
  provinceSelectors?.forEach((item) => {
    if (item?.data?.props?.itemFormKeysRequires) {
      item?.data?.props?.itemFormKeysRequires?.forEach((value, index) => {
        if (value === true) {
          if (item?.data?.props?.itemFormKeys?.at(index)) {
            extraRequiredKeys.push(item?.data?.props?.itemFormKeys?.at(index));
          }
        }
      });
    }
  });

  return extraRequiredKeys;
};
