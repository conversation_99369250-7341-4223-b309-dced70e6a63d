import './index.less';
import { Badge, message, Tabs, Form } from 'antd';
import { reviewHeaderErrorProgressItems } from '@/pages/review/components/header';
import React, { useEffect, useRef, useState } from 'react';
import {
  AssignmentItem,
  BatchItem,
  BatchMasterSysItem,
  TaskStatusSummary,
} from '@/pages/review/interface';
import { useModel, useRequest } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useUpdateEffect } from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { RevieweeType } from '@/pages/review/constants';
import ReviewPeriodSelect from '@/pages/review/components/period-select';
import ScoreCommentDrawerContainer from '@/pages/review/components/score-comment';
import { QualityExamineStatus } from '@/pages/review/components/score-comment/score/constant';
import ReviewManagementInfo from '@/pages/review/management/review-info';
import ReviewPersonSummary from '@/pages/review/management/person-summary';
import { sortByBatchDate } from '@/pages/review/utils';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import DmrReviewBatchCreateWithEmployeeModal from '@/pages/review/components/batch-create';
import DmrReviewBatchCreateOnlyDateModal from '@/pages/review/components/batch-create/date';
import { BatchMasterItem } from '@/pages/review/interface';
import { useRouteProps } from '@uni/commons/src/route-context';
import ExportIconBtn from '@uni/components/src/backend-export';
import Stats from '../components/stats';

const ReviewManagement = () => {
  const [form] = Form.useForm();
  const taskTableRef = useRef(null);

  const dmrPreviewContainerRef = useRef(null);

  const dmrReviewerContainerRef = useRef(null);

  const dmrReviewInfoContainerRef = useRef(null);

  const batchSelectorRef = useRef(null);
  const batchCreateContainerRef = useRef(null);
  const batchCreateDateContainerRef = useRef(null);

  const batchReviewInfoTitleContainerRef = useRef(null);

  const [searchParams, setSearchParams] = useState<any>({});
  const [selectedStatItem, setSelectedStatItem] = useState<string>('');
  const [batchMasters, setBatchMasters] = useState<BatchMasterItem[]>([]);

  const [taskSummaryInfo, setTaskSummaryInfo] =
    useState<TaskStatusSummary>(undefined);

  const [reviewerCodes, setReviewerCodes] = useState([]);

  const [assignments, setAssignments] = useState<AssignmentItem[]>([]);

  const [activeKey, setActiveKey] = useState('REVIEW_INFO');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [currentSysMaster, setCurrentSysMaster] =
    useState<BatchMasterSysItem>(undefined);

  const { examineMasterId } = useRouteProps();

  React.useImperativeHandle(dmrReviewerContainerRef, () => {
    return {
      cancelReview: (record: any, index: number) => {
        cancelReview(record);
      },
    };
  });

  // const [backPagination, setBackPagination] = useState({
  //   current: 1,
  //   total: 0,
  //   pageSize: 10,
  //   pageSizeOptions: ['10', '20'],
  //   hideOnSinglePage: false,
  // });

  const currentBatchMaster =
    batchMasters?.find(
      (item) => item?.MasterId === searchParams?.batchInfo?.MasterId,
    ) ?? batchMasters?.at(0);

  useEffect(() => {
    latestBatchInfoReq();
    // if (isEmptyValues(batchId)) {
    //   if (isEmptyValues(globalState?.searchParams?.BatchItem)) {
    //     latestBatchInfoReq();
    //   } else {
    //     setBatchId(globalState?.searchParams?.BatchItem?.BatchId);
    //     setBatchInfo(globalState?.searchParams?.BatchItem);
    //   }
    // }
  }, [examineMasterId]);

  useEffect(() => {
    if (!isEmptyValues(searchParams?.batchInfo)) {
      taskStatusSummaryReq(reviewerCodes);
    }
  }, [searchParams, reviewerCodes]);

  useUpdateEffect(() => {
    if (!isEmptyValues(searchParams?.BatchId)) {
      taskSelectorDataReq();
    }
  }, [searchParams?.BatchId]);

  useEffect(() => {
    if (!isEmptyValues(examineMasterId)) {
      getBatchMasterItemReq();
    }
  }, [examineMasterId]);

  useEffect(() => {
    getBatchMasterReq();
  }, []);

  useEffect(() => {
    latestBatchInfoReq();
  }, []);

  const { loading: getBatchMasterItemLoading, run: getBatchMasterItemReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Sys/QualityExamineSys/GetQualityExamineSys',
          {
            method: 'GET',
            params: {
              MasterId: examineMasterId,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchMasterSysItem>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setCurrentSysMaster(response?.data);
          } else {
            setCurrentSysMaster(undefined);
          }
        },
      },
    );

  const getUserEmployeeCode = () => {
    // return '0007';
    // TODO 不写死..

    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
    return userInfo?.EmployeeCode;
  };

  const cancelReview = async (taskItem: any) => {
    if (taskItem?.Status === QualityExamineStatus.Pending) {
      message.warning('待审核状态不能被重置');
      return;
    }

    if (taskItem?.Status === QualityExamineStatus.Reviewing) {
      message.warning('审核中状态不能被重置');
      return;
    }

    if (taskItem?.Status === QualityExamineStatus.Init) {
      message.warning('状态错误，请检查');
      return;
    }

    let cancelReviewResponse: RespVO<any> = await uniCommonService(
      'Api/Dmr/DmrCardQualityExamine/ResetTask',
      {
        method: 'POST',
        data: {
          TaskId: taskItem?.TaskId,
        },
      },
    );

    if (
      cancelReviewResponse?.code === 0 &&
      cancelReviewResponse?.statusCode === 200
    ) {
      taskTableRef?.current?.freshQueryTable();
    }
  };

  const { loading: latestBatchInfoLoading, run: latestBatchInfoReq } =
    useRequest(
      () => {
        let data = {};
        if (!isEmptyValues(examineMasterId)) {
          data['MasterId'] = examineMasterId;
        }

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchItem[]>) => {
          let latestBatch = response?.data?.sort(sortByBatchDate)?.at(0);
          let defaultBatchItem = globalState?.searchParams?.BatchItem;
          const findBatchItem = response?.data?.find(
            (item) =>
              item.BatchId === defaultBatchItem?.BatchId &&
              item.MasterId === defaultBatchItem?.MasterId,
          );
          if (findBatchItem) {
            setSearchParams({
              ...searchParams,
              BatchId: defaultBatchItem.BatchId,
              MasterId: defaultBatchItem?.MasterId,
              BatchItem: globalState?.searchParams?.BatchItem,
              batchInfo: globalState?.searchParams?.BatchItem,
            });
          } else {
            setSearchParams({
              ...searchParams,
              BatchId: latestBatch?.BatchId,
              MasterId: latestBatch?.MasterId,
              BatchItem: latestBatch,
              batchInfo: latestBatch,
            });
          }
        },
      },
    );

  const { loading: taskStatusSummaryLoading, run: taskStatusSummaryReq } =
    useRequest(
      (reviewerCodes: string[]) => {
        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary',
          {
            method: 'POST',
            data: {
              ...searchParams,
              // ...(selectedStatItem
              //   ? { [`${selectedStatItem}Flag`]: true }
              //   : {}),
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TaskStatusSummary>) => {
          let summaryInfo = response?.data;
          summaryInfo['CommentErrorTotal'] =
            reviewHeaderErrorProgressItems?.reduce(
              (accumulator, item) =>
                accumulator + (summaryInfo?.[item?.valueKey] ?? 0),
              0,
            );
          setTaskSummaryInfo(summaryInfo ?? {});
        },
      },
    );

  const { loading: taskSelectorDataLoading, run: taskSelectorDataReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetAssignments',
          {
            method: 'POST',
            data: {
              ...searchParams,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<AssignmentItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setAssignments(response?.data);
            let basicArgValues = {};
            Object.keys(RevieweeType).forEach((key) => {
              basicArgValues[RevieweeType[key]] = response?.data
                ?.filter((item) => item.RevieweeType === RevieweeType[key])
                ?.map((item) => {
                  return item?.RevieweeCode;
                });
            });
          } else {
            setAssignments([]);
          }
        },
      },
    );

  const { loading: getBatchMasterLoading, run: getBatchMasterReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/QualityExamineSys/GetQualityExamineSettingMasters',
        {
          method: 'GET',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setBatchMasters(response?.data);
        } else {
          setBatchMasters([]);
        }
      },
    },
  );

  const tabItems = [
    {
      key: 'PERSON_SUMMARY',
      label: '统计',
      children: (
        <ReviewPersonSummary
          activeKey={activeKey}
          searchParams={searchParams}
          sysMasterItem={currentSysMaster}
          onTableRowSelect={(record: any, type: string) => {
            switch (type) {
              case 'Reviewer':
                setActiveKey('REVIEW_INFO');
                setSearchParams({
                  ...searchParams,
                  ReviewerCodes: [record.ReviewerCode],
                  Coder: undefined,
                  [`${currentBatchMaster?.RevieweeType}s`]: undefined,
                });
                setTimeout(() => {
                  dmrReviewInfoContainerRef?.current?.onRowSelect(
                    [record?.ReviewerCode],
                    'ReviewerCodes',
                  );
                }, 0);
                break;
              case 'Auditee':
                setActiveKey('REVIEW_INFO');
                setSearchParams({
                  ...searchParams,
                  ReviewerCodes: undefined,
                  Coder: undefined,
                  [`${record?.RevieweeType}s`]: [record.RevieweeCode],
                });
                setTimeout(() => {
                  dmrReviewInfoContainerRef?.current?.onRowSelect(
                    [record?.RevieweeCode],
                    'DoctorCodes',
                  );
                }, 0);
                break;
              case 'Coder':
                setActiveKey('REVIEW_INFO');
                setSearchParams({
                  ...searchParams,
                  Coder: record.Coder,
                  ReviewerCodes: undefined,
                  [`${currentBatchMaster?.RevieweeType}s`]: undefined,
                });
                setTimeout(() => {
                  dmrReviewInfoContainerRef?.current?.onRowSelect(
                    record?.Coder,
                    'Coder',
                  );
                }, 0);
                break;
              default:
                break;
            }
          }}
        />
      ),
    },
    {
      key: 'REVIEW_INFO',
      label: (
        <ReviewManagementInfoTitle
          containerRef={batchReviewInfoTitleContainerRef}
        />
      ),
      children: (
        <ReviewManagementInfo
          tabKey={'REVIEW_INFO'}
          taskTableRef={taskTableRef}
          activeKey={activeKey}
          containerRef={dmrReviewInfoContainerRef}
          selectedStatItem={selectedStatItem}
          batchId={searchParams?.BatchId}
          batchInfo={searchParams?.batchInfo}
          searchParams={searchParams}
          assignments={assignments}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          dmrReviewerContainerRef={dmrReviewerContainerRef}
          tabTitleContainerRef={batchReviewInfoTitleContainerRef}
        />
      ),
    },
    {
      key: 'CONTROVERSIAL_TASK',
      label: '争议病案管理',
      children: (
        <ReviewManagementInfo
          tabKey={'CONTROVERSIAL_TASK'}
          extraConfig={{
            interfaceUrl: 'Api/Dmr/DmrCardQualityExamine/GetControversialTasks',
          }}
          taskTableRef={taskTableRef}
          activeKey={activeKey}
          containerRef={dmrReviewInfoContainerRef}
          selectedStatItem={selectedStatItem}
          batchId={searchParams?.BatchId}
          batchInfo={searchParams?.batchInfo}
          searchParams={searchParams}
          assignments={assignments}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          dmrReviewerContainerRef={dmrReviewerContainerRef}
          tabTitleContainerRef={batchReviewInfoTitleContainerRef}
        />
      ),
    },
  ];
  return (
    <div id={'management-container'} className={'management-container'}>
      {/*<ReviewManagementSearch*/}
      {/*  assignments={assignments}*/}
      {/*  onSearchClick={(formValues: any) => {*/}
      {/*    console.log('FormValues', formValues);*/}
      {/*    setReviewerCodes(cloneDeep(formValues?.['reviewerCodes'] ?? []));*/}
      {/*  }}*/}
      {/*/>*/}

      <ReviewPeriodSelect
        containerRef={batchSelectorRef}
        batchCreateUpdate={true}
        batchExport={true}
        onBatchUpdateClick={(type: string) => {
          let customData = {};
          if (type === 'ADJUST') {
            customData['title'] = '调整当前评审计划';
            customData['okText'] = '再次分配任务';
            customData['errorText'] = '再次分配任务出错，请检查';
            customData['batchId'] = searchParams?.BatchId;
            customData['batchInfo'] = searchParams?.batchInfo;
          }
          batchCreateContainerRef?.current?.setReviewCreateStatus({
            status: true,
            type: type,
            ...customData,
          });
        }}
        form={form}
        isManagement={true}
        currentBatchMaster={currentBatchMaster}
        searchParams={searchParams}
        setSearchParams={setSearchParams}
        assignments={assignments}
        batchDelete={true}
        onBatchDeleteSuccess={() => {
          // 刷一下
          latestBatchInfoReq();
        }}
      />
      <div className="review-person-summary-container">
        <Stats
          selectedStatItem={selectedStatItem}
          setSelectedStatItem={setSelectedStatItem}
          summaryInfo={taskSummaryInfo}
          loading={taskStatusSummaryLoading}
        />
      </div>

      <Tabs
        items={tabItems?.filter((item) => {
          if (item?.key === 'CONTROVERSIAL_TASK') {
            return false;
            // return currentSysMaster?.ReviewSetting?.EnableResolve === true;
          }

          return true;
        })}
        activeKey={activeKey}
        onChange={(key) => {
          // TODO 设定默认值 ReSubmitted
          setActiveKey(key);
        }}
        tabBarExtraContent={{
          right:
            activeKey === 'REVIEW_INFO' ? (
              <>
                <ExportIconBtn
                  getExternalExportConfig={() => {
                    let taskExtraParams =
                      dmrReviewInfoContainerRef?.current?.getTaskExtraParams();

                    if (selectedStatItem) {
                      taskExtraParams[`${selectedStatItem}Flag`] = true;
                    }
                    return {
                      isBackend: true,
                      backendObj: {
                        url: 'Api/Dmr/DmrCardQualityExamine/ExportGetTasks',
                        method: 'POST',
                        data: {
                          ...taskExtraParams,
                          ...searchParams,
                        },
                        fileName: '评审结果',
                      },
                    };
                  }}
                />
                <TableColumnEditButton
                  columnInterfaceUrl={'Api/Dmr/DmrCardQualityExamine/GetTasks'}
                  onTableRowSaveSuccess={(columns) => {
                    taskTableRef?.current?.setTaskTableColumns(columns);
                  }}
                />
              </>
            ) : null,
        }}
      />

      <ScoreCommentDrawerContainer
        tableReadonly={false}
        dmrReadonly={false}
        drawerContainerRef={dmrPreviewContainerRef}
        onScoreCommentReviewEnd={(taskId: number) => {
          taskStatusSummaryReq(reviewerCodes);
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentClose={(taskId: number) => {
          taskStatusSummaryReq(reviewerCodes);
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentTableRefresh={(taskId: number) => {
          taskStatusSummaryReq(reviewerCodes);
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        getContainer={() => {
          return document.getElementById('management-container');
        }}
      />

      <DmrReviewBatchCreateWithEmployeeModal
        batchCreateContainerRef={batchCreateContainerRef}
        batchId={searchParams?.BatchId}
        batchInfo={searchParams?.batchInfo}
        onBatchCreateSuccess={(batchInfo: any) => {
          setSearchParams({
            ...searchParams,
            BatchId: batchInfo.BatchId,
            batchInfo,
          });
          batchCreateContainerRef?.current?.setReviewCreateStatus({
            status: false,
          });
          batchSelectorRef?.current?.refreshBatches();
          message.success('评审计划创建成功');
          setTimeout(() => {
            taskStatusSummaryReq(reviewerCodes);
            taskTableRef?.current?.freshQueryTable();
          }, 0);
        }}
      />

      <DmrReviewBatchCreateOnlyDateModal
        batchCreateDateContainerRef={batchCreateDateContainerRef}
        onBatchCreateSuccess={(batchInfo: any) => {
          setSearchParams({
            ...searchParams,
            BatchId: batchInfo.BatchId,
            batchInfo,
          });
          batchCreateContainerRef?.current?.setBatchCreateDateStatus({
            status: false,
          });
          batchSelectorRef?.current?.refreshBatches();
          message.success('创建成功');
          setTimeout(() => {
            taskStatusSummaryReq(reviewerCodes);
            taskTableRef?.current?.freshQueryTable();
          }, 0);
        }}
      />
    </div>
  );
};

interface ReviewManagementInfoTitleProps {
  containerRef: any;
}

const ReviewManagementInfoTitle = (props: ReviewManagementInfoTitleProps) => {
  const [dataCount, setDataCount] = React.useState(0);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      setTotalCount: (count: number) => {
        setDataCount(count);
      },
    };
  });

  return (
    <span className={'title-label'}>
      审核病案明细
      <Badge
        overflowCount={1000000}
        count={dataCount}
        size={'default'}
        style={{
          backgroundColor: '#E6F7FF',
          color: '#1464f8',
          marginLeft: 10,
        }}
      />
    </span>
  );
};

export default ReviewManagement;
