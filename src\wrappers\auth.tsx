// 如果是分离项目的话 登录就用这个做判断

import React, { useState, useEffect } from 'react';
import { Redirect, useModel, useAccess, useLocation } from 'umi';
import { message } from 'antd';
import Loading from '@/loading';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import { DynamicMenuItem } from '@/layouts/root/dynamic-menu';

const enableDynamicMenuItem =
  (window as any).externalConfig?.['common']?.enableDynamicMenuItem ?? false;

const dynamicMenuItemProcess = async (pathname: string) => {
  let dynamicMenuItemsResponse: RespVO<DynamicMenuItem[]> =
    await uniCommonService('Api/Sys/ClientKitSys/GetDynamicMenuItems', {
      method: 'POST',
    });

  if (
    dynamicMenuItemsResponse?.code === 0 &&
    dynamicMenuItemsResponse?.statusCode === 200
  ) {
    if (!isEmptyValues(dynamicMenuItemsResponse?.data)) {
      for (let menuItem of dynamicMenuItemsResponse?.data) {
        // if (pathname?.includes(menuItem?.MenuItemUrl)) {
        //   return true;
        // }

        const route = `${menuItem?.NavUrl}/${menuItem?.MenuItemUrl}`;
        if (route === pathname) {
          return true;
        }
      }
    }
  }

  return false;
};

export default (props: any) => {
  const { initialState }: any = useModel('@@initialState');

  const authCheck = !!initialState?.token; // 只判断有没有登录

  const access = useAccess();
  const [canGo, setCanGo] = useState<any>(null); // 判断有没有权限访问
  useAsyncEffect(async () => {
    if (authCheck) {
      let result = false;

      // 动态菜单
      if (enableDynamicMenuItem === true) {
        result = await dynamicMenuItemProcess(props?.location?.pathname);
      }

      // 权限判定 highlight 处理
      if (props?.location?.pathname?.includes('/report/highlight')) {
        // 当是report/highlight
        if (
          access[props.location.pathname] !== undefined &&
          access[props.location.pathname] !== null
        ) {
          // 如果有自定义的 按照自定义的计算
          result = access[props.location.pathname] === true;
        } else {
          // 没有自定义的 按照 /report/hospital 计算
          result = access['/statsAnalysis/report/hospital'] === true;
        }
      } else if (props?.location?.pathname?.includes('/analysis/highlight')) {
        // 当是 analysis/highlight
        if (
          access[props.location.pathname] !== undefined &&
          access[props.location.pathname] !== null
        ) {
          // 如果有自定义的 按照自定义的计算
          result = access[props.location.pathname] === true;
        } else {
          // 没有自定义的 按照 /report/hospital 计算
          result = access['/chs/analysis/report'] === true;
        }
      } else if (props?.location?.pathname?.includes('/dip/highlight')) {
        // 当是 dip/highlight
        if (
          access[props.location.pathname] !== undefined &&
          access[props.location.pathname] !== null
        ) {
          // 如果有自定义的 按照自定义的计算
          result = access[props.location.pathname] === true;
        } else {
          // 没有自定义的 按照 /report/hospital 计算
          result = access['/chs/dip/report'] === true;
        }
      } else if (props?.location?.pathname?.includes('/dmr/examine')) {
        // 评审 /dmr/examine
        if (
          access[props.location.pathname] !== undefined &&
          access[props.location.pathname] !== null
        ) {
          // 如果有自定义的 按照自定义的计算
          result = access[props.location.pathname] === true;
        } else {
          // 没有自定义的 按照 /dmr/examine 计算
          result = access['/dmr/examine'] === true;
        }
      } else {
        if (access[props.location.pathname]) {
          result = true;
        }
      }

      setCanGo(result);
    }
  }, [authCheck]);

  if (authCheck === null || (authCheck && canGo === null)) {
    return <Loading />;
  }
  return authCheck ? (
    canGo ? (
      props.children
    ) : (
      <Redirect to="/error?code=403" />
    )
  ) : (
    <Redirect to="/login" />
  );
};
