import React, { useEffect, useRef, useState } from 'react';
import { Button, Dropdown, Form, Input, Select, Spin, Tooltip } from 'antd';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { IcdeOperItem, IcdeOperResp } from '@/pages/dmr/network/interfaces';
import { useDebounceFn } from 'ahooks';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  getSelectorDropdownContainerNode,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
} from '@uni/grid/src/utils';
import { UniAntdSelect } from '@uni/components/src';
import { icdeExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  FormTableItemBaseProps,
  useDmrDragEditOnlyTableContext,
} from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import cloneDeep from 'lodash/cloneDeep';
import EmptyWrapper from '@uni/grid/src/components/empty-wrapper';
import { IcdeOperKeyboardFriendlyDropdown } from '@uni/grid/src/components/dmr-select/keyboard';
import isEqual from 'lodash/isEqual';

const { Option } = Select;

// 关闭所有其他打开的icde-select下拉框
const closeOtherIcdeSelectDropdowns = (currentComponentId: string) => {
  // 查找所有打开的下拉框容器
  const openDropdowns = document.querySelectorAll(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)',
  );

  openDropdowns.forEach((dropdown) => {
    // 查找对应的select元素
    const selectElements = document.querySelectorAll('[id*="IcdeSelect"]');
    selectElements.forEach((selectElement: any) => {
      // 如果不是当前组件，尝试触发blur事件来关闭下拉框
      if (selectElement.id !== `formItem#${currentComponentId}#IcdeSelect`) {
        try {
          // 尝试触发blur事件
          selectElement.blur?.();
          // 或者尝试按ESC键
          const escEvent = new KeyboardEvent('keydown', {
            key: 'Escape',
            keyCode: 27,
            which: 27,
          });
          selectElement.dispatchEvent?.(escEvent);
        } catch (e) {
          // 忽略错误
        }
      }
    });
  });
};

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['dmr']?.inputFocusNotSelectAll ?? false;

const leftRightSwitchPage =
  (window as any).externalConfig?.['dmr']?.leftRightSwitchPage ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

const enableKeyboardFriendlySelect =
  (window as any).externalConfig?.['dmr']?.enableKeyboardFriendlySelect ??
  false;

export interface IcdeFormKeysItem {
  [key: string]: string;
}

export interface IcdeSelectProps extends FormTableItemBaseProps {
  form?: any;

  dataIndex?: string;

  paramKey?: string; // 没有特别用处 可以理解为该param的特殊key 来做内部逻辑区分

  parentId?: string;

  formKeys?: IcdeFormKeysItem; // 暂定

  componentId: string;

  selectFormKey?: string;
  itemKey?: string;

  getPopupContainer?: (trigger) => HTMLElement;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  interfaceUrl?: string;
  disabled?: boolean;

  // 为了兼容 置灰的手术 / 诊断用于立即搜索
  instantSelect?: boolean;
  dropdownAlign?: any;

  onChangeValueProcessor?: (value: any) => string;

  recordId?: string;

  icdeSelectType?: string;
  icdeTcmCategory?: string;

  tableId?: string;

  numberSelectItem?: boolean;

  codeColumnWidth?: number | string;
}

const maxResultCount = 100;
const IcdeSelect = (props: IcdeSelectProps) => {
  const dmrTableContext = useDmrDragEditOnlyTableContext();

  const icdeInputRef = useRef(null);

  const selectorContainerRef =
    dmrTableContext?.columnItemRefMapGetter?.(
      `${props?.dataIndex}~${props?.tableId}`,
    ) ?? React.createRef<any>();
  dmrTableContext?.columnItemRefMapSetter?.(
    `${props?.dataIndex}~${props?.tableId}`,
    selectorContainerRef,
  );

  const [dataSource, setDataSource] = useState<IcdeOperItem[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState(undefined);
  const [keywordChange, setKeywordChange] = useState(false);

  const [hasSearched, setHasSearched] = useState(false);

  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  //  keyboard friendly
  const [optionOpen, setOptionOpen] = useState(false);
  const listRef = React.useRef(null);

  // 为IcdeDamgsItem添加回跳状态管理
  const [backJumpInfo, setBackJumpInfo] = useState<any>(null);

  useEffect(() => {
    if (props?.selectFormKey) {
      Emitter.on(getDeletePressEventKey(props?.selectFormKey), (key) => {
        if (key.includes(props?.selectFormKey)) {
          // 表示就是当前组件的formKey
          onIcdeSelectClear();
        }
      });
    }

    // 只有当itemKey为IcdeDamgsItem时才处理回跳相关逻辑
    if (props?.itemKey === 'IcdeDamgsItem') {
      // 监听损伤中毒字段的专用跳转准备事件
      Emitter.on('DMR_ICDE_DAMGS_JUMP_PREPARE', (jumpInfo) => {
        console.log('IcdeDamgsItem收到跳转准备事件', jumpInfo);
        setBackJumpInfo(jumpInfo);
      });

      // 监听IcdeDamgsItem专用的取消事件
      Emitter.on('DMR_ICDE_DAMGS_JUMP_CANCEL', (cancelInfo) => {
        console.log('IcdeDamgsItem 收到取消跳转事件，清除回跳状态', cancelInfo);
        setBackJumpInfo(null);
      });
    }

    return () => {
      if (props?.selectFormKey) {
        Emitter.off(getDeletePressEventKey(props?.selectFormKey));
      }

      if (props?.itemKey === 'IcdeDamgsItem') {
        Emitter.off('DMR_ICDE_DAMGS_JUMP_PREPARE');
        Emitter.off('DMR_ICDE_DAMGS_JUMP_CANCEL');
      }
    };
  }, [props?.selectFormKey, props?.itemKey]);
  // 获取 后端 诊断数据源 start
  const getDiagnosisDataSource = async (searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    setLoading(true);
    let data = {
      Keyword: searchKeyword?.trim(),
      HasInsurCompare: true,
      HasHqmsCompare: true,
      HasDrgsCompare: true,
    };

    data = searchArgsProcessor(data, props?.icdeSelectType);

    let diagnosisDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(props?.interfaceUrl ?? 'Api/Dmr/DmrSearch/Icde', {
        params: data,
      });

    // 中医诊断 这里追加isTcm判定 用于不同的item显示
    let isTcm = props?.icdeSelectType?.toLowerCase()?.includes('istcm');

    if (diagnosisDataSourceResponse?.code === 0) {
      if (diagnosisDataSourceResponse?.statusCode === 200) {
        setDataSource(
          diagnosisDataSourceResponse?.data?.Data?.map((item) => {
            if (isTcm) {
              return {
                ...item,
                key: item?.Code,
                value: item?.TcmIcdeCode,
                title: item?.TcmIcdeCode,
                label: `${item?.TcmIcdeCode} ${item?.Name}`,
                IcdeCode: item?.TcmIcdeCode,
                IcdeName: item?.Name,
              };
            }

            return {
              ...item,
              key: item?.Code,
              value: item?.Code,
              title: item?.Code,
              label: `${item?.Code} ${item?.Name}`,
              IcdeCode: item?.Code,
              IcdeName: item?.Name,
            };
          }) || [],
        );
      }
    }

    setLoading(false);
  };

  const { run: getDiagnosisDataSourceWithDebounce } = useDebounceFn(
    (keyword) => {
      getDiagnosisDataSource(keyword);
    },
    {
      wait: 200,
    },
  );

  // 额外处理病理的 诊断输入类别： Patho(病理诊断) IsPatho=true MCode(M码) IsMorphology=true
  const searchArgsProcessor = (data, icdeSelectType) => {
    // 病理诊断 && 是表格内部select的情况 单独处理
    if (props?.paramKey === 'pathologicalDiagnosis' && props?.tableId) {
      if (props?.form) {
        let pathoType = props?.form?.getFieldValue([
          props?.recordId,
          'PathoType',
        ]);
        switch (pathoType) {
          case 'MCode':
            data['IsMorphology'] = true;
            break;
          case 'Patho':
            data['IsPatho'] = true;
            break;
          default:
            data[icdeSelectType] = true;
            break;
        }
      }
    } else {
      if (icdeSelectType?.toLowerCase()?.includes('istcm')) {
        // 中医可能出现2个 所以要 多一套这个
        data['IsTcm'] = true;
        if (
          icdeSelectType?.toLowerCase()?.includes('istcmmain') ||
          icdeSelectType?.toLowerCase()?.includes('istcmadm')
        ) {
          data['TcmIcdeCategory'] = 'A';
        } else {
          data['TcmIcdeCategory'] = props?.icdeTcmCategory ?? 'B';
        }
      } else {
        data[icdeSelectType] = true;
      }
    }

    return data;
  };
  // 获取 后端 诊断数据源 end

  const onIcdePopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 100;
    console.error('nearly end', scrollNearlyEnd);
    if (scrollNearlyEnd && !loading) {
      getDiagnosisDataSource(keyword);
    }
  };

  const onIcdeSelectClear = () => {
    let fieldsValue = {};
    if (props?.formKeys) {
      // 第一个是文本 第二个是编码
      Object.keys(props?.formKeys).forEach((key) => {
        fieldsValue[key] = '';
      });
    }
    if (props?.form) {
      if (props?.itemKey) {
        fieldsValue[props?.itemKey] = {};
      }
      if (props?.recordId) {
        Object.keys(fieldsValue)?.forEach((key) => {
          props?.form.setFieldValue([props?.recordId, key], fieldsValue[key]);
        });
      } else {
        props?.form.setFieldsValue(fieldsValue);
      }
    }

    // 清一下dataSource  keyword offset total
    setDataSource([]);
    setHasSearched(false);
    setKeyword(undefined);
    setOffset(0);
    setRecordTotal(0);

    setOptionOpen(false);

    listRef?.current?.clear();

    if (props?.onChangeValueProcessor) {
      fieldsValue = props?.onChangeValueProcessor(fieldsValue);
    }

    props?.onChange && props?.onChange(fieldsValue);
  };

  const onIcdeSelect = (value: string, externalDataSource?: any[]) => {
    let fieldsValue = {};
    let currentSelected = (externalDataSource ?? dataSource)?.find(
      (item) => item.IcdeCode === value,
    );
    if (currentSelected) {
      // 处理表单字段映射
      if (props?.formKeys) {
        // 第一个是文本 第二个是编码
        Object.keys(props?.formKeys).forEach((key) => {
          // 微创手术 特殊处理
          if (key === 'IcdeExtra') {
            fieldsValue[key] = Object.keys(icdeExtraMap)?.filter((key) => {
              if (key === 'InsurIsObsolete') {
                return currentSelected?.['IsObsolete'] ?? false;
              } else {
                return currentSelected?.[key] ?? false;
              }
            });
          } else {
            fieldsValue[key] = currentSelected?.[props?.formKeys[key]];
          }
        });
      }
      if (props?.form) {
        if (props?.itemKey) {
          fieldsValue[props?.itemKey] = currentSelected;
        }
        if (props?.recordId) {
          console.log('fieldsValue', fieldsValue);
          let recordFieldValue = cloneDeep(fieldsValue);
          Object.keys(recordFieldValue)?.forEach((key) => {
            props?.form.setFieldValue(
              [props?.recordId, key],
              recordFieldValue[key],
            );
          });
        } else {
          props?.form.setFieldsValue(fieldsValue);
        }
      }
      // 处理通过props传入的值处理器
      if (props?.onChangeValueProcessor) {
        fieldsValue = props?.onChangeValueProcessor(fieldsValue);
      }

      // 去除icde table 下 所有 标记 前提是 和当前的值不相等 当且仅当是 出院诊断表
      if (!isEmptyValues(props?.tableId)) {
        let recordFieldValue = cloneDeep(fieldsValue);
        // 相同的时候还是保留标记
        // 保证 所有 code 相同才能return
        let currentCodes = {
          Code: props?.value,
          InsurCode: recordFieldValue?.InsurCode,
          HqmsCode: recordFieldValue?.HqmsCode,
          WtCode: recordFieldValue?.WtCode,
        };
        let newCodes = {
          Code: value,
          InsurCode: currentSelected?.InsurCode,
          HqmsCode: currentSelected?.HqmsCode,
          WtCode: currentSelected?.WtCode,
        };
        if (isEqual(currentCodes, newCodes)) {
          return;
        }
        let tableItem = document.getElementById(props?.tableId);
        if (!isEmptyValues(tableItem)) {
          tableItem
            ?.querySelectorAll('.table-duplicate-highlight-item')
            ?.forEach((item) => {
              item?.classList.remove('table-duplicate-highlight-item');
            });
        }
      }

      props?.onChange && props?.onChange(fieldsValue);
      // 检查是否需要执行回跳（仅限IcdeDamgsItem）
      if (props?.itemKey === 'IcdeDamgsItem' && backJumpInfo) {
        // 只有当选择的值不为空时才执行回跳
        if (
          (fieldsValue as any)?.IcdeDamgsIcdeCode &&
          (fieldsValue as any)?.IcdeDamgsIcdeCode?.trim() !== ''
        ) {
          // 延迟执行回跳，确保当前输入已处理完成
          setTimeout(() => {
            Emitter.emit('DMR_PATHOLOGY_BACK_JUMP_EXECUTE', {
              ...backJumpInfo,
              targetIndex: 1,
              targetFieldId: 'formItem#MainIcdeCode#1#IcdeSelect',
            });
            setBackJumpInfo(null); // 清除回跳状态
          }, 150);
        }
      }

      setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
      setOffset(0);
      setRecordTotal(0);
      setKeyword(undefined);

      setTimeout(() => {
        icdeInputRef?.current?.focus();
      }, 0);
    }
  };

  useEffect(() => {
    if (dataSource?.length > 0) {
      setTimeout(() => {
        let dropDownElement = document.getElementById(
          'icde-select-dropdown-container',
        );
        if (dropDownElement) {
          let firstLi = dropDownElement?.querySelector('ul li');
          if (firstLi) {
            (firstLi as any)?.focus();
            setTimeout(() => {
              (firstLi as any)?.scrollIntoView({
                block: 'center',
                inline: 'center',
              });
            }, 100);
          }
        }
      }, 0);
    }
  }, [dataSource]);

  const Wrapper = props?.recordId ? EmptyWrapper : Form.Item;

  const extraProps = props?.recordId ? { value: props?.value } : {};

  const icdeKeyboardFriendlyProps = props?.numberSelectItem
    ? {
        dropdownRender: (menu: any) => {
          return (
            <IcdeOperKeyboardFriendlyDropdown
              columnType={'Dmr'}
              type={'Icde'}
              enableKeyboardFriendlySelect={enableKeyboardFriendlySelect}
              listRef={listRef}
              interfaceUrl={
                props?.interfaceUrl ?? 'Api/Dmr/DmrSearch/IcdeReorder'
              }
              value={props?.value}
              onSelectChange={(value, item, dataSources) => {
                onIcdeSelect(value, dataSources);
              }}
              optionOpen={optionOpen}
              setOptionOpen={setOptionOpen}
              searchKeyword={keyword}
              setSearchKeyword={setKeyword}
              instantSelect={props?.instantSelect}
              icdeSelectType={props?.icdeSelectType}
              icdeTcmCategory={props?.icdeTcmCategory}
              codeColumnWidth={props?.codeColumnWidth}
              //  透传 给病理诊断判断用
              paramKey={props?.paramKey}
              form={props?.form}
              tableId={props?.tableId}
              recordId={props?.recordId}
              //  透传 结束
              leftRightSwitchPage={leftRightSwitchPage ?? false}
            />
          );
        },
        open: optionOpen && !isEmptyValues(keyword),
        onDropdownVisibleChange: (visible: boolean) => {
          // if (visible) {
          //   // 当当前下拉框打开时，关闭所有其他的icde-select下拉框
          //   closeOtherIcdeSelectDropdowns(props?.componentId);
          // }
          setOptionOpen(visible);
        },
        onKeyboardNumberSelect: (event: any, index: any) => {
          listRef?.current?.onKeyboardNumberSelect(event, index);
        },
        onKeyboardPageFlip: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
        onPopupScroll: () => {},
        leftRightSwitchPage: leftRightSwitchPage ?? false,
        onLeftRightKeyDownSwitchPage: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
      }
    : {};

  return (
    <div className={'form-content-item-container'}>
      <Tooltip
        open={errorTooltipOpen}
        color={'rgba(235, 87, 87, 0.85)'}
        title={'诊断不存在，请检查后重新选择'}
      >
        <Wrapper className={'icde-table-item'} name={props?.selectFormKey}>
          <UniAntdSelect
            ref={selectorContainerRef}
            id={`formItem#${props?.componentId}#IcdeSelect`}
            className={`select`}
            {...extraProps}
            showSearch
            showAction={props?.instantSelect ? ['focus'] : []}
            showArrow={false}
            allowClear={false}
            disabled={props?.disabled}
            dropdownMatchSelectWidth={false}
            dropdownAlign={props?.dropdownAlign}
            optionLabelProp={'value'}
            getPopupContainer={(trigger) =>
              (props.getPopupContainer && props?.getPopupContainer(trigger)) ||
              getSelectorDropdownContainerNode()
            }
            onInputKeyDown={(event) => {
              if (hasSearched) {
                if (event.key === 'Enter' && dataSource?.length === 0) {
                  setErrorTooltipOpen(true);
                  event.preventDefault();
                  event.stopPropagation();
                }
              }
            }}
            enterSwitch={true}
            contentEditable={!inputFocusNotSelectAll}
            dropdownStyle={props?.dropdownStyle || {}}
            listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
            placeholder={
              props?.selectFormKey
                ? props?.form?.getFieldValue(props?.selectFormKey)
                : '请选择诊断'
            }
            onSearch={(searchKeyword) => {
              if (enableKeyboardFriendlySelect === true) {
                setKeyword(searchKeyword);
                return;
              }

              if (props?.numberSelectItem !== true) {
                setHasSearched(true);
                if (searchKeyword) {
                  getDiagnosisDataSourceWithDebounce(searchKeyword);
                } else {
                  setDataSource([]);
                  setOffset(0);
                  setRecordTotal(0);
                }
              }
              setKeyword(searchKeyword);
            }}
            filterOption={false}
            notFoundContent={loading ? <Spin size="small" /> : null}
            onFocus={() => {
              if (props?.instantSelect) {
                if (!isEmptyValues(props?.value)) {
                  setKeyword(props?.value);
                  if (props?.numberSelectItem !== true) {
                    getDiagnosisDataSource(props?.value);
                  }
                }
              }

              listRef?.current?.onFocus(null, props?.value);
            }}
            onBlur={(event) => {
              if (props?.numberSelectItem !== true) {
                setTimeout(() => {
                  setDataSource([]);
                  setHasSearched(false);
                  setErrorTooltipOpen(false);
                  setKeyword('');
                  setOffset(0);
                  setRecordTotal(0);
                }, 0);
              }
            }}
            onClear={() => {
              onIcdeSelectClear();
            }}
            onKeyDown={(event) => {
              console.log('onKeyDown', event?.key);

              // 当且仅当
              if (props?.numberSelectItem === true) {
                listRef?.current?.onKeyDown(event);
              }

              if (
                props?.tableId &&
                (event as any)?.hosted !== true &&
                event?.ctrlKey === false
              ) {
                if (event?.key === 'ArrowUp') {
                  Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                    event: event,
                    type: 'UP',
                    trigger: 'selectkeydown',
                  });
                }

                if (event?.key === 'ArrowDown') {
                  Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                    event: event,
                    type: 'DOWN',
                    trigger: 'selectkeydown',
                  });
                }
              }
            }}
            onSelect={(value) => {
              onIcdeSelect(value);
            }}
            options={
              props?.numberSelectItem
                ? [
                    {
                      Code: 'Default',
                      Name: 'Default',
                    },
                  ]
                : dataSource
            }
            dumbOnComposition={true}
            mousedownOptionOpen={false}
            doubleClickPopUp={false}
            numberSelectItem={props?.numberSelectItem}
            // keyboard friendly
            {...icdeKeyboardFriendlyProps}
          />
        </Wrapper>
      </Tooltip>
    </div>
  );
};

export default IcdeSelect;
