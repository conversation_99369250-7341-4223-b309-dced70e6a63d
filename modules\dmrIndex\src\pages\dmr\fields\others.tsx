import { IGridItem } from '@/pages/dmr/interfaces';

export const others: IGridItem[][] = [
  [
    {
      data: {
        prefix: 'HBS-AG检查',
        key: 'Hbsag',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Hbsag',
          modelDataKey: 'Hbsag',
          modelDataGroup: 'Dmr',
          placeholder: '请选择',
          optionNameKey: 'Name',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: 'HCV-AB检查',
        key: 'Hcvab',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Hcvab',
          modelDataKey: 'Hcvab',
          modelDataGroup: 'Dmr',
          placeholder: '请选择',
          optionNameKey: 'Name',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: 'HIV-AB检查',
        key: 'Hivab',
        desc: '',
        suffix: '',
        component: 'DmrSelect',
        props: {
          formKey: 'Hivab',
          modelDataKey: 'Hivab',
          modelDataGroup: 'Dmr',
          placeholder: '请选择',
          optionNameKey: 'Name',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '有创呼吸机使用时间',
        key: 'YCHXJSYSJ',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 1,
          precious: 0,
          formKey: 'YCHXJSYSJ',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '特级护理天数',
        key: 'NurseSups',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 1,
          precious: 0,
          formKey: 'NurseSups',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '一级护理天数',
        key: 'NurseFirs',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 1,
          precious: 0,
          formKey: 'NurseFirs',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '二级护理天数',
        key: 'NurseSecs',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 1,
          precious: 0,
          formKey: 'NurseSecs',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '三级护理天数',
        key: 'NurseThis',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 1,
          precious: 0,
          formKey: 'NurseThis',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '重症监护合计时长：',
        key: 'IcuDuration',
        desc: '',
        suffix: '',
        component: 'TimeRangeStats',
        props: {
          formKey: 'IcuDuration',
          items: [
            {
              label: '',
              inputKeys: ['day', 'hour', 'minute'],
              formKeys: ['IcuDuration_T', 'IcuDuration_XS', 'IcuDuration_FZ'],
            },
          ],
        },
      },
      h: 1,
      w: 12,
    },
  ],
];
