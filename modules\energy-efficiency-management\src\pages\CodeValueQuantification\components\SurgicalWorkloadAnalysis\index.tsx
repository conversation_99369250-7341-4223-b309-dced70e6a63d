import { UniTable } from '@uni/components/src/index';
import { Card, Col, Row, Select, Space } from 'antd';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import './index.less';
import {
  CodeValueQuantificationEventConstants,
  ELEMENT_CONFIG,
  API_ENDPOINTS,
  ElementType,
  ELEMENT_TYPES,
} from '../../constants';
import { SurgicalWorkloadAnalysisColumns } from './columns';

function normalizeArrayData(array: any[]) {
  return array.map((obj) => {
    const normalizedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = key.replace(/^[^_]*_/, '');
        normalizedObj[newKey] = obj[key];
      }
    }
    return normalizedObj;
  });
}

export interface ISurgicalWorkloadAnalysisProps {
  isSubComponent?: ElementType;
  tableParams?: any;
  dictData?: any;
  groupItems?: any;
  filterDrillDownMetrics?: boolean;
}

const SurgicalWorloadAnalysis = ({
  isSubComponent,
  tableParams,
  dictData,
  groupItems,
  filterDrillDownMetrics = false,
}: ISurgicalWorkloadAnalysisProps) => {
  const [title, setTitle] = useState({
    label: undefined,
    value: undefined,
  });

  const [selectedValue, setSelectedValue] = useState(undefined);

  // 手术下拉框
  const {
    data: operDrillDownMetricsData,
    loading: getOperDrillDownMetricsLoading,
    run: getOperDrillDownMetricsReq,
  } = useRequest(
    () =>
      uniCommonService(API_ENDPOINTS.GET_OPER_DRILLDOWN_METRICS, {
        method: 'POST',
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data) {
            let result = [];
            Object.keys(res.data).forEach((key) => {
              result.push({
                label: res.data?.[key],
                value: key,
              });
            });
            setSelectedValue(result?.at(0)?.value);
            return result;
          }
        }
        return [];
      },
    },
  );

  // 数据获取
  const {
    data: statsChangedData,
    loading: getStatsChangedLoading,
    run: getStatsChangedReq,
    fetches: fetchesStatsChanged,
  } = useRequest(
    (id, data) => {
      const BasicArgs = {
        ...tableParams,
        ...data?.data,
      };
      if (isSubComponent === ELEMENT_TYPES.CLIDEPT) {
        // data有值就走data没有走tableParams
        const list = data?.data?.[`${isSubComponent}s`] || [];
        BasicArgs.CliDepts = list.length ? list : tableParams?.CliDepts || [];
      }
      if (isSubComponent === ELEMENT_TYPES.MAJOR_PERF_DEPT) {
        const list = data?.data?.[`${isSubComponent}s`] || [];
        BasicArgs.MajorPerfDepts = list.length
          ? list
          : tableParams?.MajorPerfDepts || [];
      }
      return uniCommonService(API_ENDPOINTS.GET_OPER_DRILLDOWN_STATS, {
        method: 'POST',
        data: {
          BasicArgs,
          MetricName: data?.MetricName,
          MetricType: data?.MetricType,
          GrouperCols: [],
        },
      });
    },
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data) {
            return normalizeArrayData(res?.data?.data);
          }
        }
        return [];
      },
    },
  );

  useEffect(() => {
    if (isSubComponent) {
      Emitter.on(
        CodeValueQuantificationEventConstants.STAT_ROW_CLK +
          (isSubComponent ? `#${isSubComponent}` : '_SELF_PAGE'),
        (record) => {
          if (isSubComponent) {
            const config = ELEMENT_CONFIG[isSubComponent];
            setTitle({
              label:
                dictData?.['Dmr']?.[config.dictModule]?.find(
                  (item) => item?.Code === record?.[isSubComponent],
                )?.Name || record?.[isSubComponent],
              value: record?.[isSubComponent],
            });
          }
        },
      );
    }

    Emitter.on(
      CodeValueQuantificationEventConstants.SURGICAL_CNT_CLK,
      ({ record, type }) => {
        Emitter.emit(
          CodeValueQuantificationEventConstants.SURGICAL_MODAL_OPEN,
          {
            MetricName: selectedValue,
            MetricType: type,
            OperCode: record?.OperCode,
            OperName: record?.OperName,
            [isSubComponent]: title?.value,
            dictName: title?.label,
          },
        );
      },
    );

    return () => {
      Emitter.off(
        CodeValueQuantificationEventConstants.STAT_ROW_CLK +
          (isSubComponent ? `#${isSubComponent}` : '_SELF_PAGE'),
      );
      Emitter.off(CodeValueQuantificationEventConstants.SURGICAL_CNT_CLK);
    };
  }, [isSubComponent, dictData, title, selectedValue]);

  useEffect(() => {
    getOperDrillDownMetricsReq();
  }, []);

  useEffect(() => {
    const hasValidParams = tableParams && Object.keys(tableParams)?.length;
    if (hasValidParams) {
      if (isSubComponent) {
        if (selectedValue && title) {
          ['Incremental', 'Reductive'].forEach((type) => {
            getStatsChangedReq(type, {
              MetricName: selectedValue,
              MetricType: type,
              data: {
                [`${isSubComponent}s`]: title?.value ? [title?.value] : [],
              },
            });
          });
        }
      } else {
        if (selectedValue) {
          ['Incremental', 'Reductive'].forEach((type) => {
            getStatsChangedReq(type, {
              MetricName: selectedValue,
              MetricType: type,
            });
          });
        }
      }
    }
  }, [selectedValue, title, isSubComponent, tableParams]);

  const renderSurgicalContent = () => {
    const tables = [
      { id: 'Incremental', label: '新增' },
      { id: 'Reductive', label: '减少' },
    ].map(({ id, label }) => (
      <Col span={12} key={id}>
        <Card
          title={`${title?.label || ''} ${
            operDrillDownMetricsData?.find(
              (item) => item.value === selectedValue,
            )?.label || ''
          } ${label}工作量统计`}
          size="small"
        >
          <UniTable
            rowKey="OperCode"
            id={id}
            loading={fetchesStatsChanged?.[id]?.loading}
            columns={SurgicalWorkloadAnalysisColumns(id)}
            dataSource={fetchesStatsChanged?.[id]?.data}
          />
        </Card>
      </Col>
    ));

    return <Row gutter={[16, 16]}>{tables}</Row>;
  };

  return (
    <div>
      {isSubComponent ? (
        <>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              margin: '10px 0',
            }}
          >
            <div>{title?.label || ''} 手术工作量统计</div>
            <Space size="small">
              <span>术式：</span>
              <Select
                getPopupContainer={(trigger) =>
                  trigger?.parentElement || document.body
                }
                options={
                  (filterDrillDownMetrics
                    ? operDrillDownMetricsData?.filter(
                        (item) => item.value !== 'ThirdFourthOper',
                      )
                    : operDrillDownMetricsData) ?? []
                }
                style={{ width: '150px' }}
                allowClear={false}
                value={selectedValue}
                onChange={setSelectedValue}
              />
            </Space>
          </div>
          {renderSurgicalContent()}
        </>
      ) : (
        <Card
          title="手术工作量统计"
          extra={
            <Space size="small">
              <span>术式：</span>
              {/* ThirdFourthOper */}
              <Select
                options={
                  (filterDrillDownMetrics
                    ? operDrillDownMetricsData?.filter(
                        (item) => item.value !== 'ThirdFourthOper',
                      )
                    : operDrillDownMetricsData) ?? []
                }
                getPopupContainer={(trigger) =>
                  trigger?.parentElement || document.body
                }
                style={{ width: '150px' }}
                allowClear={false}
                value={selectedValue}
                onChange={setSelectedValue}
              />
            </Space>
          }
        >
          {renderSurgicalContent()}
        </Card>
      )}
    </div>
  );
};

export default SurgicalWorloadAnalysis;
