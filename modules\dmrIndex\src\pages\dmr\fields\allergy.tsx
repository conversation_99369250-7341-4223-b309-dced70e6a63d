import { IGridItem } from '@/pages/dmr/interfaces';
import React from 'react';
import { SuffixItem } from '@uni/grid/src/components/suffix-item';

export const allergy: IGridItem[] = [
  {
    data: {
      prefix: '药物过敏',
      key: 'MedicineAllergy',
      desc: '',
      // component: 'InputSuffix',
      component: 'AutoSelect',
      props: {
        formItemId: 'formItem#Allergy',
        bordered: false,
        formKey: 'Allergy',
        width: 150,
        style: {
          border: 'none',
        },
        className: 'flex-row-wrap-center',
        containerClassName: 'form-content-item-container',
        selectStyle: {
          // borderBottom: '1px solid #babfc7',
        },
        modelDataKey: 'Allergy',
        modelDataGroup: 'Dmr',

        hasSuffix: true,
        suffixProps: {
          showInput: true,
          showTags: false,
          formKey: 'AllergyDrugs',
          conditionKey: 'Allergy',
          modelDataKey: 'AllergyDrugs',
          modelDataGroup: 'Dmr',
          disableConditionOperator: 'equal',
          disableConditionValue: '1',
        },
        renderSuffixNode: (props: any) => {
          return (
            <div className={'flex-row-center '} style={{ flex: 1 }}>
              <SuffixItem
                {...props}
                formItemId={'formItem#AllergyDrugs'}
                style={{ marginLeft: 10, flex: 1, display: 'flex' }}
                label={'过敏药物：'}
                className={'allergy-medicine'}
                disableCondition={(value) => {
                  if (
                    props?.disableConditionValue === undefined ||
                    props?.disableConditionOperator === undefined
                  ) {
                    return false;
                  }

                  if (props?.disableConditionOperator === 'not_equal') {
                    return value?.toString() !== props?.disableConditionValue;
                  } else {
                    return value?.toString() === props?.disableConditionValue;
                  }
                }}
              />
            </div>
          );
        },
      },
    },
    w: 13,
    xxs: {
      h: 1.2,
      data: {
        itemClassName: 'grid-item-start-container',
      },
    },
  },
  {
    data: {
      prefix: '死亡患者尸检',
      key: 'Autopsy',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'Autopsy',
        modelDataKey: 'SJ',
        modelDataGroup: 'Dmr',
        formItemStyle: { width: '100%' },
        className: '',
        // dataSource: [
        //   {
        //     Code: '-',
        //     Name: '-',
        //   },
        // ],
      },
    },
    w: 5,
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
];
