import { IGridItem } from '@/pages/dmr/interfaces';

export const pathologicalDiagnosis: IGridItem[][] = [
  [
    {
      data: {
        prefix: '肿瘤病理诊断编码',
        key: 'IcdePathosItem',
        desc: '',
        suffix: '',
        component: 'IcdeSelect',
        props: {
          componentId: 'IcdePathosIcdeCode',
          selectFormKey: 'IcdePathosIcdeCode',
          itemKey: 'IcdePathosItem',
          formKeys: {
            IcdePathosIcdeName: 'Name',
            IcdePathosIcdeCode: 'Code',
          },
          parentId: 'dmr-content-container',
          icdeSelectType: 'IsMorphology',
        },
      },
      w: 4,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 12,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '肿瘤病理诊断名称',
        key: 'IcdePathosIcdeName',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
          disabled: false,
        },
      },
      w: 10,
      md: {
        w: 8,
      },
      sm: {
        w: 7,
      },
      xs: {
        w: 12,
      },
      xxs: {
        w: 10,
      },
    },
    {
      data: {
        prefix: '肿瘤病理号',
        key: 'IcdePathosPalgNo',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 4,
    },
  ],
];
