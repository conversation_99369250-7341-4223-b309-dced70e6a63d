{"name": "@uni/utils", "version": "1.0.0", "description": "", "types": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "keywords": [], "authors": ["zhang.jun"], "license": "MIT", "files": ["dist", "compiled"], "publishConfig": {"access": "public"}, "dependencies": {"@uni/commons": "workspace:*", "better-xlsx": "0.7.6", "rc-util": "^5.27.2", "eventemitter3": "^4.0.7", "file-saver": "^2.0.5", "pinyin-pro": "^3.15.2", "dayjs": "^1.9.4", "lodash": "^4.17.21", "mime-types": "^2.1.35", "fuzzysort": "2.0.4", "exceljs": "4.4.0", "streamsaver": "2.0.6"}, "devDependencies": {}}