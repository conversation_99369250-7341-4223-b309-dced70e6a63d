import { PropertyItem } from '@/pages/configuration/interfaces';

export const InputProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.disabled',
      label: '禁用',
      width: 'xs',
      component: 'Switch',
      fieldProps: {},
    },
    {
      key: 'data.props.tooltip',
      label: '显示文字提示',
      width: 'xs',
      component: 'Switch',
      fieldProps: {},
    },
  ],
];

export const PatAgeWithYMDProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.disabled',
      label: '禁用',
      width: 'xs',
      component: 'Switch',
      fieldProps: {},
    },
  ],
];

export const InputNumberProperties: PropertyItem[][] = [];

export const RestrictInputNumberProperties: PropertyItem[][] = [
  // min: 0,
  // max: 200,
  // step: 1,

  [
    {
      key: 'data.props.min',
      label: '最小值',
      width: 'xs',
      component: 'Digit',
      fieldProps: {
        precision: 0,
      },
    },
    {
      key: 'data.props.max',
      label: '最大值',
      width: 'xs',
      component: 'Digit',
      fieldProps: {
        precision: 0,
        min: 1,
      },
    },
  ],
  [
    {
      key: 'data.props.step',
      label: '步进',
      width: 'xs',
      component: 'Digit',
      fieldProps: {
        step: 0.01,
      },
    },
    {
      key: 'data.props.precious',
      label: '精确到小数位',
      width: 'xs',
      component: 'Digit',
      fieldProps: {
        step: 1,
        precision: 0,
        max: 10,
        min: 0,
      },
    },
  ],
];
