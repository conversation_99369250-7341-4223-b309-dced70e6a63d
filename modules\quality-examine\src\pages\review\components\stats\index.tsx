import { Spin } from 'antd';
import { useState, useMemo, useEffect } from 'react';
import { useRequest } from 'umi';
import _ from 'lodash';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { CheckCard } from '@ant-design/pro-components';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

export interface IStatsProps {
  summaryInfo?: any;
  setSelectedStatItem: (string) => void;
  loading: Boolean;
  selectedStatItem?: string;
  selectOption: string;
}

const Stats = (props: IStatsProps) => {
  const {
    summaryInfo,
    setSelectedStatItem,
    loading,
    selectedStatItem,
    selectOption = 'TaskCnt',
  } = props;

  const [columns, setColumns] = useState<any[]>([]);

  const { loading: columnsLoading, run: getColumnReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res?.statusCode === 200) {
          setColumns(tableColumnBaseProcessor([], res.data?.Columns));
        } else {
          setColumns([]);
        }
      },
    },
  );

  useEffect(() => {
    getColumnReq();
  }, []);

  // 合并 columns 和 summaryInfo
  const mergedStatsData = useMemo(() => {
    // 只保留数量类（去掉Ratio结尾的比例列）
    return columns
      .filter(
        (col) => col.visible && !String(col.dataIndex).endsWith('Ratio'), // 只保留数量
      )
      .map((col) => {
        // 找到对应的比例列
        const ratioCol = columns.find(
          (c) =>
            c.visible &&
            c.dataIndex === `${col.dataIndex.replace(/Cnt$/, '')}Ratio`,
        );
        // 获取比例值并格式化为百分比
        let percent = '';
        if (ratioCol) {
          percent =
            (Number(summaryInfo?.[ratioCol?.dataIndex] || 0) * 100)?.toFixed(
              2,
            ) + '%';
        }
        // 平均分数特殊处理，保留两位小数
        let value = summaryInfo?.[col.dataIndex] ?? 0;
        if (col.dataIndex === 'AvgScore') {
          value =
            value !== null && value !== undefined
              ? Number(value).toFixed(2)
              : 0;
        }
        return {
          key: col.dataIndex,
          title: col.title || col.dataIndex,
          value,
          percent,
          ...col,
        };
      });
  }, [columns, summaryInfo]);

  // 设置默认选中项为第一个
  useEffect(() => {
    if (mergedStatsData.length > 0) {
      setSelectedStatItem(
        selectedStatItem ||
          mergedStatsData.find((e) => e.key === selectOption)?.dataIndex,
      );
    }
  }, [mergedStatsData]);

  return (
    <div className="stats-item">
      <Spin spinning={loading || columnsLoading}>
        <div className="edit-btn">
          <TableColumnEditButton
            columnInterfaceUrl={
              'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary'
            }
            onTableRowSaveSuccess={(columns) => {
              setColumns(tableColumnBaseProcessor([], columns));
            }}
          />
        </div>
        <CheckCard.Group
          className="stats-card"
          value={selectedStatItem}
          onChange={(value) => {
            if (['TargetTaskCnt', 'AvgScore'].includes(value)) {
              return;
            }
            setSelectedStatItem(value);
          }}
        >
          {mergedStatsData?.map((item) => (
            <CheckCard
              size="small"
              key={item?.dataIndex}
              value={item.dataIndex}
              title={item?.title}
              style={
                ['TargetTaskCnt', 'AvgScore'].includes(item.dataIndex)
                  ? { cursor: 'unset' }
                  : {}
              }
              description={
                <span>
                  {item.value}
                  {item.percent && <span>（{item.percent}）</span>}
                </span>
              }
            />
          ))}
        </CheckCard.Group>
      </Spin>
    </div>
  );
};
export default Stats;
