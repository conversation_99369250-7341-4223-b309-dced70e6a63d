import { IGridItem } from '@/pages/dmr/interfaces';

export const headerData: IGridItem[][] = [
  [
    {
      data: {
        prefix: '住院号',
        key: 'PatAdmNo',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
        },
      },
      w: 5,
    },
    {
      data: {
        prefix: '住院次数',
        key: 'InTimes',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          bordered: false,
          style: {
            maxWidth: 35,
          },
          formKey: 'InTimes',
          min: 1,
          precious: 0,
          step: 1,
        },
      },
      w: 5,
    },
    {
      data: {
        prefix: '医保卡号',
        key: 'InsurCardNumber',
        desc: '',
        suffix: '',
        component: 'Input',
        props: {
          bordered: false,
          disabled: true,
        },
      },
      w: 5,
    },
    {
      data: {
        prefix: '医疗付费方式',
        key: 'YLFKFS',
        desc: '',
        suffix: '',
        component: 'AutoSelect',
        props: {
          formKey: 'YLFK<PERSON>',
          modelDataKey: 'YLFKFS',
          modelDataGroup: 'Dmr',
          className: 'payment-input',
          formItemStyle: { width: '100%' },
        },
      },
      w: 5,
    },
  ],
];
