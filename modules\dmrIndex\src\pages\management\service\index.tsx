import { uniCommonService } from '@uni/services/src';

export const lockDmrCard = (dmrCardId: number) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/Lock', {
    method: 'POST',
    data: {
      dmrCardId: dmrCardId,
    },
  });
};

export const unlockDmrCard = (dmrCardId: number) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/UnLock', {
    method: 'POST',
    data: [dmrCardId],
  });
};

export const batchLockDmrCard = (dmrCardIds: number[]) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/BatchLock', {
    method: 'POST',
    data: dmrCardIds,
  });
};

export const batchUnLockDmrCard = (dmrCardIds: number[]) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/UnLock', {
    method: 'POST',
    data: dmrCardIds,
  });
};

export const destroyDmrCard = (dmrCardIds: number[]) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/BatchInvalid', {
    method: 'POST',
    data: dmrCardIds,
  });
};

// 批量审核 接口
export const batchReviewDmrCard = (
  dmrCardIds: number[],
  description: string,
) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/BatchReview', {
    method: 'POST',
    data: {
      dmrCardIds,
      description,
    },
  });
};

// 轮询获取审核进程的 进度 接口 【默认24小时内的数据list】
export const pollingQualityResultList = (minDateTime: string = null) => {
  return uniCommonService(
    'Api/Dmr/DmrDataManagement/GetRecentQualityResultMasters',
    {
      method: 'POST',
      data: minDateTime,
    },
  );
};

// 取消某条审核进程（不展示）接口
export const cancelQualityOne = (masterId: number = null) => {
  return uniCommonService(
    'Api/Dmr/DmrDataManagement/CancelQualityResultMaster',
    {
      method: 'POST',
      data: masterId,
    },
  );
};

// 审核 by searchParams
export const reviewDmrCard = (startDate, endDate, hospCodes) => {
  let data = {
    Sdate: startDate,
    Edate: endDate,
  };

  if (hospCodes && hospCodes?.length > 0) {
    data['HospCode'] = hospCodes;
  }

  return uniCommonService('Api/Dmr/DmrDataManagement/Review', {
    method: 'POST',
    data: data,
  });
};

// 重置 by Ids
export const batchResetStatusDmrCard = (dmrCardIds: number[]) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/ResetStatus', {
    method: 'POST',
    data: dmrCardIds,
  });
};

// 重置 by searchParams
export const resetStatusDmrCard = (startDate, endDate, hospCodes) => {
  let data = {
    Sdate: startDate,
    Edate: endDate,
  };

  if (hospCodes && hospCodes?.length > 0) {
    data['HospCode'] = hospCodes;
  }
  return uniCommonService('Api/Dmr/DmrDataManagement/BatchResetStatus', {
    method: 'POST',
    data: data,
  });
};

// 重置 by Ids
export const importDmrCard = (dmrCardIds: number[]) => {
  return uniCommonService('Api/Dmr/DmrDataManagement/PullCard ', {
    method: 'POST',
    data: {
      HisId: dmrCardIds,
    },
  });
};
