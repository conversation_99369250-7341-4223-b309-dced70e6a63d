import IconBtn from '@uni/components/src/iconBtn';
import { QualityExamineStatus } from '@/pages/review/components/score-comment/score/constant';
import {
  CarryOutOutlined,
  FileTextOutlined,
  UndoOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { Popover, Tooltip } from 'antd';
import { isEmptyValues } from '@uni/utils/src/utils';

const summaryKeys = [
  {
    label: '病案号',
    key: 'PatNo',
  },
  {
    label: '住院号',
    key: 'PatAdmNo',
  },
  {
    label: '分数',
    key: 'Score',
  },
  {
    label: '评级',
    key: 'ScoreLevelName',
  },
];

const RecordSummary = (props: any) => {
  return (
    <div className={'record-summary-container'}>
      {summaryKeys
        ?.filter((item) => !isEmptyValues(props?.record?.[item?.key]))
        ?.map((item) => {
          return (
            <div className={'summary-item'}>
              <span className={'summary-label'}>{item?.label}：</span>
              <span className={'summary-content'}>
                {props?.record?.[item?.key]}
              </span>
            </div>
          );
        })}
    </div>
  );
};

export const dmrReviewBasicColumns = (
  drawerRef: any,
  extraHiddenColumns: any[],
  showCancel: boolean,
  showAccept: boolean,
  reviewContainerRef?: any,
  showOtherExamineTask?: boolean,
) => {
  return [
    ...(extraHiddenColumns ?? []),
    {
      dataIndex: 'operation',
      visible: true,
      width: 100,
      align: 'center',
      title: '操作',
      fixed: 'left',
      render: (node, record, index) => {
        return (
          <div
            className={'flex-row-center'}
            style={
              showCancel === true
                ? { justifyContent: 'space-between' }
                : { justifyContent: 'center' }
            }
          >
            <Popover
              placement="top"
              title={'摘要'}
              content={<RecordSummary record={record} />}
            >
              <div
                className="operation-btn"
                style={{
                  height: 28,
                  alignItems: 'center',
                  display: 'flex',
                  flex: 1,
                  justifyContent: 'center',
                }}
              >
                <FileTextOutlined style={{ fontSize: 18, color: '#1464f8' }} />
              </div>
            </Popover>

            <IconBtn
              title="查看详情"
              type="checkInfo"
              className="operation-btn"
              style={{
                height: 28,
                alignItems: 'center',
                display: 'flex',
                flex: 1,
                justifyContent: 'center',
              }}
              onClick={(e) => {
                drawerRef?.current?.showDrawer({
                  taskItem: record,
                  status: true,
                });
              }}
            />

            {showOtherExamineTask === true &&
              !isEmptyValues(record?.OtherExamineTaskId) && (
                <IconBtn
                  title={record?.OtherExamineMsg || '存在其他评审信息'}
                  type=""
                  customIcon={<ExportOutlined style={{ fontSize: 16 }} />}
                  className="operation-btn"
                  onClick={(e) => {
                    drawerRef?.current?.showDrawer({
                      taskItem: record,
                      status: true,
                      readonly: true,
                      extraExamineTaskId: record?.OtherExamineTaskId,
                    });
                  }}
                />
              )}

            {showAccept === true &&
              [
                QualityExamineStatus.Pending,
                QualityExamineStatus.Reviewing,
                // QualityExamineStatus.Reviewed,
                // QualityExamineStatus.Rejected,
                // QualityExamineStatus.ReSubmitted,
              ]?.includes(record?.Status) && (
                <IconBtn
                  title="审核无误"
                  type={''}
                  customIcon={<CarryOutOutlined style={{ fontSize: 18 }} />}
                  btnDisabled={
                    ![
                      QualityExamineStatus.Pending,
                      QualityExamineStatus.Reviewing,
                      // QualityExamineStatus.Reviewed,
                      // QualityExamineStatus.Rejected,
                      // QualityExamineStatus.ReSubmitted,
                    ]?.includes(record?.Status)
                  }
                  className="operation-btn"
                  style={{
                    height: 28,
                    alignItems: 'center',
                    display: 'flex',
                    flex: 1,
                    justifyContent: 'center',
                  }}
                  onClick={(e) => {
                    // 审核无误按钮
                    reviewContainerRef?.current?.instantReview(record, index);
                  }}
                />
              )}

            {showCancel === true &&
              ![
                QualityExamineStatus.Reviewing,
                QualityExamineStatus.Pending,
                QualityExamineStatus.Init,
              ]?.includes(record?.Status) && (
                <IconBtn
                  title="撤回审核"
                  type={''}
                  customIcon={<UndoOutlined style={{ fontSize: 18 }} />}
                  btnDisabled={[
                    QualityExamineStatus.Reviewing,
                    QualityExamineStatus.Pending,
                    QualityExamineStatus.Init,
                  ]?.includes(record?.Status)}
                  className="operation-btn"
                  style={{
                    height: 28,
                    alignItems: 'center',
                    display: 'flex',
                    flex: 1,
                    justifyContent: 'center',
                  }}
                  onClick={(e) => {
                    // 取消按钮
                    reviewContainerRef?.current?.cancelReview(record, index);
                  }}
                />
              )}
          </div>
        );
      },
    },
  ];
};

export const dmrReviewColumns = [
  // {
  //   dataIndex: 'ReviewerCode',
  //   visible: false,
  // },
  // {
  //   dataIndex: 'ReviewerUserId',
  //   visible: false,
  // },
  // {
  //   dataIndex: 'ReviewerUserName',
  //   visible: false,
  // },
];

export const dmrAuditeeColumns = [
  // {
  //   dataIndex: 'Coder',
  //   visible: false,
  // },
  // {
  //   dataIndex: 'CoderName',
  //   visible: false,
  // },
];

export const dmrSelectionTableColumns = [
  {
    dataIndex: 'HisId',
    title: 'HisId',
    visible: false,
    align: 'center',
  },
  {
    dataIndex: 'PatNo',
    title: '病案号',
    visible: true,
    width: 100,
    align: 'center',
  },
  {
    dataIndex: 'PatName',
    title: '姓名',
    visible: true,
    width: 100,
    align: 'center',
  },
  {
    dataIndex: 'HospCode',
    title: '院区',
    width: 100,
    align: 'center',
    dictionaryModule: 'Hospital',
    dictionaryModuleGroup: null,
  },
  {
    dataIndex: 'InDate',
    title: '入院时间',
    visible: true,
    width: 150,
    align: 'center',
    dataType: 'DateByDash',
  },
  {
    dataIndex: 'InTimes',
    visible: false,
  },
  {
    dataIndex: 'OutDate',
    title: '出院时间',
    visible: true,
    width: 150,
    align: 'center',
    dataType: 'DateByDash',
  },
  {
    dataIndex: 'RegisterStatusName',
    title: '登记状态',
    visible: true,
    width: 80,
    align: 'center',
  },
];
