import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { CardBundleInfo } from '@/pages/chs/network/interfaces';
import assign from 'lodash/assign';
import pick from 'lodash/pick';
import keys from 'lodash/keys';
import { isEmptyValues } from '@uni/utils/src/utils';
import { message } from 'antd';

const getBaseInfoReqV2 = (hisId) => {
  return uniCommonService('Api/Insur/InsurCardBundle/Get', {
    method: 'POST',
    data: {
      HisId: hisId,
    },
  });
};

export const getCardInfoV2 = async (
  hisId: string,
  chsProcessorInstance: any,
) => {
  let formFieldValue = {};

  let cardBundleInfo: RespVO<CardBundleInfo> = await getBaseInfoReqV2(hisId);
  if (cardBundleInfo?.code === 0 && cardBundleInfo?.statusCode === 200) {
    console.error('cardBundleInfo', cardBundleInfo);

    formFieldValue = Object.assign({}, cardBundleInfo?.data?.CardFlat);

    // 诊断
    formFieldValue = await chsProcessorInstance.cardIcdeResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.IcdeResult,
      cardBundleInfo?.data?.InsurIcdeOperMetaData?.IcdeMetaData?.Data,
    );

    // 手术
    formFieldValue = await chsProcessorInstance.cardOperationResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Opers,
      cardBundleInfo?.data?.InsurIcdeOperMetaData?.OperMetaData?.Data,
    );

    // 转科
    formFieldValue = chsProcessorInstance.cardTransferResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Transfers,
    );

    // icu
    formFieldValue = chsProcessorInstance.cardIcuResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Icus,
    );
    // 血液
    formFieldValue = chsProcessorInstance.cardBloodsResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Bloods,
    );

    // 费用
    formFieldValue = chsProcessorInstance.cardFeeChargesResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.FeeCharges,
    );
    formFieldValue = chsProcessorInstance.cardFeePaymentsResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.FeePayments,
    );

    // // 新生儿
    // formFieldValue = chsProcessorInstance.cardBabyResponseProcessor(
    //   formFieldValue,
    //   cardBundleInfo?.data?.Babys,
    // );
  }

  console.error('formFieldValue with v2', JSON.stringify(formFieldValue));
  return {
    formFieldValue: formFieldValue,
    cardBundleInfo: cardBundleInfo?.data,
  };
};

export const getChsIndexLayout = (
  cliDeptCode,
  hospCode,
  modules?: string[],
) => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  if (cliDeptCode) {
    identityParam = {
      IdentityCode: cliDeptCode,
      IdentityType: 'CliDepts',
    };
  } else if (hospCode) {
    identityParam = {
      IdentityCode: hospCode,
      IdentityType: 'Hospital',
    };
  }

  let data = {
    ...identityParam,
    configModules: ['ChsLayout', 'ChsHeaderLayout'].concat(modules ?? []),
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 获取数据库中保存的 operator sort 和 enable等字段
export const getChsOperatorConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['ChsOperatorsConfig'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 获取数据库中保存的 rule enable等字段
export const getChsPreCheckRulesConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['ChsPreCheckRules'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 右侧的
export const getChsPreCheckModuleConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['ChsPreCheckModules'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

export const getChsSavedMenu = () => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  let data = {
    ...identityParam,
    configModules: ['ChsLeftMenus'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 独立 第三方质控 按钮
export const insurBundleThirdPartyCheckReq = async (
  hisId: string,
  originInsurCardInfo: any,
  formFieldsValue: any,
  chsProcessorInstance: any,
  globalState: any,
) => {
  let data: CardBundleInfo = Object.assign({}, originInsurCardInfo);

  let checkData = await chsProcessorInstance.cardSaveCheckParamProcessor(
    data,
    originInsurCardInfo,
    formFieldsValue,
    globalState?.dictData?.['Insur'],
  );

  data = Object.assign({}, checkData);

  let reduced = new CardBundleInfo();
  let checkParams = assign(reduced, pick(data, keys(reduced)));
  checkParams['HisId'] = hisId;

  let thirdPartyCheckResponse = await uniCommonService(
    'Api/Insur/InsurCardBundle/ThirdPartyCheck',
    {
      method: 'POST',
      data: checkParams,
    },
  );

  console.log('third party response', thirdPartyCheckResponse);
  if (!isEmptyValues(thirdPartyCheckResponse?.data)) {
    window.open(thirdPartyCheckResponse?.data);
  } else {
    message.error('第三方质控结果不存在，请联系管理员');
  }
};
