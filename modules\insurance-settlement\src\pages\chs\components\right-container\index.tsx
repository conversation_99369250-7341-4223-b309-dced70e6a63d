import {
  <PERSON><PERSON>,
  Pop<PERSON>,
  Spin,
  Col,
  Collapse,
  List,
  Menu,
  Row,
  Tag,
  Tooltip,
  Anchor,
  Button,
  message,
  Table,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _, { isNumber } from 'lodash';
import './precheck.less';
import {
  ApartmentOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  CardBundleCheck,
  CardBundleInfo,
  DmrCardCheck,
  DmrChsGroup,
} from '@/pages/chs/network/interfaces';
import assign from 'lodash/assign';
import pick from 'lodash/pick';
import keys from 'lodash/keys';
import { useModel } from 'umi';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';

const { Panel } = Collapse;
const { Link } = Anchor;

const errLevelDict = {
  '0': {
    title: '无错误',
    color: 'green',
    abbr: '无',
  },
  '1': {
    title: '弱提示性规则错误',
    color: 'cyan',
    abbr: '弱',
  },
  '2': {
    title: '提示性规则错误',
    color: 'blue',
    abbr: '提',
  },
  '3': {
    title: '审核非强规则错误',
    color: 'purple',
    abbr: '非强',
  },
  '4': {
    title: '一般规则错误',
    color: 'volcano',
    abbr: '普',
  },
  '5': {
    title: '审核强制规则错误',
    color: 'red',
    abbr: '强',
  },
};

const basicChsDrgProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  {
    key: 'TotalFee',
    title: '总费用',
  },
  {
    key: 'CwValue',
    title: '支付标准',
  },
  {
    key: 'Profit',
    title: '预估盈亏',
  },
  {
    key: 'AvgFee',
    title: '标杆费用',
  },
  {
    key: 'AvgInPeriod',
    title: '标杆住院日',
  },
  {
    key: 'CwRate',
    title: '费率',
  },
  {
    key: 'Cw',
    title: 'Rw',
  },
  {
    key: 'BaseCwPoint',
    title: '基准点数',
  },
  {
    key: 'CwPointCoefficient',
    title: '成本系数',
  },
];

const basicChsDipProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  {
    key: 'TotalFee',
    title: '总费用',
  },
  {
    key: 'CwValue',
    title: '支付标准',
  },
  {
    key: 'Profit',
    title: '预估盈亏',
  },
  {
    key: 'AvgFee',
    title: '标杆费用',
  },
  {
    key: 'AvgInPeriod',
    title: '标杆住院日',
  },
  {
    key: 'CwRate',
    title: '结算点值',
  },
  {
    key: 'Cw',
    title: '分值',
  },
  {
    key: 'BaseCwPoint',
    title: '基准点数',
  },
  {
    key: 'CwPointCoefficient',
    title: '成本系数',
  },
];

export const chsDrgDipProps = {
  Drg: [
    ...basicChsDrgProps,
    {
      key: 'AbnFeeType',
      title: '病例类型',
    },
  ],
  Dip: [
    ...basicChsDipProps,
    {
      key: 'AbnFeeType',
      title: '病例类型',
    },
  ],
};

export const adjustDrgDipProps = {
  Drg: [
    ...basicChsDrgProps,
    {
      key: 'ShuffleMessage',
      title: '调整建议',
    },
  ],
  Dip: [
    ...basicChsDipProps,
    {
      key: 'ShuffleMessage',
      title: '调整建议',
    },
  ],
};

interface PreCheckResultProps {
  borderErrorInputs?: any;
  chsProcessorInstance?: any;
  borderErrorInputsInTable?: (
    tableErrorKeyItem: any,
    navigate?: boolean,
  ) => void;
}

const PreCheckResult = (props: PreCheckResultProps) => {
  const [currentMenuItemKey, setCurrentMenuItemKey] = useState('1');

  const [cardCheckData, setCardCheckData] = useState<any>({});

  const [cardGroupData, setCardGroupData] = useState<any>({});

  const [cardCalculated, setCardCalculated] = useState<any>(true);

  const [adjustDrgsResultData, setAdjustDrgsResultData] = useState<any>([]);

  const [chsGroupAuditData, setChsGroupAuditData] = useState(undefined);

  const [needChsGroupAudit, setNeedChsGroupAudit] = useState(false);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const modelData = globalState?.dictData?.['Insur'];

  useEffect(() => {
    Emitter.on(EventConstant.CHS_CHSGROUP_AUDIT, (data) => {
      setChsGroupAuditData(data);
      setNeedChsGroupAudit(true);
    });

    Emitter.on(EventConstant.CHS_CARD_DATA_RESET, () => {
      setCardCheckData({});
      setCardGroupData({});
      setAdjustDrgsResultData([]);
    });

    Emitter.on(EventConstant.CHS_CARD_SAVE_REVIEWS, (qualityCheckResult) => {
      if (isEmptyValues(qualityCheckResult)) {
        setCardCheckData({});
      } else {
        setCardCheckData({
          ...qualityCheckResult,
          Codes: _.orderBy(
            qualityCheckResult?.Reviews.filter(
              (item) => item.CheckCategory === '9',
            ),
            ['DisplayErrMsg'],
            ['asc'],
          ),
          Others: _.orderBy(
            qualityCheckResult?.Reviews.filter(
              (item) => item.CheckCategory !== '9',
            ),
            ['DisplayErrMsg'],
            ['asc'],
          ),
        });
      }
    });

    return () => {
      Emitter.off(EventConstant.CHS_CHSGROUP_AUDIT);
      Emitter.off(EventConstant.CHS_CARD_DATA_RESET);
      Emitter.off(EventConstant.CHS_CARD_SAVE_REVIEWS);
    };
  }, []);

  useEffect(() => {
    if (needChsGroupAudit) {
      if (globalState?.dictData?.['Insur'] && chsGroupAuditData) {
        let data = cloneDeep(chsGroupAuditData);
        insuranceBundleCheckReq(
          data?.hisId,
          data?.originChsCardInfo,
          data?.formFieldsValue,
        );
        setNeedChsGroupAudit(false);
        setChsGroupAuditData(undefined);
      }
    }
  }, [globalState, chsGroupAuditData, needChsGroupAudit]);

  // 接口 start
  const { loading: insuranceBundleCheckLoading, run: insuranceBundleCheckReq } =
    useRequest(
      async (hisId, originDmrCardInfo, formFieldsValue) => {
        let data: CardBundleInfo = Object.assign({}, originDmrCardInfo);
        data['HisId'] = hisId;

        let checkData =
          await props?.chsProcessorInstance?.cardSaveCheckParamProcessor(
            data,
            originDmrCardInfo,
            formFieldsValue,
            modelData,
          );

        data = Object.assign({}, checkData);

        let reduced = new CardBundleInfo();
        let checkParams = assign(reduced, pick(data, keys(reduced)));

        return uniCommonService('Api/Insur/InsurCardBundle/Check', {
          method: 'POST',
          data: checkParams,
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<CardBundleCheck>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            if (response?.data) {
              if (response?.data?.ChsMetricsResult) {
                let chsMetricsResult = response?.data?.ChsMetricsResult;
                if (chsMetricsResult?.ChsDrgsResult?.length) {
                  setCardGroupData(chsMetricsResult.ChsDrgsResult[0]);
                  setCardCalculated(chsMetricsResult?.IsCalculated);
                } else {
                  setCardGroupData({});
                  setCardCalculated(true);
                }
                if (chsMetricsResult?.AdjustDrgsResult?.length) {
                  setAdjustDrgsResultData(chsMetricsResult?.AdjustDrgsResult);
                } else {
                  setAdjustDrgsResultData([]);
                }
              } else {
                setCardGroupData({});
                setAdjustDrgsResultData([]);
              }

              if (response?.data?.QualityCheckResult) {
                let qualityCheckResult = response?.data?.QualityCheckResult;
                setCardCheckData({
                  ...qualityCheckResult,
                  Codes: _.orderBy(
                    qualityCheckResult?.Reviews.filter(
                      (item) => item.CheckCategory === '9',
                    ),
                    ['DisplayErrMsg'],
                    ['asc'],
                  ),
                  Others: _.orderBy(
                    qualityCheckResult?.Reviews.filter(
                      (item) => item.CheckCategory !== '9',
                    ),
                    ['DisplayErrMsg'],
                    ['asc'],
                  ),
                });
              } else {
                setCardCheckData({});
              }
            } else {
              setCardGroupData({});
              setAdjustDrgsResultData([]);
              setCardCheckData({});
            }
          } else {
            // TODO 提示信息
            message.error('接口错误');
          }
        },
      },
    );

  // 接口 end

  // popover
  const [hovered, setHovered] = useState(false);

  const handleHoverChange = (open: boolean) => {
    setHovered(open);
  };

  const actualColumnNameProcessor = (detailColumnsName: string) => {
    let columnsNames = detailColumnsName?.split('|');
    let columnNameWithIndex = columnsNames?.at(0)?.split('~');
    return {
      columnName: columnNameWithIndex?.at(0),
      columnIndex: parseInt(columnNameWithIndex?.at(1)),
    };
  };

  const columnNameTableProcessor = (detailColumnName: string) => {
    let { columnName, columnIndex } =
      actualColumnNameProcessor(detailColumnName);

    // 动态判定 根据列来判定
    const diagnosisTableColumnElements = document?.querySelectorAll(
      '#diagnosisTable #tanstack-table-container thead th',
    );
    const diagnosisTableColumnIds = [].slice
      .call(diagnosisTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    const operationTableColumnElements = document?.querySelectorAll(
      '#operationTable #tanstack-table-container thead th',
    );
    const operationTableColumnIds = [].slice
      .call(operationTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    const icuTableColumnElements = document?.querySelectorAll(
      '#icuTable #tanstack-table-container thead th',
    );
    const icuTableColumnIds = [].slice
      .call(icuTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    const bloodTableColumnElements = document?.querySelectorAll(
      '#bloodTable #tanstack-table-container thead th',
    );
    const bloodTableColumnIds = [].slice
      .call(bloodTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    if (diagnosisTableColumnIds.includes(columnName)) {
      return `diagnosisTable~${columnName}~${columnIndex}`;
    }

    if (operationTableColumnIds.includes(columnName)) {
      return `operationTable~${columnName}~${columnIndex}`;
    }

    if (icuTableColumnIds.includes(columnName)) {
      return `icuTable~${columnName}~${columnIndex}`;
    }

    if (bloodTableColumnIds.includes(columnName)) {
      return `bloodTable~${columnName}~${columnIndex}`;
    }

    return columnName;
  };

  const renderDrgResultCard = (props, data) => {
    return (
      <div>
        <Row gutter={[8, 6]}>
          {props.map((prop) => {
            switch (prop.key) {
              case 'DrgCode':
                return (
                  <Col span={24}>
                    <div className="title">
                      {data[prop.key]}&nbsp;&nbsp;
                      {data?.DrgName}
                    </div>
                  </Col>
                );
              case 'PaymentMode':
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className={`value font-danger text-bold-600`}>
                      {data['PaymentMode']}
                    </span>
                  </Col>
                );
              case 'Cw':
                if (!data.PointBasedMethod) {
                  return (
                    <Col span={24} className="item">
                      <div className="name">{prop.title}</div>
                      <span className="value">{data[prop.key]}</span>
                    </Col>
                  );
                }
                break;
              case 'CwRate':
                if (!data.PointBasedMethod) {
                  return (
                    <Col span={24} className="item">
                      <div className="name">{prop.title}</div>
                      <span className="value">
                        {data[prop.key]?.toFixed(2)}
                      </span>
                    </Col>
                  );
                }
                break;
              case 'BaseCwPoint':
              case 'CwPointCoefficient':
                if (data.PointBasedMethod) {
                  return (
                    <Col span={24} className="item">
                      <div className="name">{prop.title}</div>
                      <span className="value">
                        {data[prop.key]?.toFixed(2)}
                      </span>
                    </Col>
                  );
                }
                break;
              case 'ShuffleMessage':
                return (
                  <Col span={24} className="item">
                    {/* <div className="name">{prop.title}</div> */}
                    <span className="value font-danger text-bold-600">
                      {data[prop.key]}
                    </span>
                  </Col>
                );
              default:
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {isNumber(data[prop.key])
                        ? data[prop.key].toFixed(2)
                        : data[prop.key]}
                    </span>
                  </Col>
                );
            }
          })}
        </Row>
      </div>
    );
  };

  const renderCheckCards = (data) => {
    if (data && data.length) {
      return _.map(data, (item, key, index) => {
        const errObj = errLevelDict[item?.ErrorLevel];
        // if (
        //   item?.Details.length === 1 &&
        //   _.isEqual(item?.DisplayErrMsg, item.Details[0]?.ErrMsg)
        // ) {
        //   return (
        //     <div className="check-card">
        //       {errObj && (
        //         <>
        //           <Tooltip title={errObj.title}>
        //             <Tag color={errObj.color}>{errObj.abbr}</Tag>
        //           </Tooltip>

        //           <a
        //             href={`#${item.Details[0]?.ColumnName}`}
        //             onClick={(e) => {
        //               e.preventDefault();
        //               props.borderErrorInputs(
        //                 [`${item.Details[0]?.ColumnName}`],
        //                 true,
        //               );
        //             }}
        //           >
        //             {item.Details[0]?.ErrMsg}
        //           </a>
        //         </>
        //       )}
        //     </div>
        //   );
        // } else {
        return (
          <div className="check-card">
            <div className="">
              {errObj && (
                <Tooltip title={errObj.title}>
                  <Tag color={errObj.color}>{errObj.abbr}</Tag>
                </Tooltip>
              )}
              {item?.SubType}
            </div>
            {item.Details.map((det, i) => {
              let columnName = columnNameTableProcessor(det?.ColumnName);
              return (
                <div className="detail">
                  <a
                    href={`#${encodeURIComponent(columnName)}`}
                    onClick={(e) => {
                      e.preventDefault();
                      if (
                        !isEmptyValues(props?.borderErrorInputsInTable) &&
                        columnName?.includes('Table')
                      ) {
                        props.borderErrorInputsInTable(
                          {
                            key: columnName,
                            ...det,
                          },
                          true,
                        );
                      } else {
                        props.borderErrorInputs(
                          [`${encodeURIComponent(columnName)}`],
                          true,
                        );
                      }
                    }}
                  >
                    {det?.ErrMsg}
                  </a>
                </div>
              );
            })}
          </div>
        );
        // }
      });
    } else {
      return;
    }
  };

  return (
    <div className={'dmr-precheck-container'}>
      {/* <Badge.Ribbon text={cardGroupData?.AbnFeeType || ''} color="red">
        <div className="dmr-precheck-header">清单审核</div>
      </Badge.Ribbon> */}
      <Collapse
        className="main-collapse"
        expandIconPosition={'end'}
        bordered={false}
        defaultActiveKey={['2', '3']}
        style={{
          height:
            document.getElementById('site-layout-content')?.offsetHeight - 50,
          overflowY: 'auto',
        }}
      >
        <Panel header="医保预分组" key="2" id={'panel-pre-drg-container'}>
          <Spin spinning={insuranceBundleCheckLoading} size={'small'}>
            {cardCalculated ? (
              <div className="inner-collapse-container">
                {Object.keys(cardGroupData).length !== 0 && (
                  <div style={{ borderBottom: '1px solid #e9e9e9' }}>
                    <div className="single-card">
                      {renderDrgResultCard(
                        chsDrgDipProps[cardGroupData?.SettleMethod || 'Drg'],
                        cardGroupData,
                      )}
                    </div>
                  </div>
                )}
                <Collapse
                  style={{ width: '100%' }}
                  defaultActiveKey={['1']}
                  bordered={false}
                >
                  <Panel
                    key="2"
                    header={
                      <div
                        className="d-flex"
                        style={{ justifyContent: 'space-between' }}
                      >
                        可调整组
                        {adjustDrgsResultData.length !== 0 && (
                          <Badge
                            color="#f4a741"
                            count={adjustDrgsResultData.length}
                          />
                        )}
                      </div>
                    }
                  >
                    <div className="multi-single-card">
                      {adjustDrgsResultData.length !== 0 &&
                        _.map(adjustDrgsResultData, (data) => {
                          return (
                            Object.keys(data).length !== 0 && (
                              <div className="single-card">
                                {renderDrgResultCard(
                                  adjustDrgDipProps[
                                    cardGroupData?.SettleMethod || 'Drg'
                                  ],
                                  data,
                                )}
                              </div>
                            )
                          );
                        })}
                    </div>
                  </Panel>
                  <Panel key="3" header={'分项费用标杆值'}>
                    <Row>
                      <Col span={24}>
                        <Table
                          className="chs-bm-fee-table"
                          columns={[
                            {
                              title: '费用类型',
                              dataIndex: 'FeeType',
                            },
                            {
                              title: '当前费用',
                              dataIndex: 'Fee',
                              render: (_) => <>{_ ? _?.toFixed(2) : _}</>,
                            },
                            {
                              title: '标杆费用',
                              dataIndex: 'AvgFee',
                              render: (_) => <>{_ ? _?.toFixed(2) : _}</>,
                            },
                          ]}
                          dataSource={cardGroupData?.FeeTypeFees}
                          size="small"
                          pagination={false}
                        />
                      </Col>
                    </Row>
                  </Panel>
                </Collapse>
              </div>
            ) : (
              <span className={'not-calculate'}>
                分组服务出现异常，请联系工程师
              </span>
            )}
          </Spin>
        </Panel>
        <Panel header="审核结果" key="3">
          <Spin spinning={insuranceBundleCheckLoading} size={'small'}>
            <div className="inner-collapse-container">
              <Collapse style={{ width: '100%' }} bordered={false}>
                <Panel
                  key="1"
                  header={
                    <div
                      className="d-flex"
                      style={{ justifyContent: 'space-between' }}
                    >
                      首页问题
                      <Badge count={cardCheckData?.Others?.length ?? 0} />
                    </div>
                  }
                >
                  <>{renderCheckCards(cardCheckData?.Others)}</>
                </Panel>
                <Panel
                  key="2"
                  header={
                    <div
                      className="d-flex"
                      style={{ justifyContent: 'space-between' }}
                    >
                      编码问题
                      <Badge count={cardCheckData?.Codes?.length ?? 0} />
                    </div>
                  }
                >
                  <>{renderCheckCards(cardCheckData.Codes)}</>
                </Panel>
              </Collapse>
            </div>
          </Spin>
        </Panel>
      </Collapse>
    </div>
  );
};

export default PreCheckResult;
