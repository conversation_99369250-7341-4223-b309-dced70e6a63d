import {
  diffSeparatorSelector,
  SeparatorSelectorDiffKeys,
} from '@/pages/doubleDeck/components/diff/separate-selector';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  diffIcdeItem,
  IcdeDiffKeys,
} from '@/pages/doubleDeck/components/diff/icde';
import {
  diffKeyItem,
  diffOthers,
  isFeeItemKey,
  OthersDiffKeys,
} from '@/pages/doubleDeck/components/diff/others';
import pick from 'lodash/pick';
import Diff from 'diff';
import {
  diffTableData,
  TableDiffKeys,
} from '@/pages/doubleDeck/components/diff/table';

export const keyToDiffMethod = {
  CurAddressSeparateSelector: diffSeparatorSelector,
  RegistAddressSeparateSelector: diffSeparatorSelector,
  CurAddress: diffSeparatorSelector,
  RegistAddress: diffSeparatorSelector,

  IcdeAdmsItem: diffIcdeItem,
  // 入院疾病诊断
  IcdeOtpsItem: diffIcdeItem,
  // 损伤中毒诊断
  IcdeDamgsItem: diffIcdeItem,
  // 病理疾病诊断
  IcdePathosItem: diffIcdeItem,
  TcmIcdeOtpsItem: diffIcdeItem,
  TcmIcdeOtpsMainItem: diffIcdeItem,

  IcdeAdmsIcdeCode: diffIcdeItem,
  // 入院疾病诊断
  IcdeOtpsIcdeCode: diffIcdeItem,
  // 损伤中毒诊断
  IcdeDamgsIcdeCode: diffIcdeItem,
  // 病理疾病诊断
  IcdePathosIcdeCode: diffIcdeItem,
  IcdeOtpsIcdeCode: diffIcdeItem,
  IcdeOtpsMainIcdeCode: diffIcdeItem,

  IcuDuration: diffOthers,
  Unconscious: diffOthers,

  MedicineAllergy: diffKeyItem,
  PatientReturnPlan: diffKeyItem,
  OutHospital: diffKeyItem,

  'diagnosis-table': diffTableData,
  'operation-table': diffTableData,
  'pathological-diagnosis-table': diffTableData,
  'icu-table': diffTableData,
  'tcm-diagnosis-table': diffTableData,

  diagnosisTable: diffTableData,
  operationTable: diffTableData,
  pathologicalDiagnosisTable: diffTableData,
  icuTable: diffTableData,
  tcmDiagnosisTable: diffTableData,

  'department-transfer-table': diffTableData,
  departmentTransferTable: diffTableData,
};

const customDiffKeysMap = {
  ...SeparatorSelectorDiffKeys,
  ...IcdeDiffKeys,
  ...OthersDiffKeys,
  ...TableDiffKeys,
};

const keyToDmrItemId = {};
Object.keys(customDiffKeysMap)?.forEach((key) => {
  customDiffKeysMap?.[key]?.forEach((dataKey: string) => {
    keyToDmrItemId[dataKey] = key;
  });
});

// 判定是不是 normal key / true / false
export const keyIdentifier = (key: string) => {
  let dmrItemId = keyToDmrItemId?.[key];
  if (isEmptyValues(dmrItemId)) {
    return {
      key: key,
      actualKeys: [],
      normal: true,
    };
  } else {
    return {
      key: dmrItemId,
      actualKeys: customDiffKeysMap?.[dmrItemId],
      diff: keyToDiffMethod?.[dmrItemId],
      normal: false,
    };
  }
};

export const diffCardInfoWithPreCard = (
  currentDmrCardInfo: any,
  readonlyCardInfo: any,
) => {
  let excludeKeys = ['preCheckRules', 'requiredKeys', 'formEdited', 'Coder'];

  Object.keys(currentDmrCardInfo)
    ?.filter((key) => {
      return !excludeKeys.includes(key);
    })
    .forEach((key) => {
      let diffResult = [];
      let value = currentDmrCardInfo[key];

      let keyIdentifierResult = keyIdentifier(key);
      if (keyIdentifierResult?.normal === true) {
        if (Array.isArray(value)) {
          diffResult = Diff.diffArrays(
            readonlyCardInfo[key] ?? [],
            value ?? [],
          );
        } else if (typeof value === 'object' && value !== null) {
          diffResult = Diff.diffJson(readonlyCardInfo[key] ?? {}, value ?? {});
        } else {
          diffResult = Diff.diffLines(
            (readonlyCardInfo[key] ?? '')?.toString(),
            (value ?? '')?.toString(),
            { ignoreCase: true },
          );
        }
      } else {
        diffResult = keyIdentifierResult?.diff(
          pick(currentDmrCardInfo, keyIdentifierResult?.actualKeys),
          pick(readonlyCardInfo, keyIdentifierResult?.actualKeys),
          keyIdentifierResult?.key,
        );
      }

      console.log(
        'diffResult',
        diffResult,
        value,
        readonlyCardInfo[key],
        keyIdentifierResult,
        key,
      );

      if (key?.toLowerCase()?.endsWith('table')) {
        // 表示是 表格 用表格的标记方法
        highlightInTable(key, diffResult);
      } else {
        if (
          diffResult?.filter((item) => item?.added || item?.removed)?.length > 0
        ) {
          // 标记 当前格子 和 另一个格子
          let itemId = keyIdentifierResult?.key ?? key;

          if (isFeeItemKey(itemId)) {
            let dmrItem = document.querySelector(
              `.grid-stack-item div[id=FeeItem-${itemId}]`,
            );
            let readonlyItem = document.querySelector(
              `div[id='Readonly-FeeItem#${itemId}']`,
            );
            dmrItem?.classList?.add('dmr-diff-container');
            readonlyItem?.parentElement?.classList?.add(
              'readonly-diff-container',
            );
          } else {
            let dmrItem = document.querySelector(`.grid-stack-item#${itemId}`);
            let readonlyItem = document.querySelector(
              `div[id='Readonly-formItem#${itemId}']`,
            );
            dmrItem?.classList?.add('dmr-diff-container');
            readonlyItem?.parentElement?.classList?.add(
              'readonly-diff-container',
            );
          }
        }
      }
    });
};

export const diffCardInEditing = (
  editingValues: any,
  readonlyCardInfo: any,
) => {
  let excludeKeys = ['preCheckRules', 'requiredKeys', 'formEdited', 'Coder'];

  Object.keys(editingValues)
    ?.filter((key) => {
      return !excludeKeys.includes(key);
    })
    .forEach((key) => {
      let diffResult = [];
      let value = editingValues[key];

      let keyIdentifierResult = keyIdentifier(key);
      if (keyIdentifierResult?.normal === true) {
        if (Array.isArray(value)) {
          diffResult = Diff.diffArrays(
            readonlyCardInfo[key] ?? [],
            value ?? [],
          );
        } else if (typeof value === 'object' && value !== null) {
          diffResult = Diff.diffJson(readonlyCardInfo[key] ?? {}, value ?? {});
        } else {
          diffResult = Diff.diffLines(
            (readonlyCardInfo[key] ?? '')?.toString(),
            (value ?? '')?.toString(),
            { ignoreCase: true },
          );
        }
      } else {
        diffResult = keyIdentifierResult?.diff(
          pick(editingValues, keyIdentifierResult?.actualKeys),
          pick(readonlyCardInfo, keyIdentifierResult?.actualKeys),
          keyIdentifierResult?.key,
        );
      }

      if (key?.toLowerCase()?.endsWith('table')) {
        // 表示是 表格 用表格的标记方法
        highlightInTable(key, diffResult, true, keyIdentifierResult?.key);
      } else {
        // 标记 当前格子 和 另一个格子
        let itemId = keyIdentifierResult?.key ?? key;

        let dmrItem = null;
        let readonlyItem = null;
        if (isFeeItemKey(itemId)) {
          dmrItem = document.querySelector(
            `.grid-stack-item div[id=FeeItem-${itemId}]`,
          );
          readonlyItem = document.querySelector(
            `div[id='Readonly-FeeItem#${itemId}']`,
          );
        } else {
          dmrItem = document.querySelector(`.grid-stack-item#${itemId}`);
          readonlyItem = document.querySelector(
            `div[id='Readonly-formItem#${itemId}']`,
          );
        }

        if (
          diffResult?.filter((item) => item?.added || item?.removed)?.length > 0
        ) {
          dmrItem?.classList?.add('dmr-diff-container');
          readonlyItem?.parentElement?.classList?.add(
            'readonly-diff-container',
          );
        } else {
          dmrItem?.classList?.remove('dmr-diff-container');
          readonlyItem?.parentElement?.classList?.remove(
            'readonly-diff-container',
          );
        }
      }
    });
};

export const highlightInTable = (
  key: string,
  diffResults: any[],
  needDelete?: boolean,
  extraContainerKey?: string,
) => {
  // Key 表示的是 首页的item id
  // diffResults 里面 有多套  有标记 到 列格子的 有标记到行的 分开做

  if (
    key === 'department-transfer-table' ||
    key === 'departmentTransferTable'
  ) {
    let hasDiff = false;

    for (const diffResultItem of diffResults) {
      if (
        diffResultItem?.diffResults?.filter(
          (item) => item?.add === true || item?.removed === true,
        )?.length > 0
      ) {
        hasDiff = true;
        break;
      }
    }

    //标记
    let dmrItem = document.querySelector(`.grid-stack-item#TransDept`);
    let readonlyItem = document.querySelector(
      `div[id='Readonly-formItem#TransDept']`,
    );

    if (hasDiff === true) {
      dmrItem?.classList?.add('dmr-diff-container');
      readonlyItem?.parentElement?.classList?.add('readonly-diff-container');
    } else {
      dmrItem?.classList?.remove('dmr-diff-container');
      readonlyItem?.parentElement?.classList?.remove('readonly-diff-container');
    }

    return;
  }

  if (needDelete === true && !isEmptyValues(extraContainerKey)) {
    // 清除当前表格内的全部 包含 高亮css
    let dmrTableItem = document.querySelector(`#${extraContainerKey}`);
    let readonlyDmrTableItem = document.querySelector(
      `div[id='Readonly-formItem#${extraContainerKey}']`,
    );

    dmrTableItem?.querySelectorAll('.dmr-diff-container')?.forEach((item) => {
      item?.classList?.remove('dmr-diff-container');
    });
    readonlyDmrTableItem
      ?.querySelectorAll('.readonly-diff-container')
      ?.forEach((item) => {
        item?.classList?.remove('readonly-diff-container');
      });
  }

  diffResults?.forEach((diffResultItem: any) => {
    let dmrTableItem = document.querySelector(
      `#${diffResultItem?.tableItemId}`,
    );
    let readonlyDmrTableItem = document.querySelector(
      `div[id='Readonly-formItem#${diffResultItem?.tableItemId}']`,
    );

    if (isEmptyValues(diffResultItem?.dataType)) {
      if (!isEmptyValues(dmrTableItem)) {
        let tableLineItem = dmrTableItem?.querySelector(
          `tbody > tr:nth-child(${diffResultItem?.tableItemIndex + 1})`,
        );
        if (!isEmptyValues(tableLineItem)) {
          tableLineItem?.classList?.add('dmr-diff-container');
        }
      }

      if (!isEmptyValues(readonlyDmrTableItem)) {
        let readonlyTableLineItem = readonlyDmrTableItem?.querySelector(
          `tbody > tr:nth-child(${diffResultItem?.tableItemIndex + 2})`,
        );
        if (!isEmptyValues(readonlyTableLineItem)) {
          readonlyTableLineItem?.classList?.add('readonly-diff-container');
        }
      }
    } else {
      if (diffResultItem?.dataType === 'DMR') {
        if (!isEmptyValues(dmrTableItem)) {
          let tableLineItem = dmrTableItem?.querySelector(
            `tbody > tr:nth-child(${diffResultItem?.tableItemIndex + 1})`,
          );
          if (!isEmptyValues(tableLineItem)) {
            tableLineItem?.classList?.add('dmr-diff-container');
          }
        }
      }

      if (diffResultItem?.dataType === 'READONLY') {
        if (!isEmptyValues(readonlyDmrTableItem)) {
          let readonlyTableLineItem = readonlyDmrTableItem?.querySelector(
            `tbody > tr:nth-child(${diffResultItem?.tableItemIndex + 2})`,
          );
          if (!isEmptyValues(readonlyTableLineItem)) {
            readonlyTableLineItem?.classList?.add('readonly-diff-container');
          }
        }
      }
    }
  });
};
