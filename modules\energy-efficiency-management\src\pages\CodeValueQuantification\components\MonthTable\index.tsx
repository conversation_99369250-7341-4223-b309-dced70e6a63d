import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { API_ENDPOINTS } from '../../constants';
import _ from 'lodash';
import { generateTableHeaders } from '../../utils';
import { RespVO } from '@uni/commons/src/interfaces';
import { RedoOutlined } from '@ant-design/icons';
import { uniCommonService } from '@uni/services/src';
import { Card, Col, Divider, Row, Space, Tooltip } from 'antd';
import { ExportIconBtn, UniTable } from '@uni/components/src/index';
import { exportExcelByClaudeForCode } from '@uni/utils/src/excel-export';
import { processArray, calculateSumAndGenerateSummary } from './utils';

const iconStyle: React.CSSProperties = {
  width: '32px',
  height: '32px',
  lineHeight: '35px',
  cursor: 'pointer',
};

const key = 'Month_Data';

const MonthTable = ({ dictData, tableParams, metricData }) => {
  const [tableColumns, setTableColumns] = useState([]);

  const [allData, setAllData] = useState<undefined | {}>();

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
  });

  const frontTableOnChange = (pagination: any) => {
    setFrontPagination({
      ...frontPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleRefresh = () => () => {
    setFrontPagination({
      ...frontPagination,
      current: 1,
    });
    getStatsChangedReq('Month_Data', {
      BasicArgs: {
        ...tableParams,
      },
      GrouperCols: [],
    });
  };

  const {
    data: statsChangedData,
    loading: getStatsChangedLoading,
    run: getStatsChangedReq,
    fetches: fetchesStatsChanged,
  } = useRequest(
    (id, data) =>
      uniCommonService(API_ENDPOINTS.Get_Stats_By_Month, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data) {
            let list = res.data?.data || [];
            list = processArray(list);
            if (list.length) {
              const lastItem = calculateSumAndGenerateSummary(list);
              setAllData(lastItem);
            }
            return list;
          }
        }
        return [];
      },
    },
  );

  useEffect(() => {
    setFrontPagination({
      ...frontPagination,
      current: 1,
    });
    const data = tableParams;
    if (data && Object.keys(data)?.length) {
      getStatsChangedReq('Month_Data', {
        BasicArgs: {
          ...tableParams,
        },
        GrouperCols: [],
      });
    }
  }, [tableParams]);

  useEffect(() => {
    if (metricData) {
      const sortedData = _.orderBy(metricData, [
        (item) => (item.MenuSort === 0 ? 1 : 0),
        'MenuSort',
      ]);

      const baseColumns = generateTableHeaders(sortedData, {
        isFromDrawer: false,
        filterMetric: '',
        nestedCliDept: '',
      });

      setTableColumns([
        {
          dataIndex: 'Month',
          title: '月份',
          visible: true,
          dictionaryModuleGroup: 'Dmr',
          dictionaryModule: 'Month',
          exportable: true,
          fixed: 'left',
        },
        ...baseColumns,
      ]);
    }
  }, [metricData]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <Card
          title="月度工作量统计"
          extra={
            <Space>
              <Divider type="vertical" />
              <Tooltip title="刷新">
                <RedoOutlined
                  className="refresh-icon"
                  style={iconStyle}
                  onClick={handleRefresh()}
                  onPointerEnterCapture={() => {}}
                  onPointerLeaveCapture={() => {}}
                />
              </Tooltip>
              <ExportIconBtn
                isBackend={false}
                frontendObj={{
                  columns: [...tableColumns],
                  dataSource: allData
                    ? statsChangedData?.concat([allData])
                    : statsChangedData,
                  dictionaryData: dictData,
                  fileName: '月度工作量统计',
                  customExportFunc: exportExcelByClaudeForCode,
                }}
                btnDisabled={fetchesStatsChanged?.[key]?.data?.length < 1}
              />
            </Space>
          }
        >
          <UniTable
            rowKey={'OutDate'}
            id={`month-workload-statistics`}
            bordered
            columns={[...tableColumns]}
            loading={fetchesStatsChanged?.[key]?.loading ?? false}
            dataSource={statsChangedData}
            forceColumnsUpdate
            widthDetectAfterDictionary
            dictionaryData={dictData}
            scroll={{ x: 'max-content' }}
            pagination={frontPagination}
            onChange={frontTableOnChange}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default MonthTable;
