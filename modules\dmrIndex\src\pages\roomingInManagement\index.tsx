import React, { useEffect, useRef, useState } from 'react';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { useLocation, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import {
  BasePageProps,
  ColumnItem,
  RespVO,
  TableCardResp,
  TableColumns,
  TableResp,
} from '@uni/commons/src/interfaces';
import {
  Button,
  Card,
  message,
  Modal,
  TableProps,
  Row,
  Col,
  Tabs,
  Space,
  notification,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { DmrCardOperation, DmrEventConstant } from '@/utils/constants';
import {
  batchLockDmrCard,
  batchReviewDmrCard,
  batchUnLockDmrCard,
  destroyDmrCard,
  lockDmrCard,
  reviewDmrCard,
  unlockDmrCard,
} from '@/pages/management/service';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import SearchForm from '@/components/searchForm';
import { SearchOpts } from './formItems';
import { useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import {
  dataTableFilterSorterHandler,
  isEmptyObj,
  toArrayMode,
} from '@/utils/utils';
import { SorterResult } from 'antd/lib/table/interface';

import './index.less';
import { roomingInManagementColumns } from './columns';
import { SwagDmrCardIgnoreItem } from './interface';
import { OperationApiUrl } from './constants';
import MenuDivider from 'antd/es/menu/MenuDivider';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';

const headerSearchForceModified =
  (window as any).externalConfig?.['common']?.headerSearchForceModified ??
  false;

const RoomingInManagement = () => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState({
    ..._.omit(searchParams, 'hospCode', 'hospCodes'),
    Sdate: searchParams?.dateRange
      ? dayjs(searchParams?.dateRange[0]).startOf('M').format('YYYY-MM-DD')
      : undefined,
    Edate: searchParams?.dateRange
      ? dayjs(searchParams?.dateRange[1]).endOf('M').format('YYYY-MM-DD')
      : undefined,
    HospCode: toArrayMode(searchParams?.hospCode ?? searchParams?.hospCodes),
  });

  const [cardsColumns, setCardColumns] = useState([]);
  const [cardTableDataSource, setCardTableDataSource] = useState([]);
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // columns
  const { data: originalColumns, run: cardColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardIgnore/GetCards', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          return response?.data?.Columns;
        } else {
          return [];
        }
      },
      onSuccess(data, params) {
        setCardColumns(
          tableColumnBaseProcessor(roomingInManagementColumns, data),
        );
      },
    },
  );
  // table数据
  const { loading: cardLoading, run: cardReq } = useRequest(
    (current, pageSize, otherData = {}) => {
      let data = {
        skipFilterSorterMiddleware: true,
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
          ...otherData,
        },
        ...searchedValue,
      };

      return uniCommonService('Api/Dmr/DmrCardIgnore/GetCards', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (
        response: RespVO<TableCardResp<any, SwagDmrCardIgnoreItem>>,
      ) => {
        if (response.code === 0) {
          setCardTableDataSource(response?.data?.data);
          setBackPagination({
            ...backPagination,
            total:
              response?.data?.recordsFiltered ||
              response?.data?.recordsTotal ||
              response?.data?.data?.length ||
              0,
          });
        } else {
          setCardTableDataSource([]);
          setBackPagination({
            ...backPagination,
            current: 1,
            total: 0,
          });
        }
      },
    },
  );
  // operation apiReq
  const { loading: operationLoading, run: operationReq } = useRequest(
    (data, apiUrl) => {
      return uniCommonService(apiUrl, {
        method: 'POST',
        data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableCardResp<any, any>>) => {
        if (response.code === 0) {
          cardReq(backPagination?.current, backPagination?.pageSize);
          return response;
        }
      },
      onSuccess: (data, params) => {
        if (data.code === 0) {
          const key = `open${Date.now()}`;
          notification['success']({
            key,
            message: `登记成功`,
            description:
              '你可以点击这里，直接打开病案首页页面查看该病案的相关数据',
            btn: (
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  // window.open(
                  //   `/dmr/index?hisId=${encodeURIComponent(params[0].HisId)}`,
                  // );
                  // global['tabAwareWorker'].port.postMessage({
                  //   type: 'DMR_VIEW_CLICK',
                  //   payload: {
                  //     hisId: encodeURIComponent(params[0].HisId),
                  //   },
                  // });
                  (global?.window as any)?.eventEmitter?.emit(
                    'DMR_AOD_STATUS',
                    {
                      status: true,
                      hisId: params[0].HisId,
                    },
                  );
                  notification.close(key);
                }}
              >
                确认打开
              </Button>
            ),
            onClose: close,
          });
        }
      },
    },
  );

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    cardReq(
      pagi.current,
      pagi.pageSize,
      dataTableFilterSorterHandler(
        originalColumns,
        filters,
        sorter as SorterResult<any>,
      ),
    );
  };

  const onCardOperation = (
    record: SwagDmrCardIgnoreItem,
    operation: string,
    index: number,
  ) => {
    switch (operation) {
      case DmrCardOperation.ACTIVE:
        if (record) {
          operationReq({ HisId: record?.HisId }, OperationApiUrl.ActivateCard);
        }
        break;

      default:
        break;
    }
  };

  useEffect(() => {
    // 事件监听
    Emitter.on(DmrEventConstant.DMR_ROOMINGIN_MANAGEMENT_OPERATION, (data) => {
      console.log(data?.record, data?.type, data?.index);
      onCardOperation(data?.record, data?.type, data?.index);
    });

    return () => {
      Emitter.off(DmrEventConstant.DMR_ROOMINGIN_MANAGEMENT_OPERATION);
    };
  }, []);

  return (
    <div className={'dmr-management-container'}>
      <Row wrap={false} gutter={16}>
        <Col flex="330px">
          <Card title="查询条件" className="dmr-management-search-card">
            <SearchForm
              classNames={['noMarginBottom']}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              searchOpts={SearchOpts(searchedValue, {
                hospOpts: dictData.Hospital,
                deptOpts: dictData.CliDepts,
                wardOpts: dictData?.['Dmr']?.Wards,
              })}
              onFinish={async (values) => {
                console.log(values);
                setSearchedValue({
                  ...values,
                });
                (global?.window as any)?.eventEmitter?.emit(
                  EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
                  {
                    ..._.omit(values, 'SearchKeyWord'),
                    searchNow: headerSearchForceModified === false,
                  },
                );

                setTimeout(() => {
                  cardReq(1, backPagination?.pageSize);
                }, 0);
              }}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{ width: '100%', marginTop: '8px' }}
                      key="reset"
                      onClick={() => props.form?.submit?.()}
                    >
                      查询
                    </Button>,
                  ];
                },
              }}
            />
          </Card>
        </Col>
        <Col flex="auto">
          <Card
            title="母婴同室列表"
            extra={
              <Space>
                <ExportIconBtn
                  isBackend={true}
                  backendObj={{
                    url: 'Api/Dmr/DmrCardIgnore/ExportGetCards',
                    method: 'POST',
                    data: {
                      skipFilterSorterMiddleware: true,
                      ...searchedValue,
                    },
                    fileName: '母婴同室列表',
                  }}
                  btnDisabled={cardTableDataSource?.length < 1}
                />
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Dmr/DmrCardIgnore/GetCards',
                    onTableRowSaveSuccess: (columns) => {
                      setCardColumns(
                        tableColumnBaseProcessor(
                          roomingInManagementColumns,
                          columns,
                        ),
                      );
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id={'dmr-rooming-in-management-card-table'}
              rowKey={'Id'}
              dictionaryData={dictData}
              scroll={{ x: 'max-content' }}
              loading={cardLoading || operationLoading}
              columns={cardsColumns}
              dataSource={cardTableDataSource}
              pagination={backPagination}
              onChange={backTableOnChange}
              toolBarRender={null}
              isBackPagination
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RoomingInManagement;
