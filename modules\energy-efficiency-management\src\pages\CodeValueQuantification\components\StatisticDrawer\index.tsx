import React, { useEffect, useState } from 'react';
import { Drawer } from 'antd';
import TableStatistic from '../TableStatistic';
import { ELEMENT_TYPES } from '../../constants';
import { Emitter } from '@uni/utils/src/emitter';
import { CodeValueQuantificationEventConstants } from '../../constants';

interface DrilldownData {
  open: boolean;
  record: any;
  selectedKey: string;
}

export interface StatisticDrawerProps {
  open: boolean;
  onClose: () => void;
  metricData: any[];
  dictData: any;
  record: any;
  selectedKey: string;
  groupItems: any[];
  tableParams: any;
}

const StatisticDrawer: React.FC<StatisticDrawerProps> = (props) => {
  const {
    open,
    onClose,
    metricData,
    dictData,
    record,
    selectedKey,
    groupItems,
    tableParams,
  } = props;

  const [nestedDrawer, setNestedDrawer] = useState<DrilldownData>({
    open: false,
    record: null,
    selectedKey: '',
  });
  // 获取抽屉标题
  const getDrawerTitle = () => {
    let parts = '统计信息';
    if (record?.MenuDirectories?.[0]) {
      // 只取前两层作为标题
      parts = record.MenuDirectories[0]
        .split('/')
        ?.filter(Boolean)
        ?.slice(0, 2)
        ?.join(' - ');
    }
    return parts;
  };

  // nested标题
  const getNestedDrawerTitle = () => {
    let parts = getDrawerTitle();
    // nestedDrawerRecord 拼接 只有科室的情况
    if (nestedDrawer?.record?.CliDept) {
      const cliDeptName = dictData?.['Dmr']?.['CliDepts']?.find(
        (item) => item?.Code === nestedDrawer?.record?.CliDept,
      )?.Name;
      if (cliDeptName) {
        // 如果有科室名称，则拼接到标题后面
        parts += ` - ${cliDeptName}`;
      } else {
        parts += ` - ${nestedDrawer?.record?.CliDept}`;
      }
    }
    return parts;
  };

  // 订阅事件监听器
  useEffect(() => {
    const handleDrilldown = (event: DrilldownData) => {
      setNestedDrawer({
        open: event.open,
        record: event.record,
        selectedKey: event.selectedKey,
      });
    };

    // 添加事件监听
    Emitter.on(
      CodeValueQuantificationEventConstants.STAT_NESTED_DRILLDOWN_CLICK,
      handleDrilldown,
    );

    // 清理事件监听
    return () => {
      Emitter.off(
        CodeValueQuantificationEventConstants.STAT_NESTED_DRILLDOWN_CLICK,
      );
    };
  }, []);

  // 主要指标名称，优先使用 MetricName，如果没有则使用 ColumnName
  const filterMetric = record?.MetricName || record?.ColumnName;

  if (!filterMetric) {
    console.warn('No MetricName or ColumnName found in record:', record);
  }

  // Map which key needs which boolean flag
  const needFlags = {
    [ELEMENT_TYPES.CODER]: selectedKey === ELEMENT_TYPES.CODER,
    [ELEMENT_TYPES.CLIDEPT]: selectedKey === ELEMENT_TYPES.CLIDEPT,
    [ELEMENT_TYPES.CHIEF]: selectedKey === ELEMENT_TYPES.CHIEF,
    [ELEMENT_TYPES.MEDTEAM]: selectedKey === ELEMENT_TYPES.MEDTEAM,
    [ELEMENT_TYPES.MAJOR_PERF_DEPT]:
      selectedKey === ELEMENT_TYPES.MAJOR_PERF_DEPT,
  };

  const handleNestedDrawerClose = () => {
    setNestedDrawer((prev) => ({ open: false, record: null, selectedKey: '' }));
  };

  return (
    <>
      <Drawer
        title={getDrawerTitle()}
        placement="right"
        width={'1200px'}
        onClose={onClose}
        destroyOnClose={true}
        open={open}
        zIndex={97}
      >
        <TableStatistic
          metricData={metricData}
          dictData={dictData}
          tableParams={tableParams}
          groupItems={groupItems}
          isFromDrawer={true}
          filterMetric={filterMetric}
          needCoder={needFlags[ELEMENT_TYPES.CODER]}
          needCliDept={needFlags[ELEMENT_TYPES.CLIDEPT]}
          needChief={needFlags[ELEMENT_TYPES.CHIEF]}
          needMedTeam={needFlags[ELEMENT_TYPES.MEDTEAM]}
          needMajorPerfDept={needFlags[ELEMENT_TYPES.MAJOR_PERF_DEPT]}
        />
        {/* 嵌套的抽屉 目前必定是医疗组 */}
        <Drawer
          title={getNestedDrawerTitle()}
          placement="right"
          width={'1200px'}
          destroyOnClose={true}
          open={nestedDrawer.open}
          onClose={handleNestedDrawerClose}
          zIndex={98}
        >
          <TableStatistic
            metricData={metricData}
            dictData={dictData}
            tableParams={tableParams}
            groupItems={groupItems}
            isFromDrawer={true}
            filterMetric={filterMetric}
            nestedCliDept={nestedDrawer.record?.CliDept}
            needMedTeam={true}
          />
        </Drawer>
      </Drawer>
    </>
  );
};

export default StatisticDrawer;
