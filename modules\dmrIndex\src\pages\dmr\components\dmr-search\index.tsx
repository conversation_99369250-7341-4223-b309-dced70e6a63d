import Datepicker from '@uni/components/src/picker/datepicker';
import { UniTable } from '@uni/components/src';
import {
  Badge,
  Button,
  Card,
  Col,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  TableProps,
  Tabs,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDebounceFn } from 'ahooks';
import { history, useLocation, useModel, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import {
  ColumnItem,
  RespVO,
  TableCardResp,
  TableColumns,
} from '@uni/commons/src/interfaces';
import './index.less';
import { dmrSearchColumns } from '@/pages/dmr/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { DmrSearchTableItem } from './interface';
import {
  tableColumnBaseProcessor,
  tableSorterProcessor,
} from '@uni/utils/src/tableColumnProcessor';
import dayjs from 'dayjs';
import UniDmrSelect from '@uni/grid/src/components/dmr-select/UniDmrSelect';
import locale from 'antd/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { dmrSearchParamProcessor } from '@/pages/dmr/components/dmr-search/processor';
import qs from 'qs';
import { AlertOutlined, DoubleLeftOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { isEmptyValues } from '@uni/utils/src/utils';
import { isEmpty } from 'lodash';
import { useInsurSeparateTableLogic } from '../icde-insur-separate-table/hooks/useInsurSeparateTableLogic';
import { isMedicareWithPermission } from '../../utils';

// 病案室员工
const searchStatusOptions = [
  {
    label: '全部',
    value: '0',
  },
  {
    label: '未登记',
    value: '1',
    icon: (
      <span className="font-warning">
        <AlertOutlined />
      </span>
    ),
  },
  {
    label: '已登记',
    value: '2',
    icon: (
      <span className="font-success">
        <AlertOutlined />
      </span>
    ),
  },
  // {
  //   label: '审核有误',
  //   value: '999',
  // },
];

// 医保人员
const insurSearchStatusOptions = [
  {
    label: '全部',
    value: '0',
  },
  {
    label: '未登记',
    value: '2-todo-medicare',
    icon: (
      <span className="font-warning">
        <AlertOutlined />
      </span>
    ),
  },
  {
    label: '已登记',
    value: '2-done-medicare',
    icon: (
      <span className="font-success">
        <AlertOutlined />
      </span>
    ),
  },
  // {
  //   label: '审核有误',
  //   value: '999',
  // },
];

const cardSummaries = [
  // RegisterStatusAllCnt
  // RegisterStatusRegisteredCnt
  // RegisterStatusReviewFailedCnt
  // RegisterStatusUnUpdatedCnt
  {
    label: '总数量',
    key: 'RegisterStatusAllCnt',
    status: '0',
  },
  {
    label: '未登记',
    key: 'RegisterStatusUnUpdatedCnt',
    status: '1',
  },
  {
    label: '已登记',
    key: 'RegisterStatusRegisteredCnt',
    status: '2',
  },
  {
    label: '审核有误',
    key: 'RegisterStatusReviewFailedCnt',
    status: '999',
  },
];

interface DmrSearchProps {
  containerRef: any;
  visible?: boolean;
  onDmrCardChangeInterceptor?: () => Promise<boolean>;
  viewMode?: boolean;

  onRightArrowClick: (status: boolean) => void;
}

const externalDmrConfig = (window as any).externalConfig?.['dmr'];
const dmrSearchParamLabels = externalDmrConfig?.['dmrSearchParamLabels'];

export const DmrSearch = (props: DmrSearchProps) => {
  const [dmrTableDataSource, setDmrTableDataSource] = useState<
    DmrSearchTableItem[]
  >([]);

  const [dmrCheckedTableDataSource, setDmrCheckedTableDataSource] = useState<
    DmrSearchTableItem[]
  >([]);

  const location = useLocation();

  const [dmrTableColumns, setDmrTableColumns] = useState<ColumnItem[]>([]);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const userRoleLogic = useInsurSeparateTableLogic();

  // 导入外部配置
  const externalDmrConfig = (window as any).externalConfig?.['dmr'];
  const dmrSearchParamLabels = externalDmrConfig?.['dmrSearchParamLabels'];

  const [selectedDmrSearchItem, setSelectedDmrSearchItem] = useState(undefined);
  const [selectedCheckedDmrSearchItem, setSelectedCheckedDmrSearchItem] =
    useState(undefined);

  const [dmrNextNeedPagination, setDmrNextNeedPagination] = useState(false);

  const [dmrSearchParams, setDmrSearchParams] = useState<any>({
    Sdate:
      dayjs().date() > 15
        ? dayjs().startOf('month').format('YYYY-MM-DD')
        : dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    Edate:
      dayjs().date() > 15
        ? dayjs().endOf('month').format('YYYY-MM-DD')
        : dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    CustomRegisterStatus: '0',
  });

  const [dmrCardsSummary, setDmrCardsSummary] = useState<any>({});

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
    showSizeChanger: false,
    size: 'small',
  });

  const [searchTableSorter, setSearchTableSorter] = useState(undefined);

  const [checkedBackPagination, setCheckedBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 50,
    pageSizeOptions: ['10', '20', '30', '50'],
    hideOnSinglePage: false,
    showSizeChanger: false,
    size: 'small',
  });

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      getCurrentHisIdPreviousHisId: async (currentHisId: string) => {
        let currentHisIdIndex = dmrTableDataSource?.findIndex((item) => {
          return item?.HisId === currentHisId;
        });

        if (currentHisIdIndex === 0) {
          if (backPagination?.current !== 1) {
            // 实时拿上一页
            let currentPageDmrDataResponse = await dmrSearchReq(
              backPagination?.current - 1,
              backPagination.pageSize,
              searchTableSorter,
            );
            setBackPagination({
              ...backPagination,
              current: backPagination?.current - 1,
            });
            let currentPageDmrHisIds = currentPageDmrDataResponse?.data?.map(
              (item) => item?.HisId,
            );
            if (!isEmptyValues(currentPageDmrHisIds)) {
              setTimeout(() => {
                Emitter.emit(EventConstant.TABLE_ROW_CLICK, {
                  record:
                    currentPageDmrDataResponse?.data?.[
                      currentPageDmrHisIds.length - 1
                    ],
                  index: 0,
                });
              }, 0);
              return currentPageDmrHisIds[currentPageDmrHisIds.length - 1];
            }
          }
        } else {
          setTimeout(() => {
            Emitter.emit(EventConstant.TABLE_ROW_CLICK, {
              record: dmrTableDataSource?.[currentHisIdIndex - 1],
              index: 0,
            });
          }, 0);
          return dmrTableDataSource?.[currentHisIdIndex - 1]?.HisId;
        }
        return undefined;
      },
    };
  });

  const [checkedTotal, setCheckedTotal] = useState(0);

  const [activeKey, setActiveKey] = useState('DmrSearch');

  useEffect(() => {
    dmrSearchColumnsReq();

    // 只能这样 先行捞一份出来
    dmrCheckedTotalReq();
  }, []);

  // table click
  useEffect(() => {
    Emitter.on(EventConstant.TABLE_ROW_CLICK, async ({ record, index }) => {
      // TODO 判定是不是改了东西
      if (props?.onDmrCardChangeInterceptor) {
        let result = await props?.onDmrCardChangeInterceptor();
        if (result) {
          return;
        }
      }

      setSelectedDmrSearchItem(record);
      if (record?.HisId) {
        let dmrParam = {
          hisId: record?.HisId,
        };
        if (dmrSearchParams?.CustomRegisterStatus?.toString() === '999') {
          dmrParam['instantAudit'] = true;
        }
        history.replace(`${location?.pathname}?${qs.stringify(dmrParam)}`);
        Emitter.emit(EventConstant.DMR_DRAWER_CHANGE, false);
        Emitter.emit(
          EventConstant.DMR_SEARCH_TABLE_HISIDS,
          dmrTableDataSource
            ?.filter((item) => item?.HisId)
            ?.map((item) => item?.HisId),
        );
      } else {
        message.error('HisId不存在');
      }
    });
    return () => {
      Emitter.off(EventConstant.TABLE_ROW_CLICK);
    };
  }, [dmrSearchParams, dmrTableDataSource, props?.viewMode]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_NEXT, async (hisId) => {
      let currentRecord = dmrTableDataSource?.find(
        (item) => item?.HisId === hisId,
      );
      if (currentRecord) {
        setSelectedDmrSearchItem(currentRecord);
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_NEXT);
    };
  }, [dmrTableDataSource]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_NEXT_PAGE, async () => {
      let current = backPagination?.current + 1;
      if (dmrSearchParams?.CustomRegisterStatus === '1') {
        // 先行拿第一页出来看一下 到底是不是在当前页出现过 没出现过 就下一页
        //
        let currentPageDmrDataResponse = await dmrSearchReq(
          1,
          backPagination.pageSize,
          searchTableSorter,
        );
        let currentPageDmrHisIds = currentPageDmrDataResponse?.data?.map(
          (item) => item?.HisId,
        );
        let existPageDmrHisIds = dmrTableDataSource?.map((item) => item?.HisId);
        const hasIntersection = currentPageDmrHisIds.some((item) =>
          existPageDmrHisIds.includes(item),
        );
        if (hasIntersection === false) {
          current = 1;
        }
      }

      setDmrNextNeedPagination(true);

      backTableOnChange(
        {
          current: current,
          pageSize: backPagination?.pageSize,
        },
        null,
        searchTableSorter,
        null,
      );
    });

    return () => {
      Emitter.off(EventConstant.DMR_NEXT_PAGE);
    };
  }, [backPagination, dmrSearchParams, searchTableSorter, dmrTableDataSource]);

  const { run: dmrSearchColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setDmrTableColumns(
            tableColumnBaseProcessor(dmrSearchColumns, response?.data?.Columns),
          );
        } else {
          setDmrTableColumns([]);
        }
      },
    },
  );

  const { run: dmrSearchReqWithDebounce } = useDebounceFn(
    (current, pageSize, sorter) => {
      dmrSearchReq(current, pageSize, sorter);
    },
    {
      wait: 500,
    },
  );

  const { loading: dmrSearchLoading, run: dmrSearchReq } = useRequest(
    (current, pageSize, sorter) => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        ...dmrSearchParamProcessor(dmrSearchParams),
      };

      if (sorter) {
        global['tableParameters'] = {
          ...global['tableParameters'],
          sorter: tableSorterProcessor(sorter),
        };
      }

      return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (
        response: RespVO<TableCardResp<any, DmrSearchTableItem>>,
      ) => {
        if (response.code === 0) {
          setDmrTableDataSource(response?.data?.data);

          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsTotal || 0,
          });

          if (dmrNextNeedPagination) {
            setDmrNextNeedPagination(false);
            if (response?.data?.data?.length > 0) {
              setTimeout(() => {
                Emitter.emit(EventConstant.TABLE_ROW_CLICK, {
                  record: response?.data?.data?.at(0),
                  index: 0,
                });
              }, 0);

              document
                .querySelector('#dmr-search-content .ant-table-body')
                .scrollTo({
                  top: 0,
                  behavior: 'smooth',
                });
            }
          }

          // if(response?.data?.recordsFiltered === response?.data?.recordsTotal) {
          //   // 表示最后一个
          //   Emitter.emit(EventConstant.DMR_RECORD_LAST_PAGE);
          // }

          setDmrCardsSummary({
            RegisterStatusAllCnt: response?.data?.RegisterStatusAllCnt,
            RegisterStatusUnUpdatedCnt:
              response?.data?.RegisterStatusUnUpdatedCnt,
            RegisterStatusRegisteredCnt:
              response?.data?.RegisterStatusRegisteredCnt,
            RegisterStatusReviewFailedCnt:
              response?.data?.RegisterStatusReviewFailedCnt,
          });
        } else {
          setDmrTableDataSource([]);
        }

        return response?.data;
      },
      onSuccess: (response, params) => {
        let lastPage =
          response?.recordsTotal <=
          (params?.at(0) - 1) * params?.at(1) + params?.at(1);
        // 表示最后一个
        Emitter.emit(EventConstant.DMR_RECORD_LAST_PAGE, lastPage);
      },
    },
  );

  const { loading: dmrCheckedSearchLoading, run: dmrCheckedSearchReq } =
    useRequest(
      (current, pageSize) => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          UseRegistDate: true,
          CodeSdate: dayjs().format('YYYY-MM-DD 00:00:00'),
          CodeEdate: dayjs().format('YYYY-MM-DD 23:59:59'),
        };

        if (
          isEmptyValues(global['tableParameters']?.sorter) ||
          isEmptyValues(global['tableParameters']?.sorter?.order)
        ) {
          global['tableParameters'] = {
            sorter: {
              columnKey: 'CodeTime',
              field: 'CodeTime',
              order: 'descend',
            },
          };
        }

        return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableCardResp<any, DmrSearchTableItem>>,
        ) => {
          if (response.code === 0) {
            setDmrCheckedTableDataSource(response?.data?.data);

            setCheckedBackPagination({
              ...checkedBackPagination,
              total: response?.data?.recordsTotal || 0,
            });

            setCheckedTotal(response?.data?.recordsTotal ?? 0);
          } else {
            setDmrTableDataSource([]);
          }

          return response?.data;
        },
        onSuccess: (response, params) => {
          let lastPage =
            response?.recordsTotal <=
            (params?.at(0) - 1) * params?.at(1) + params?.at(1);
          // 表示最后一个
          Emitter.emit(EventConstant.DMR_RECORD_LAST_PAGE, lastPage);
        },
      },
    );

  const { loading: dmrCheckedTotalLoading, run: dmrCheckedTotalReq } =
    useRequest(
      () => {
        let data = {
          skipFilterSorterMiddleware: true,
          DtParam: {
            Draw: 1,
            Start: 1,
            Length: 1,
          },
          UseRegistDate: true,
          CodeSdate: dayjs().format('YYYY-MM-DD 00:00:00'),
          CodeEdate: dayjs().format('YYYY-MM-DD 23:59:59'),
        };

        return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableCardResp<any, DmrSearchTableItem>>,
        ) => {
          setCheckedTotal(response?.data?.recordsTotal ?? 0);
        },
      },
    );

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    setSearchTableSorter(sorter);

    dmrSearchReq(pagi.current, pagi.pageSize, sorter);
  };

  const dmrCheckedBackTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setCheckedBackPagination({
      ...checkedBackPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    dmrCheckedSearchReq(pagi.current, pagi.pageSize);
  };

  const renderDmrSearchContent = () => {
    return (
      <div id={'dmr-search-content'} className="dmr-search-content">
        <div className={'search-items-container'}>
          <Row gutter={[10, 10]}>
            <Col span={12}>
              <div className={'search-item'}>
                <label>{dmrSearchParamLabels?.['OutDate'] || '出院日期'}</label>
                <div className={'search-item-content-container'}>
                  <Datepicker.RangePicker
                    size={'small'}
                    style={{ flex: 1 }}
                    locale={locale}
                    picker={'date'}
                    allowClear={true}
                    value={[
                      dmrSearchParams?.Sdate
                        ? dayjs(dmrSearchParams?.Sdate)
                        : undefined,
                      dmrSearchParams?.Edate
                        ? dayjs(dmrSearchParams?.Edate)
                        : undefined,
                    ]}
                    showTime={false}
                    getPopupContainer={(trigger) =>
                      document.getElementById('dmr-root-container')
                    }
                    format={'YYYY-MM-DD'}
                    onChange={(dates, dateStrings) => {
                      setDmrSearchParams({
                        ...dmrSearchParams,
                        Sdate: dateStrings?.at(0),
                        Edate: dateStrings?.at(1),
                      });
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div className={'search-item'}>
                <UniDmrSelect
                  size={'small'}
                  label={dmrSearchParamLabels?.['HospCode'] || '院区'}
                  modelDataKey={'Hospital'}
                  className={'selector-container'}
                  // value={}
                  placeholder="请选择院区"
                  showSearch
                  optionNameKey={'Name'}
                  optionValueKey={'Code'}
                  optionTitleKey={'Name'}
                  allowClear={true}
                  getPopupContainer={(trigger) =>
                    document.getElementById('dmr-search-content')
                  }
                  onChange={(value) => {
                    dmrSearchParams['HospCode'] = value;
                    if (isEmptyValues(value)) {
                      delete dmrSearchParams['HospCode'];
                    }
                    setDmrSearchParams(dmrSearchParams);
                  }}
                />
              </div>
            </Col>
          </Row>
          <Row gutter={[10, 10]}>
            <Col span={12}>
              <div className={'search-item'}>
                <UniDmrSelect
                  size={'small'}
                  mode={'multiple'}
                  label={dmrSearchParamLabels?.['CliDepts'] || '科室'}
                  modelDataKey={'CliDepts'}
                  className={'selector-container'}
                  // value={}
                  placeholder="请选择科室"
                  showSearch
                  optionNameKey={'Name'}
                  optionValueKey={'Code'}
                  optionTitleKey={'Name'}
                  getPopupContainer={(trigger) =>
                    document.getElementById('dmr-search-content')
                  }
                  allowClear={true}
                  onChange={(value) => {
                    dmrSearchParams['CliDepts'] = value;
                    if (isEmptyValues(value)) {
                      delete dmrSearchParams['CliDepts'];
                    }
                    setDmrSearchParams(dmrSearchParams);
                  }}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className={'search-item'}>
                <UniDmrSelect
                  size={'small'}
                  label={dmrSearchParamLabels?.['Coder'] || '编码员'}
                  className={'selector-container'}
                  modelDataKey={'Coder'}
                  // value={}
                  placeholder="请选择编码员"
                  showSearch
                  optionNameKey={'label'}
                  optionValueKey={'Code'}
                  optionTitleKey={'Name'}
                  optionLabelProp={'title'}
                  allowClear={true}
                  getPopupContainer={(trigger) =>
                    document.getElementById('dmr-search-content')
                  }
                  dataSourceProcessor={(dataSource) => {
                    return dataSource.map((item) => {
                      return {
                        ...item,
                        label: `${item.Code}：${item.Name}`,
                      };
                    });
                  }}
                  onChange={(value) => {
                    dmrSearchParams['Coder'] = [value];
                    if (isEmptyValues(value)) {
                      delete dmrSearchParams['Coder'];
                    }
                    setDmrSearchParams(dmrSearchParams);
                  }}
                />
              </div>
            </Col>
          </Row>
          <Row gutter={[10, 10]}>
            <Col span={12}>
              <div className={'search-item'}>
                <label>
                  {dmrSearchParamLabels?.['SearchKeyWord'] || '病案标识'}
                </label>
                <div className={'search-item-content-container'}>
                  <Input
                    size={'small'}
                    value={dmrSearchParams?.['SearchKeyWord']}
                    placeholder={'病案号/姓名/住院号/条形码'}
                    onChange={(event) => {
                      setDmrSearchParams({
                        ...dmrSearchParams,
                        SearchKeyWord: event.target.value,
                      });
                    }}
                  />
                </div>
              </div>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <div className={'search-item'}>
                <label>
                  {dmrSearchParamLabels?.['CustomRegisterStatus'] || '状态'}
                </label>
                <div className={'search-item-content-container'}>
                  <Radio.Group
                    size={'small'}
                    buttonStyle="outline"
                    value={dmrSearchParams?.CustomRegisterStatus}
                    onChange={(event) => {
                      setDmrSearchParams({
                        ...dmrSearchParams,
                        CustomRegisterStatus: event.target.value,
                      });
                    }}
                  >
                    {(isMedicareWithPermission(userRoleLogic.userRole)
                      ? insurSearchStatusOptions
                      : searchStatusOptions
                    ).map((item) => {
                      return (
                        <Radio.Button value={item.value}>
                          <span>
                            {item?.icon && (
                              <Tooltip title={item.label}>{item?.icon}</Tooltip>
                            )}
                            {item.label}
                          </span>
                        </Radio.Button>
                      );
                    })}
                  </Radio.Group>
                </div>
              </div>
            </Col>
          </Row>
          {externalDmrConfig?.['dmrSearchRecycleShow'] === true && (
            <Row gutter={[10, 10]}>
              <Col span={12}>
                <div className={'search-item'}>
                  <label>
                    {dmrSearchParamLabels?.['OverSignInDay'] || '回收超过'}
                  </label>
                  <div className={'search-item-content-container'}>
                    <InputNumber
                      size={'small'}
                      min={0}
                      keyboard={false}
                      controls={false}
                      precision={0}
                      value={dmrSearchParams?.OverSignInDay}
                      onChange={(event) => {
                        setDmrSearchParams({
                          ...dmrSearchParams,
                          OverSignInDay: event,
                        });
                      }}
                    />
                    <label className={'suffix'}>天</label>
                  </div>
                </div>
              </Col>
            </Row>
          )}
          <Row gutter={[10, 10]}>
            <Col
              span={24}
              style={{ display: 'flex', justifyContent: 'flex-end' }}
            >
              {/*TODO debounce*/}
              <Button
                block
                className={'search-btn'}
                type="primary"
                size={'small'}
                onClick={() => {
                  setBackPagination({
                    ...backPagination,
                    current: 1,
                  });
                  dmrSearchReqWithDebounce(
                    1,
                    backPagination?.pageSize,
                    searchTableSorter,
                  );
                }}
              >
                <span>查</span>
                <span style={{ marginLeft: 10 }}>询</span>
              </Button>
            </Col>
          </Row>
        </div>

        {/* <div className={'dmr-cards-summary-container'}>
        <Row gutter={[10, 5]}>
          {cardSummaries?.map((item) => {
            return (
              <Col xs={24} sm={12} md={12} lg={6} xl={6}>
                <DmrCardSummaryItemCard
                  label={item?.label}
                  itemKey={item?.key}
                  count={dmrCardsSummary?.[item?.key]}
                  onClick={() => {
                    setDmrSearchParams({
                      ...dmrSearchParams,
                      CustomRegisterStatus: item?.status,
                    });
                  }}
                />
              </Col>
            );
          })}
        </Row>
      </div> */}

        <Card
          size={'small'}
          className={'dmr-search-table'}
          bordered={false}
          extra={
            <TableColumnEditButton
              columnInterfaceUrl={'Api/Dmr/DmrDataQuery/GetPendingCards'}
              onTableRowSaveSuccess={(columns) => {
                setDmrTableColumns(
                  tableColumnBaseProcessor(dmrSearchColumns, columns),
                );
              }}
            />
          }
        >
          <UniTable
            id={'dmr-search-table'}
            rowKey={'id'}
            rowClassName={(record, index) =>
              record.PatNo === selectedDmrSearchItem?.PatNo
                ? 'row-selected'
                : ''
            }
            size="small"
            scroll={{
              x: 'max-content',
              y: 420,
            }}
            columns={dmrTableColumns?.map((item) => {
              if (!item['width']) {
                item['width'] = 100;
              }
              return item;
            })}
            dataSource={dmrTableDataSource}
            dictionaryData={globalState?.dictData}
            clickable={true}
            loading={dmrSearchLoading}
            pagination={backPagination as any}
            onChange={backTableOnChange}
          />
        </Card>
      </div>
    );
  };

  const renderHasCheckedDmrContent = () => {
    return (
      <UniTable
        id={'dmr-search-table'}
        rowKey={'id'}
        rowClassName={(record, index) =>
          record.PatNo === selectedCheckedDmrSearchItem?.PatNo
            ? 'row-selected'
            : ''
        }
        size="small"
        scroll={{
          x: 'max-content',
          y: document.getElementById('dmr-root-container')?.offsetHeight - 160,
        }}
        columns={dmrTableColumns?.map((item) => {
          if (!item['width']) {
            item['width'] = 100;
          }
          return item;
        })}
        dataSource={dmrCheckedTableDataSource}
        dictionaryData={globalState?.dictData}
        clickable={true}
        loading={dmrCheckedSearchLoading}
        pagination={checkedBackPagination as any}
        onChange={dmrCheckedBackTableOnChange}
        onRow={(record, index) => {
          return {
            onClick: async (event) => {
              // TODO 判定是不是改了东西
              if (props?.onDmrCardChangeInterceptor) {
                let result = await props?.onDmrCardChangeInterceptor();
                if (result) {
                  return;
                }
              }

              setSelectedCheckedDmrSearchItem(record);
              if (record?.HisId) {
                let dmrParam = {
                  hisId: record?.HisId,
                };
                history.replace(
                  `${location?.pathname}?${qs.stringify(dmrParam)}`,
                );
                Emitter.emit(
                  EventConstant.DMR_SEARCH_TABLE_HISIDS,
                  dmrCheckedTableDataSource
                    ?.filter((item) => item?.HisId)
                    ?.map((item) => item?.HisId),
                );
              } else {
                message.error('HisId不存在');
              }
            },
            // ...(restProps?.onRow || {}),
          };
        }}
      />
    );
  };

  useEffect(() => {
    if (props?.visible === true) {
      if (activeKey === 'DmrChecked') {
        setCheckedBackPagination({
          ...checkedBackPagination,
          current: 1,
        });
        dmrCheckedSearchReq(1, checkedBackPagination?.pageSize);
      }
    }
  }, [props?.visible]);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_SEARCH_REFRESH_DATA, (status) => {
      if (status == true && dmrTableDataSource?.length !== 0) {
        dmrSearchReq(
          backPagination.current,
          backPagination.pageSize,
          searchTableSorter,
        );
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_SEARCH_REFRESH_DATA);
    };
  }, [props?.visible, backPagination, dmrTableDataSource, searchTableSorter]);

  const tabs = [
    {
      key: 'DmrSearch',
      label: '查询病案',
      children: renderDmrSearchContent(),
    },
    // 当启用权限控制且用户角色为 Medicare 时，不显示"今日已登记"tab
    ...(!isMedicareWithPermission(userRoleLogic.userRole)
      ? [
          {
            key: 'DmrChecked',
            label: (
              <span className={'title-badge-container'}>
                今日已登记{' '}
                <Badge
                  style={{ marginLeft: 5 }}
                  count={checkedTotal ?? 0}
                  overflowCount={99}
                />
              </span>
            ),
            children: renderHasCheckedDmrContent(),
          },
        ]
      : []),
  ];

  return (
    <div className={'dmr-search-container'}>
      <Tabs
        defaultActiveKey={tabs?.at(0)?.key}
        onChange={(activeKey) => {
          if (activeKey === 'DmrChecked') {
            setCheckedBackPagination({
              ...checkedBackPagination,
              current: 1,
            });

            dmrCheckedSearchReq(1, checkedBackPagination?.pageSize);
          }
          setActiveKey(activeKey);
        }}
        items={tabs}
        tabBarExtraContent={{
          right: (
            <DoubleLeftOutlined
              style={{ cursor: 'pointer' }}
              onClick={() => {
                props?.onRightArrowClick(false);
              }}
            />
          ),
        }}
      />
    </div>
  );
};

interface DmrCardSummaryItemProps {
  className?: string;
  label?: string;
  count?: number;
  itemKey?: string;
  onClick?: () => any;
}

const DmrCardSummaryItemCard = (props: DmrCardSummaryItemProps) => {
  return (
    <div className={`dmr-cards-summary-item-container`}>
      <div className={'label'}>{props?.label}</div>
      <div className={'value'}>
        {props?.count === null || props?.count === undefined
          ? '-'
          : props?.count}
      </div>
    </div>
  );
};
