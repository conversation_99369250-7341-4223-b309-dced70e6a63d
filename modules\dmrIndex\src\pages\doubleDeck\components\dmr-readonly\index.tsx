import './index.less';
import React, { useEffect, useState } from 'react';
import { ids } from '@uni/utils/src/id-inject';
import { isEmptyValues } from '@uni/utils/src/utils';
import { RequireMark } from '@uni/grid/src/components/require-mark';
import { dynamicComponentsMap } from '@/pages/doubleDeck/components/dmr-readonly/dynamicComponents';
import { Col, Form, message, Row, Spin } from 'antd';
import { commonProps } from '@uni/grid/src/common/field-common';
import {
  mockHeaderLayouts,
  mockLayouts,
} from '@/pages/doubleDeck/components/dmr-readonly/layouts';
import groupBy from 'lodash/groupBy';
import { defaultFieldValue, ROW_HEIGHT } from '@/pages/dmr';
import {
  customLabelMap,
  formNoBorder,
  spanMap,
} from '@/pages/doubleDeck/components/span-map';
import chunk from 'lodash/chunk';
import { Gutter } from 'antd/lib/grid/row';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import qs from 'qs';
import { getCardInfoV2, getDmrNotes } from '@/pages/dmr/network/get';
import mergeWith from 'lodash/mergeWith';
import { CardBundleInfo } from '@/pages/dmr/network/interfaces';
import { DmrProcessor } from '@/pages/dmr/processors/processors';

const context = {
  externalConfig: (window as any).externalConfig?.['dmr'],
  dynamicComponentsMap: dynamicComponentsMap,
};

// const requiredLabelHighlight =
//   context?.externalConfig?.requiredLabelHighlight ?? {};

interface DmrReadonlyProps {
  containerRef: any;
  contentLayouts: any;
  headerLayouts: any;
  hisId: string;
  dmrProcessorInstance: any;
  getInterfaceUrl?: string;
  dmrContainerRef: any;
}

const requiredLabelHighlight = {};

const DmrReadonly = (props: DmrReadonlyProps) => {
  const [formCardInfo, setFormCardInfo] = useState(undefined);

  const [diffResult, setDiffResult] = useState(undefined);

  const [originDmrCardInfo, setOriginDmrCardInfo] =
    useState<CardBundleInfo>(null);

  const [form] = Form.useForm();

  const [headerLayouts, setHeaderLayouts] = useState({});
  const [contentLayouts, setContentLayouts] = useState({});

  const [currentDmrHisId, setCurrentDmrHisId] = useState('');

  const [dmrCardInfoLoading, setDmrCardInfoLoading] = useState(false);

  useEffect(() => {
    if (!isEmptyValues(props?.headerLayouts) && isEmptyValues(headerLayouts)) {
      setHeaderLayouts(groupBy(props?.headerLayouts, 'y'));
    }

    if (
      !isEmptyValues(props?.contentLayouts) &&
      isEmptyValues(contentLayouts)
    ) {
      setContentLayouts(groupBy(props?.contentLayouts, 'y'));
    }
  }, []);

  useEffect(() => {
    if (
      !isEmptyValues(props?.contentLayouts) &&
      isEmptyValues(contentLayouts)
    ) {
      setContentLayouts(groupBy(props?.contentLayouts, 'y'));
    }
  }, [props?.contentLayouts]);

  useEffect(() => {
    if (!isEmptyValues(props?.headerLayouts) && isEmptyValues(headerLayouts)) {
      setHeaderLayouts(groupBy(props?.headerLayouts, 'y'));
    }
  }, [props?.headerLayouts]);

  useEffect(() => {
    if (
      isEmptyValues(props?.hisId) ||
      isEmptyValues(props?.contentLayouts) ||
      isEmptyValues(props?.headerLayouts)
    ) {
      return;
    }

    if (props?.hisId !== currentDmrHisId) {
      getDmrCardInfo(props?.hisId);
    }
  }, [props]);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      getReadonlyCardFormInfo: () => {
        return formCardInfo;
      },
      updateDiffResult: (key, diffResult) => {
        setDiffResult({
          ...diffResult,
          [key]: diffResult,
        });
      },
    };
  });

  const getDmrCardInfo = async (hisId: string, instantAudit?: boolean) => {
    if (hisId) {
      setDmrCardInfoLoading(true);

      let { formFieldValue, cardBundleInfo } = await getCardInfoV2(
        hisId,
        props?.dmrProcessorInstance,
        props?.getInterfaceUrl ?? 'Api/Dmr/DmrCardBundle/GetPreCard',
      );

      let formCardInfo = mergeWith(
        {},
        defaultFieldValue,
        formFieldValue,
        (objValue, srcValue, key) => {
          if (srcValue !== null && srcValue !== undefined) {
            return srcValue;
          }

          return objValue;
        },
      );

      // form.resetFields();
      // reset form table fields
      // resetTableData();

      form.setFieldsValue(formCardInfo);
      setFormCardInfo(formCardInfo);
      setCurrentDmrHisId(hisId);
      setOriginDmrCardInfo(cardBundleInfo);
      setDmrCardInfoLoading(false);

      setTimeout(() => {
        props?.dmrContainerRef?.current?.showDiff(formCardInfo);
      }, 500);
    } else {
      message.error('HisId缺失，请检查');
    }
  };

  const currentRowGutterByRowSpanTotal = (rowLayouts: any[]) => {
    let total = 0;
    rowLayouts.forEach((item) => {
      total += spanMap?.[item.data.key] ?? 6;
    });

    if (total > 24) {
      return [16, 0] as [Gutter, Gutter];
    }

    return [16, 16] as [Gutter, Gutter];
  };

  const renderDmrRow = (key: string, layouts: any) => {
    switch (layouts[key]?.length) {
      case 1:
        if (layouts[key]?.at(0)?.data?.component === 'SectionBottom') {
          return null;
        }

        if (layouts[key]?.at(0)?.data?.component === 'SectionHeader') {
          return (
            <Row gutter={[16, 16]} style={{ minHeight: ROW_HEIGHT }}>
              {layouts[key]?.map((item) => {
                return (
                  <Col span={24} style={{ padding: 0 }}>
                    <DmrReadonlyItem
                      form={form}
                      componentId={item.data.key}
                      data={item.data}
                    />
                  </Col>
                );
              })}
            </Row>
          );
        }

        return (
          <Row
            className={
              layouts[key]?.at(0)?.data?.key?.includes('Table')
                ? 'row-item-table'
                : 'row-item'
            }
            gutter={[16, 16]}
            style={{ minHeight: ROW_HEIGHT }}
          >
            {layouts[key]?.map((item) => {
              return (
                <Col span={24}>
                  <DmrReadonlyItem
                    form={form}
                    componentId={item.data.key}
                    data={item.data}
                  />
                </Col>
              );
            })}
          </Row>
        );
      case 2:
        let remainsSpan = 0;
        return (
          <Row
            className={'row-item'}
            gutter={[16, 16]}
            style={{ minHeight: ROW_HEIGHT }}
          >
            {layouts[key]?.map((item, index) => {
              let span = spanMap?.[item.data.key] ?? 6;
              if (index === 0) {
                remainsSpan = 24 - (spanMap?.[item.data.key] ?? 6);
              } else {
                span = (spanMap?.[item.data.key] ?? remainsSpan) || 6;
              }
              return (
                <Col span={span}>
                  <DmrReadonlyItem
                    form={form}
                    componentId={item.data.key}
                    data={item.data}
                  />
                </Col>
              );
            })}
          </Row>
        );
      case 3:
        return (
          <Row
            className={'row-item'}
            gutter={[16, 16]}
            style={{ minHeight: ROW_HEIGHT }}
          >
            {layouts[key]?.map((item) => {
              return (
                <Col span={spanMap?.[item.data.key] ?? 6}>
                  <DmrReadonlyItem
                    form={form}
                    componentId={item.data.key}
                    data={item.data}
                  />
                </Col>
              );
            })}
          </Row>
        );
      case 4:
      case 5:
      case 6:
        let chunkSize = layouts[key]?.length;
        let totalSpan = 0;
        layouts[key]?.forEach(
          (item) => (totalSpan += spanMap?.[item.data.key] ?? 6),
        );

        if (totalSpan > 24) {
          chunkSize =
            layouts[key]?.length === 4 ? 2 : layouts[key]?.length === 5 ? 3 : 4;
        }

        let chunkRows = chunk(layouts[key], chunkSize);
        return (
          <>
            {chunkRows?.map((chunkItem) => {
              return (
                <Row
                  className={'row-item'}
                  gutter={[16, 16]}
                  style={{ minHeight: ROW_HEIGHT }}
                >
                  {chunkItem?.map((item) => {
                    return (
                      <Col span={spanMap?.[item.data.key] ?? 6}>
                        <DmrReadonlyItem
                          form={form}
                          componentId={item.data.key}
                          data={item.data}
                        />
                      </Col>
                    );
                  })}
                </Row>
              );
            })}
          </>
        );
      default:
        return (
          <Row className={'row-item'} gutter={[16, 16]}>
            {layouts[key]?.map((item) => {
              return (
                <Col span={spanMap?.[item.data.key] ?? 6}>
                  <DmrReadonlyItem
                    form={form}
                    componentId={item.data.key}
                    data={item.data}
                  />
                </Col>
              );
            })}
          </Row>
        );
    }
  };

  return (
    <div
      id={'dmr-readonly-container'}
      className={'dmr-readonly-container'}
      style={{ height: 'calc(100% - 10px)' }}
    >
      <Spin spinning={dmrCardInfoLoading} style={{ height: '100%' }}>
        <Form form={form} className={'form-container'}>
          <div
            id={'readonly-header-container'}
            className={'readonly-header-container'}
          >
            <span className={'header-label'}>医生病案首页</span>
            {Object.keys(headerLayouts)?.map((key) => {
              return renderDmrRow(key, headerLayouts);
            })}
          </div>

          <div
            style={{
              flex: 1,
              overflowX: 'hidden',
              overflowY: 'auto',
              position: 'relative',
            }}
          >
            <div
              id={'readonly-content-container'}
              className={
                'readonly-content-container grid-stack gs-16 gs-id-0 grid-stack-static grid-stack-animate'
              }
            >
              {Object.keys(contentLayouts)?.map((key) => {
                return renderDmrRow(key, contentLayouts);
              })}
            </div>
          </div>
        </Form>
      </Spin>
    </div>
  );
};

const DmrReadonlyItem = (props: any) => {
  const DynamicComponent = props.data?.component
    ? (context?.dynamicComponentsMap[
        `Uni${props.data?.component}`
      ] as React.FC<any>)
    : undefined;

  let componentProps = {
    ...props?.data?.props,
    ...(commonProps[props.data?.component] || {}),
  };

  if (props?.data?.component === 'SectionBottom') {
    return null;
  }

  if (props?.data?.component === 'SectionHeader') {
    return (
      <div
        id={ids(`formItem#${props?.componentId}`, 'Readonly')}
        className={'flex-row-center'}
        style={{ height: '100%' }}
      >
        <DynamicComponent
          form={props?.form}
          id={`Readonly-${props.data?.component}#${props?.componentId}`}
          componentId={props?.componentId}
          {...componentProps}
        />
      </div>
    );
  }

  return (
    <>
      {DynamicComponent ? (
        <div
          id={ids(`formItem#${props?.componentId}`, 'Readonly')}
          className={'flex-row-center'}
          style={{ height: '100%' }}
        >
          {props.data?.prefix && (
            <span
              style={
                props?.data?.required && !isEmptyValues(requiredLabelHighlight)
                  ? requiredLabelHighlight
                  : {}
              }
              className={'prefix'}
            >
              {isEmptyValues(requiredLabelHighlight) &&
                props?.data?.required && <RequireMark />}
              {customLabelMap?.[props?.componentId] ??
                props.data?.prefix?.toString()}
            </span>
          )}

          <Form.Item
            className={
              formNoBorder?.includes(props?.componentId) ? 'border-none' : ''
            }
            name={props?.componentId}
          >
            {
              <DynamicComponent
                style={{ padding: '0px 5px' }}
                form={props?.form}
                id={`Readonly-${props.data?.component}#${props?.componentId}`}
                componentId={props?.componentId}
                {...componentProps}
              />
            }
          </Form.Item>

          {props.data?.suffix && (
            <span className={'suffix'}>{props.data?.suffix}</span>
          )}
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default React.memo(DmrReadonly);
